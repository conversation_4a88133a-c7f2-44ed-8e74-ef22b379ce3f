package servicos.impl.vindi;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.enumeradores.TipoTelefoneEnum;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.TelefoneVO;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Pessoa;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Estulano on 07/07/2023.
 */
public class SincronizarDadosVindiService extends AbstractCobrancaOnlineServiceComum {

    public SincronizarDadosVindiService(Connection con) throws Exception {
        super(con);
    }

    public void processarDados() throws Exception {
        Pessoa pessoaDAO = null;
        AutorizacaoCobrancaCliente autorizacaoDAO = null;

        try {
            Uteis.logarDebug("##Processo SincronizarDadosVindiService - INÍCIO");
            pessoaDAO = new Pessoa(getCon());
            autorizacaoDAO = new AutorizacaoCobrancaCliente(getCon());

            String sql = "SELECT * FROM pessoa WHERE idvindi IS NOT NULL OR idvindi <> 0";
            List<PessoaVO> pessoas = pessoaDAO.consultar(sql, Uteis.NIVELMONTARDADOS_PESSOA_PACTO_PAY);
            Uteis.logarDebug("##Processo SincronizarDadosVindiService - Encontrei " + pessoas.size() + " alunos com IDVindi para tentar atualizar os dados pessoais");

            if (UteisValidacao.emptyList(pessoas)) {
                throw new Exception("Nenhuma pessoa com IDVindi foi encontrada para atualizar");
            }

            int i = 0;
            for (PessoaVO pessoa : pessoas) {
                try {
                    i++;
                    Uteis.logarDebug("##Processo SincronizarDadosVindiService - processando " + i + "/" + pessoas.size() + " | IDVindi " + pessoa.getIdVindi());
                    AutorizacaoCobrancaClienteVO aut = autorizacaoDAO.consultarPorPessoaTipoConvenio(pessoa.getCodigo(), TipoConvenioCobrancaEnum.DCC_VINDI, true, Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS).get(0);
                    JSONObject pessoaJSON = criarPessoaJSON(pessoa);
                    JSONObject resposta = new JSONObject(
                            executarRequestVindi(pessoaJSON.toString(), "customers/" + pessoa.getIdVindi(), ExecuteRequestHttpService.METODO_PUT, aut.getConvenio().getCodigoAutenticacao01())
                    );
                    if (resposta != null && !UteisValidacao.emptyString(resposta.toString()) && !resposta.has("errors")) {
                        Uteis.logarDebug("##Processo SincronizarDadosVindiService - IDVindi " + pessoa.getIdVindi() + " atualizado com sucesso!");
                    }
                } catch (Exception ex) {
                    Uteis.logarDebug("##Processo SincronizarDadosVindiService - Não consegui atualizar os dados do aluno IDVindi " + pessoa.getIdVindi());
                }

            }
        } finally {
            pessoaDAO = null;
            autorizacaoDAO = null;
        }

    }

    private String executarRequestVindi(String parametros, String metodo, String metodoHTTP, String chaveAPI) throws
            Exception {
        String path = "https://app.vindi.com.br:443/api/v1/" + metodo;
        Map<String, String> maps = new HashMap<String, String>();
        maps.put("Content-Type", "application/json");
        maps.put("Authorization", "Basic " + new String(new Base64().encode(chaveAPI.getBytes())));

        // Para não permitir que a quantidade de requisições por segundo seja atingida.
        // Para mais detalhes veja: https://atendimento.vindi.com.br/hc/pt-br/articles/204075034-Qual-o-limite-de-requisi%C3%A7%C3%B5es-da-plataforma-Vindi-
        Integer limiteRequisicoesPorMinuto = 120;
        Thread.sleep((limiteRequisicoesPorMinuto / 60) * 1000);

        return ExecuteRequestHttpService.executeHttpRequest(path, parametros, maps, metodoHTTP, "UTF-8");
    }

    private JSONObject criarPessoaJSON(PessoaVO pessoa) {
        JSONObject pes = new JSONObject();
        pes.put("name", Uteis.retirarAcentuacao(pessoa.getNome()));
        if (!UteisValidacao.emptyString(pessoa.getEmail())) {
            pes.put("email", pessoa.getEmail());
        }
        if (!UteisValidacao.emptyString(pessoa.getCfp())) {
            pes.put("registry_code", pessoa.getCfp());
        }
        JSONArray phones = getTelefonesCelularesPessoa(pessoa);
        if (phones.length() > 0) {
            pes.put("phones", phones);
        }
        return pes;
    }

    private JSONArray getTelefonesCelularesPessoa(PessoaVO pessoa) {
        JSONArray telefones = new JSONArray();
        if (pessoa.getTelefoneVOs() != null && !pessoa.getTelefoneVOs().isEmpty()) {
            for (TelefoneVO telefone : pessoa.getTelefoneVOs()) {
                if (!UteisValidacao.emptyString(telefone.getTipoTelefone()) && telefone.getTipoTelefone().equals(TipoTelefoneEnum.CELULAR.getCodigo())) {
                    telefones.put(criarTelefoneJSON(telefone));
                }
            }
        }
        return telefones;
    }

    private JSONObject criarTelefoneJSON(TelefoneVO telefone) {
        JSONObject tel = new JSONObject();
        tel.put("phone_type", telefone.getTipoTelefone().equals(TipoTelefoneEnum.CELULAR.getCodigo()) ? "mobile" : "landline");
        String numero = telefone.getNumero();
        numero = numero.replaceAll("[()]", "");
        tel.put("number", "55" + numero);
        return tel;
    }
}
