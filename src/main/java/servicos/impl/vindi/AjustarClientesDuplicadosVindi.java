package servicos.impl.vindi;

import br.com.pactosolucoes.oamd.controle.basico.DAO;
import importador.ImportacaoConfigTO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.commons.codec.binary.Base64;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AjustarClientesDuplicadosVindi {

    public static String urlAPI = PropsService.getPropertyValue(PropsService.urlApiVindiProducao);

    public static void main(String[] args) {
        try {

            String chave = "teste";
//            Connection con = DriverManager.getConnection("*************************************************", "postgres", "pactodb");
            Connection con = new DAO().obterConexaoEspecifica(chave);
            Conexao.guardarConexaoForJ2SE(con);

            Integer empresa = 1;
            String chaveAPI = "31ILYezKDeDJO5rLK3otJBXglUpH9A5aA4fdsYC7EnE";
            String nomeFiltrar = "adriana carne";

            processarNovo(nomeFiltrar, empresa, chaveAPI, con);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void processarNovo(String nomeFiltrar, Integer empresa, String chaveAPI, Connection con) throws Exception {

        StringBuilder sql = new StringBuilder();
        sql.append("select  \n");
        sql.append("c.matricula, \n");
        sql.append("p.codigo, \n");
        sql.append("p.idvindi, \n");
        sql.append("p.cfp as cpf, \n");
        sql.append("p.nome  \n");
        sql.append("from pessoa p \n");
        sql.append("inner join cliente c on c.pessoa = p.codigo \n");
        sql.append("where coalesce(p.cfp,'') <> '' \n");
        sql.append("and p.idvindi is not null \n");
        sql.append("and c.empresa = ").append(empresa).append(" \n");
        if (!UteisValidacao.emptyString(nomeFiltrar)) {
            sql.append("and p.nome ilike '%").append(nomeFiltrar).append("%' \n");
        }
        sql.append("order by p.nome \n");

        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {
            try {
                Integer pessoa = rs.getInt("codigo");
                Integer idvindi = rs.getInt("idvindi");
                String cpf = rs.getString("cpf");
                String nome = rs.getString("nome");
                String matricula = rs.getString("matricula");


                Uteis.logarDebug("Mat. " + matricula + " | " + nome + " | CPF: " + cpf);

                List<ImportacaoVindiDTO> lista = obterCadastrosVindi(cpf, chaveAPI);

                if (lista.size() == 0) {
                    throw new Exception("Nenhum cliente encontrado");
                } else if (lista.size() == 1) {
                    Uteis.logarDebug("Somente 1 cliente encontrado....");
                    continue;
                }

                String statusAtual = "";
                for (ImportacaoVindiDTO vindiDTO : lista) {
                    if (vindiDTO.getIdVindi().equalsIgnoreCase(idvindi.toString())) {
                        statusAtual = vindiDTO.getStatus();
                        break;
                    }
                }

                ImportacaoVindiDTO atualizar = null;
                for (ImportacaoVindiDTO vindiDTO : lista) {
                    if (vindiDTO.getStatus().equalsIgnoreCase("active")) {
                        atualizar = vindiDTO;
                        break;
                    }
                }

                if (atualizar == null) {
                    for (ImportacaoVindiDTO vindiDTO : lista) {
                        if (vindiDTO.getStatus().equalsIgnoreCase("inactive")) {
                            atualizar = vindiDTO;
                            break;
                        }
                    }
                }

                if (atualizar == null) {
                    throw new Exception("Nenhum cliente encontrado");
                }

                if (!statusAtual.equalsIgnoreCase(atualizar.getStatus())) {
                    Uteis.logarDebug("Idvindi diferente pessoa: " + pessoa);
                    Uteis.logarDebug("Idvindi anterior: " + idvindi + " | Status: " + statusAtual);
                    Uteis.logarDebug("Idvindi novo: " + atualizar.getIdVindi() + " | Status: " + atualizar.getStatus());


                    String update = "update pessoa set idvindi = " + atualizar.getIdVindi() + " where codigo = " + pessoa;
                    SuperFacadeJDBC.executarUpdate(update, con);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private static List<ImportacaoVindiDTO> obterCadastrosVindi(String cpf, String chaveAPI) throws Exception {
        List<ImportacaoVindiDTO> lista = new ArrayList<>();

        Integer page = 1;
        JSONArray customers;
        int totalProcessado = 0;

        boolean processar = true;
        while (processar) {

            String url = PropsService.getPropertyValue(PropsService.urlApiVindiProducao) + "/customers";
            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Basic " + new String(new Base64().encode(chaveAPI.getBytes())));

            Map<String, String> params = new HashMap<String, String>();
            params.put("perPage", "25");
            params.put("page", String.valueOf(page++));
            params.put("query", "registry_code=" + Uteis.formatarCpfCnpj(cpf, true));

            RequestHttpService requestHttpService = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = requestHttpService.executeRequest(url, headers, params, null, MetodoHttpEnum.GET);

            JSONObject obj = new JSONObject(respostaHttpDTO.getResponse());
            customers = obj.getJSONArray("customers");
            totalProcessado += customers.length();
            for (int i = 0; i < customers.length(); i++) {
                JSONObject json = customers.getJSONObject(i);

                ImportacaoVindiDTO itemDTO = new ImportacaoVindiDTO();
                itemDTO.setIdVindi(json.opt("id").toString());
                itemDTO.setCpfVindi(json.opt("registry_code").toString());
                itemDTO.setNomeVindi(json.optString("name"));
                itemDTO.setStatus(json.optString("status"));
                lista.add(itemDTO);
            }

            Integer totalCadastros = Integer.parseInt(respostaHttpDTO.getHeaders().get("Total"));
            if (totalProcessado >= totalCadastros) {
                processar = false;
            }
        }

        return lista;
    }

    private static void processar(String chaveAPI, Integer empresa, Integer convenioCobranca, Connection con) throws Exception {

        VindiService vindiService = new VindiService(con, empresa, convenioCobranca);

        Map<String, List<ImportacaoVindiDTO>> mapa = new HashMap<>();

        Integer page = 1;
        JSONArray customers;
        int totalProcessado = 0;

        boolean processar = true;
        while (processar) {

            String url = PropsService.getPropertyValue(PropsService.urlApiVindiProducao) + "/customers";
            Map<String, String> headers = new HashMap<String, String>();
            headers.put("Content-Type", "application/json");
            headers.put("Authorization", "Basic " + new String(new Base64().encode(chaveAPI.getBytes())));

            Map<String, String> params = new HashMap<String, String>();
            params.put("perPage", "25");
            params.put("page", String.valueOf(page++));
//            params.put("query", "registry_code:98071068268");

            RequestHttpService requestHttpService = new RequestHttpService();
            RespostaHttpDTO respostaHttpDTO = requestHttpService.executeRequest(url, headers, params, null, MetodoHttpEnum.GET);

            JSONObject obj = new JSONObject(respostaHttpDTO.getResponse());
            customers = obj.getJSONArray("customers");
            totalProcessado += customers.length();
            for (int i = 0; i < customers.length(); i++) {
                JSONObject json = customers.getJSONObject(i);

                ImportacaoVindiDTO itemDTO = new ImportacaoVindiDTO();
                itemDTO.setIdVindi(json.opt("id").toString());
                itemDTO.setCpfVindi(json.opt("registry_code").toString());
                itemDTO.setNomeVindi(json.optString("name"));
                itemDTO.setStatus(json.optString("status"));

                if (UteisValidacao.emptyString(itemDTO.getCpfVindi())) {
                    Uteis.logarDebug("Não tem cpf... " + json.toString());
                    continue;
                }

                List<ImportacaoVindiDTO> lista = mapa.get(itemDTO.getCpfVindi());
                if (lista == null) {
                    lista = new ArrayList<>();
                }
                lista.add(itemDTO);
                mapa.put(itemDTO.getCpfVindi(), lista);
            }

            Integer totalCadastros = Integer.parseInt(respostaHttpDTO.getHeaders().get("Total"));
            if (totalProcessado >= totalCadastros) {
                processar = false;
            }
        }


        List<ImportacaoVindiDTO> listaGeral = new ArrayList<>();
        for (String key : mapa.keySet()) {
            if (key.equalsIgnoreCase("98071068268")) {
                System.out.println("aaaaa");
            }
            listaGeral.addAll(mapa.get(key));
        }

        for (String key : mapa.keySet()) {
            try {

                List<ImportacaoVindiDTO> lista = mapa.get(key);

                Uteis.logarDebug("CPF: " + key + " | Lista: " + lista.size());

                if (key.equalsIgnoreCase("98071068268")) {
                    System.out.println("aaaaa");
                }

                ImportacaoVindiDTO importacaoVindiDTO = null;
                if (lista.size() > 1) {
                    Uteis.logarDebug("Procurar um cliente ativo...");
                    for (ImportacaoVindiDTO vindiDTO : lista) {
                        if (vindiDTO.getStatus().equalsIgnoreCase("active")) {
                            importacaoVindiDTO = vindiDTO;
                            break;
                        }
                    }

                    if (importacaoVindiDTO == null) {
                        for (ImportacaoVindiDTO vindiDTO : lista) {
                            if (vindiDTO.getStatus().equalsIgnoreCase("inactive")) {
                                importacaoVindiDTO = vindiDTO;
                                break;
                            }
                        }
                    }

                } else {
                    importacaoVindiDTO = lista.get(0);
                    continue;
                }

                if (importacaoVindiDTO == null) {
                    throw new Exception("ImportacaoVindiDTO não encontrado | CPF " + key);
                }


                StringBuilder sql = new StringBuilder();
                sql.append("select  \n");
                sql.append("p.codigo, \n");
                sql.append("p.idvindi, \n");
                sql.append("p.cfp as cpf, \n");
                sql.append("p.nome  \n");
                sql.append("from pessoa p \n");
                sql.append("inner join cliente c on c.pessoa = p.codigo \n");
                sql.append("where (p.cfp = '").append(Uteis.formatarCpfCnpj(key, true)).append("' or p.cfp = '").append(Uteis.formatarCpfCnpj(key, false)).append("') \n");
                sql.append("and c.empresa = ").append(empresa).append(" \n");
                sql.append("order by p.nome \n");

                Integer total = SuperFacadeJDBC.contar("SELECT COUNT(*) FROM (" + sql.toString() + ") as a", con);

                if (total != 1) {
                    throw new Exception("Mais de um cliente encontrado: CPF: " + key);
                }

                ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
                if (rs.next()) {

                    Integer pessoa = rs.getInt("codigo");
                    Integer idvindi = rs.getInt("idvindi");
                    String cpf = rs.getString("cpf");
                    String nome = rs.getString("nome");

                    if (importacaoVindiDTO.getIdVindi().equalsIgnoreCase(idvindi.toString())) {

                        String statusAtual = "";
                        for (ImportacaoVindiDTO aaa : listaGeral) {
                            if (aaa.getIdVindi().equalsIgnoreCase(idvindi.toString())) {
                                statusAtual = aaa.getStatus();
                                break;
                            }
                        }


                        if (!statusAtual.equalsIgnoreCase(importacaoVindiDTO.getStatus())) {
                            Uteis.logarDebug("Idvindi diferente pessoa: " + pessoa);
                            Uteis.logarDebug("Idvindi anterior: " + idvindi + " | Status: " + statusAtual);
                            Uteis.logarDebug("Idvindi novo: " + importacaoVindiDTO.getIdVindi() + " | Status: " + importacaoVindiDTO.getStatus());
                        }
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }
}
