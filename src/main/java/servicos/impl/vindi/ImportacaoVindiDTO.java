package servicos.impl.vindi;

public class ImportacaoVindiDTO {

    private String idVindi;
    private String nomeZw;
    private String situacaoZw;
    private String nomeVindi;
    private String cpfVindi;
    private String resultado;
    private String chaveAPI;
    private String status;

    public String getIdVindi() {
        if (idVindi == null) {
            idVindi = "";
        }
        return idVindi;
    }

    public void setIdVindi(String idVindi) {
        this.idVindi = idVindi;
    }

    public String getNomeVindi() {
        if (nomeVindi == null) {
            nomeVindi = "";
        }
        return nomeVindi;
    }

    public void setNomeVindi(String nomeVindi) {
        this.nomeVindi = nomeVindi;
    }

    public String getCpfVindi() {
        if (cpfVindi == null) {
            cpfVindi = "";
        }
        return cpfVindi;
    }

    public void setCpfVindi(String cpfVindi) {
        this.cpfVindi = cpfVindi;
    }

    public String getResultado() {
        if (resultado == null) {
            resultado = "";
        }
        return resultado;
    }

    public void setResultado(String resultado) {
        this.resultado = resultado;
    }

    public String getNomeZw() {
        if (nomeZw == null) {
            nomeZw = "";
        }
        return nomeZw;
    }

    public void setNomeZw(String nomeZw) {
        this.nomeZw = nomeZw;
    }

    public String getSituacaoZw() {
        if (situacaoZw == null) {
            situacaoZw = "";
        }
        return situacaoZw;
    }

    public void setSituacaoZw(String situacaoZw) {
        this.situacaoZw = situacaoZw;
    }

    public String getChaveAPI() {
        if (chaveAPI == null) {
            chaveAPI = "";
        }
        return chaveAPI;
    }

    public void setChaveAPI(String chaveAPI) {
        this.chaveAPI = chaveAPI;
    }

    public String getStatus() {
        if (status == null) {
            status = "";
        }
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
