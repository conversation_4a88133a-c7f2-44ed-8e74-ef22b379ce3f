/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package servicos.impl.vindi;

import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperFacadeJDBC;
import org.apache.commons.codec.binary.Base64;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class ImportarCodigoVindi {

    public static String urlAPI = PropsService.getPropertyValue(PropsService.urlApiVindiProducao);
    public static int cont = 0;
    public static boolean vemDaEVO = true;
    public static int nrjatem = 0;
    public static int nomebate = 0;
    public static int naobate = 0;
    public static boolean compararIdVindiComMatriculaExterna = false;
    public static boolean ignorarNomesDiferentes = false;

    private static String executarRequestVindi(String chave, String parametros, String metodo, String metodoHTTP) throws Exception {
        String path = urlAPI + "/" + metodo;
        Map<String, String> maps = new HashMap<String, String>();
        maps.put("Content-Type", "application/json");
        maps.put("Authorization", "Basic " + new String(new Base64().encode(chave.getBytes())));
        return ExecuteRequestHttpService.executeHttpRequest(path, parametros, maps, metodoHTTP, "UTF-8");
    }

    public static String lerArquivo(String nome) {
        StringBuilder content = new StringBuilder();
        try {
            FileReader arq = new FileReader(nome);
            BufferedReader lerArq = new BufferedReader(arq);
            String linha = "";
            while (linha != null) {
                linha = lerArq.readLine();
                if (linha != null) {
                    content.append(linha);
                }
            }
            arq.close();
        } catch (IOException e) {
            System.err.printf("Erro na abertura do arquivo: %s.\n",
                    e.getMessage());
        }
        return content.toString();
    }

    public static void updatePessoa(Integer idVindi, Integer idExterno, Connection con,
                                    String nome, Integer convenio, ImportacaoVindiDTO itemDTO) throws Exception {
        if (idExterno != null && idExterno > 0) {
            PreparedStatement stmConsulta = con.prepareStatement("select codigo, nome from pessoa where idexterno = ? ");
            stmConsulta.setInt(1, idExterno);
            ResultSet rs = stmConsulta.executeQuery();
            if (rs.next()) {
                adicionarLog(nome + "  --  OK");
                itemDTO.setResultado("Ok");
                PreparedStatement stm = con.prepareStatement("update pessoa set idvindi = ?, dataalteracaovindi = ? where idexterno = ? ");
                stm.setInt(1, idVindi);
                stm.setDate(2, Uteis.getDataJDBC(Calendario.hoje()));
                stm.setInt(3, idExterno);
                stm.execute();
                criarAutorizacao(convenio, rs.getInt("codigo"), con);
            }
        }
    }

    public static void criarAutorizacao(Integer codigoConvenio, Integer pessoa, Connection con) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo from cliente where pessoa = " + pessoa, con);
        if (rs.next()) {
            SuperFacadeJDBC.executarConsulta("insert into autorizacaocobrancacliente "
                    + "(cliente, tipoautorizacao, tipoacobrar, conveniocobranca, ordem)\n"
                    + "values (" + rs.getInt("codigo") + ",1,1," + codigoConvenio + ",1)", con);
        }
    }

    public static boolean temAutorizacao(Integer codigoConvenio, Integer pessoa, Connection con) throws Exception {
        ResultSet rs = SuperFacadeJDBC.criarConsulta("SELECT codigo from cliente where pessoa = " + pessoa, con);
        if (rs.next()) {
            ResultSet rst = SuperFacadeJDBC.criarConsulta("select codigo from autorizacaocobrancacliente where conveniocobranca = " + codigoConvenio
                    + "and cliente = " + rs.getInt("codigo"), con);
            return rst.next();
        }
        return false;
    }

    public static void consultarServico(String chave, Connection con, Integer convenio, List<ImportacaoVindiDTO> lista) throws Exception {
        nrjatem = 0;
        nomebate = 0;
        naobate = 0;
        Integer perPage = 25;
        int page = 1;
//        List<String> alunosFiltrar = listaAlunosFiltrar();
        List<String> alunosFiltrar = new ArrayList<>();
        JSONArray customers;
        do {
            String retorno = executarRequestVindi(chave, null, "customers?per_page=" + perPage + "&page=" + page++, ExecuteRequestHttpService.METODO_GET);
            JSONObject obj = new JSONObject(retorno);
            customers = obj.getJSONArray("customers");
            for (int i = 0; i < customers.length(); i++) {
                JSONObject json = customers.getJSONObject(i);

                ImportacaoVindiDTO itemDTO = new ImportacaoVindiDTO();
                itemDTO.setChaveAPI(chave);
                itemDTO.setIdVindi(json.opt("id").toString());
                itemDTO.setCpfVindi(json.opt("registry_code").toString());
                itemDTO.setNomeVindi(json.optString("name"));

                if (vemDaEVO) {
                    adicionarLog(cont++ + " - " + json.getString("name") + " - " + json.opt("code").toString() + " - " + json.opt("id").toString());
                    updatePessoa(Integer.valueOf(json.opt("id").toString()), Integer.valueOf(json.opt("code").toString()), con,
                            json.getString("name"), convenio, itemDTO);
                } else {
                    if (UteisValidacao.emptyList(alunosFiltrar) || alunosFiltrar.contains(json.getString("name").toLowerCase())) {
                        updatePessoaSemCode(Integer.valueOf(json.opt("id").toString()), (json.opt("registry_code") == null ? "" : json.opt("registry_code").toString()),
                                con, json.getString("name"), convenio, itemDTO);
                    }
                }
                lista.add(itemDTO);
            }
        } while (customers.length() > 0);
        adicionarLog("já tem :" + nrjatem);
        adicionarLog("OK:" + nomebate);
        adicionarLog("Nomes não batem :" + naobate);
    }

    public static void updatePessoaSemCode(Integer idVindi, String cpf, Connection con,
                                           String nome, Integer convenio, ImportacaoVindiDTO itemDTO) throws Exception {

        if (cpf == null || UteisValidacao.emptyString(cpf.trim())) {
            adicionarLog(cont++ + ";" + nome + " Não veio CPF da VINDI");
            return;
        }

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("p.codigo, \n");
        sql.append("p.nome, \n");
        sql.append("(select situacao from cliente where pessoa = p.codigo limit 1) as situacao \n");
        sql.append("from pessoa p where (p.cfp = ? or ");
        if (compararIdVindiComMatriculaExterna) {
            sql.append(" p.codigo in (select pessoa from cliente where matriculaexterna = '").append(idVindi).append("' ) or  ");
        }
        sql.append(" p.codigo in (\n");
        sql.append(" select clifa.pessoa from familiar f\n");
        sql.append(" inner join parentesco p on p.codigo = f.parentesco\n");
        sql.append(" inner join cliente clifa on clifa.codigo = f.cliente\n");
        sql.append(" inner join cliente cli on cli.codigo = f.familiar\n");
        sql.append(" inner join pessoa pes on pes.codigo = cli.pessoa \n");
        sql.append(" where p.descricao in('PAI', 'MÃE') and pes.cfp =  ? ) ) ");
        sql.append(" and p.idvindi is null");

        PreparedStatement stmConsulta = con.prepareStatement(sql.toString());
        stmConsulta.setString(1, Uteis.formatarCpfCnpj(cpf, false));
        stmConsulta.setString(2, Uteis.formatarCpfCnpj(cpf, false));
        ResultSet rs = stmConsulta.executeQuery();

        while (rs.next()) {

            String nomeZW = rs.getString("nome");
            String situacao = rs.getString("situacao");

            itemDTO.setNomeZw(nomeZW);
            itemDTO.setSituacaoZw(situacao);

            if (temAutorizacao(convenio, rs.getInt("codigo"), con)) {
                nrjatem++;
                adicionarLog(cont++ + ";" + nome + " já tem autorização");
                itemDTO.setResultado("Já tem autorização");
            } else if (nome.equalsIgnoreCase(nomeZW) || ignorarNomesDiferentes) {
                nomebate++;
                adicionarLog(cont++ + ";" + Uteis.formatarCpfCnpj(cpf, false) + ";" + nome + ";" + nomeZW + "; OK");
                itemDTO.setResultado("Ok");
                PreparedStatement stm = con.prepareStatement("update pessoa set idvindi = ?, dataalteracaovindi = ? where CODIGO = ? ");
                stm.setInt(1, idVindi);
                stm.setDate(2, Uteis.getDataJDBC(Calendario.hoje()));
                stm.setInt(3, rs.getInt("codigo"));
                stm.execute();
                criarAutorizacao(convenio, rs.getInt("codigo"), con);
            } else {
                naobate++;
                adicionarLog(cont++ + ";" + Uteis.formatarCpfCnpj(cpf, false) + ";" + nome + ";" + nomeZW + "; NOMES NÃO BATEM");
                itemDTO.setResultado("NOMES NÃO BATEM");
            }
        }

        if (UteisValidacao.emptyString(itemDTO.getNomeZw())) {
            itemDTO.setNomeZw("NÃO ENCONTROU NO ZW");
        }
    }

    public static List<String> listaAlunosFiltrar() {
        List<String> filtros = Arrays.asList(new String[]{
                "ALINNE DE CARVALHO CALDAS".toLowerCase(),
                "DIOGO HENRIQUE ALVES PRETTO".toLowerCase(),
                "Giselle Cristina Da Conceição Vitoria".toLowerCase(),
                "GLAUBER DA SILVA SOUZA".toLowerCase(),
                "ALDENIRA SOARES FERREIRA DOS SANTOS".toLowerCase(),
                "GISELLE CRISTINA DA CONCEIÇÃO VITORIA".toLowerCase(),
                "DIOGO HENRIQUE ALVES PRETTO".toLowerCase(),
                "NÁGILA DE OLIVEIRA SANTOS SILVA".toLowerCase(),
                "RANNIEÊ MASSAO DUARTE UEDA".toLowerCase(),
                "Veronica De Almeida Gonçalves Viana".toLowerCase(),
                "VANESSA LIMA FARINELI".toLowerCase(),
                "VICTOR COSTA MACHADO".toLowerCase(),
                "PAMELA MUNIZ DA SILVA OLIVEIRA".toLowerCase(),
                "VANESSA LIMA FARINELI".toLowerCase(),
                "ALDENIRA SOARES FERREIRA DOS SANTOS".toLowerCase(),
                "WILLIAN VALENTE RIBEIRO".toLowerCase(),
                "Kayky De Souza Garcia Da Rosa".toLowerCase()});
        return filtros;
    }


    public static void consultarServicoPeloNome(String chave, Connection con, Integer convenio, List<ImportacaoVindiDTO> lista) throws Exception {
        Integer perPage = 25;
        int page = 1;

        Integer totalProcessado = 0;
        Integer encontrou = 0;

        JSONArray customers;
        do {
            String retorno = executarRequestVindi(chave, null, "customers?per_page=" + perPage + "&page=" + page++, ExecuteRequestHttpService.METODO_GET);
            JSONObject obj = new JSONObject(retorno);
            customers = obj.getJSONArray("customers");
            for (int i = 0; i < customers.length(); i++) {
                JSONObject json = customers.getJSONObject(i);

                //comparar pelo nome do aluno.....

                Integer id = json.optInt("id");
                String nome = json.optString("name");
                String cpf = json.optString("registry_code");

                ++totalProcessado;

                if (UteisValidacao.emptyNumber(id)) {
                    adicionarLog("Id vazio.. ");
                    continue;
                }

                boolean alterou = updatePessoaNomeOuCPF(id, cpf, nome, convenio, con);
                if (alterou) {
                    ++encontrou;
                }
            }
        } while (customers.length() > 0);

        adicionarLog("Total: " + totalProcessado);
        adicionarLog("OK:" + encontrou);
    }

    public static boolean updatePessoaNomeOuCPF(Integer idVindi, String cpf, String nome, Integer convenio, Connection con) throws Exception {

        String cpfNumeros = Uteis.formatarCpfCnpj(cpf, true);
        String cpfFormatado = Uteis.formatarCpfCnpj(cpf, false);

        StringBuilder sql = new StringBuilder();
        sql.append("select \n");
        sql.append("p.codigo, \n");
        sql.append("p.nome \n");
        sql.append("from pessoa p \n");
        sql.append("inner join cliente cl on cl.pessoa = p.codigo \n");
        sql.append("where p.idvindi is null \n");
        sql.append("and cl.empresa in (select empresa from conveniocobrancaempresa where conveniocobranca = ").append(convenio).append(") \n");

        if (!UteisValidacao.emptyString(cpfNumeros)) {
            sql.append("and (p.cfp = '").append(cpfNumeros).append("' or p.cfp = '").append(cpfFormatado).append("') \n");
        } else {
            sql.append("and p.nome ilike '").append(nome).append("' \n");
        }

        boolean encontrou = false;
        ResultSet rs = SuperFacadeJDBC.criarConsulta(sql.toString(), con);
        while (rs.next()) {

            Integer codigoPessoa = rs.getInt("codigo");
            String nomePessoa = rs.getString("nome");

            boolean temAutorizacao = temAutorizacao(convenio, codigoPessoa, con);

            PreparedStatement stm = con.prepareStatement("update pessoa set idvindi = ?, dataalteracaovindi = ? where codigo = ? ");
            stm.setInt(1, idVindi);
            stm.setTimestamp(2, Uteis.getDataJDBCTimestamp(Calendario.hoje()));
            stm.setInt(3, codigoPessoa);
            stm.execute();

            encontrou = true;

            if (temAutorizacao) {
                adicionarLog("Pessoa " + codigoPessoa + " | " + nomePessoa + " | Já tem autorização");
            } else {
                criarAutorizacao(convenio, codigoPessoa, con);
                adicionarLog("Pessoa " + codigoPessoa + " | " + nomePessoa + " | Incluiu autorização");
            }
        }

        return encontrou;
    }

    private static void adicionarLog(String msg) {
        String s = "[DEBUG] " + Calendario.hoje() + " --> " + msg;
        System.out.println(s);
    }

    private static void gerarArquivoExcel(String banco, List<ImportacaoVindiDTO> lista) {
        try {

            if (UteisValidacao.emptyList(lista)) {
                throw new Exception("Lista de importação vazia");
            }

            // Criar arquivo excel
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook();

            //lista
            gerarSheetGeral(hssfWorkbook, lista);

            //salvar arquivo
            File file = new File(PropsService.getPropertyValue(PropsService.diretorioArquivos) + File.separator + "ImportacaoIdVindi -- " + banco + " -- " + Calendario.hoje().getTime() + ".xls");

            FileOutputStream out = new FileOutputStream(file);
            hssfWorkbook.write(out);
            out.close();
            hssfWorkbook.close();

            adicionarLog("#####################################");
            adicionarLog("#####################################");
            adicionarLog("########## ARQUIVO EXCEL ############");
            adicionarLog(file.getAbsolutePath());
            adicionarLog("#####################################");
            adicionarLog("#####################################");
            adicionarLog("#####################################");

        } catch (Exception ex) {
            ex.printStackTrace();
            adicionarLog("#####################################");
            adicionarLog("########## ERRO GERAR EXCEL #########");
            adicionarLog(ex.getMessage());
            adicionarLog("#####################################");
            adicionarLog("#####################################");
        }
    }

    private static void gerarSheetGeral(HSSFWorkbook hssfWorkbook, List<ImportacaoVindiDTO> lista) {

        HSSFSheet sheet = hssfWorkbook.createSheet("IMPORTACAO VINDI");

        int rownum = 0;
        int cellnum = 0;
        Row row;

        // Definindo alguns padroes de layout
        sheet.setDefaultColumnWidth(20);
        sheet.setDefaultRowHeight((short) 300);

        row = sheet.createRow(rownum++);
        String[] colunas = new String[]{"IdVindi", "Nome ZillyonWeb", "Situação ZillyonWeb", "Nome Vindi", "CPF Vindi", "Resultado", "Chave API"};
        for (String coluna : colunas) {
            cabecalho(hssfWorkbook, row, cellnum++, coluna);
        }

        for (ImportacaoVindiDTO item : lista) {

            row = sheet.createRow(rownum++);
            cellnum = 0;

            criarCelula(hssfWorkbook, cellnum++, row, item.getIdVindi());
            criarCelula(hssfWorkbook, cellnum++, row, item.getNomeZw());
            criarCelula(hssfWorkbook, cellnum++, row, item.getSituacaoZw());
            criarCelula(hssfWorkbook, cellnum++, row, item.getNomeVindi());
            criarCelula(hssfWorkbook, cellnum++, row, item.getCpfVindi());
            criarCelula(hssfWorkbook, cellnum++, row, item.getResultado());
            criarCelula(hssfWorkbook, cellnum++, row, item.getChaveAPI());
        }
    }

    private static void setValorCelula(Cell cell, Object valor) {
        if (valor instanceof String) {
            cell.setCellType(CellType.STRING);
            cell.setCellValue((String) valor);
        } else if (valor instanceof Integer) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue((Integer) valor);
        } else if (valor instanceof Double) {
            cell.setCellType(CellType.NUMERIC);
            cell.setCellValue((Double) valor);
        } else if (valor instanceof Date) {
            cell.setCellValue((Date) valor);
        } else if (valor instanceof Boolean) {
            cell.setCellType(CellType.BOOLEAN);
            cell.setCellValue((Boolean) valor);
        } else {
            cell.setCellValue(valor == null ? "" : valor.toString());
        }
    }

    private static Cell cabecalho(HSSFWorkbook hssfWorkbook, Row row, int cellnum, String textoCelula) {
        CellStyle cellStyle = hssfWorkbook.createCellStyle();
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        Cell cell = row.createCell(cellnum++);
        cell.setCellStyle(cellStyle);
        cell.setCellValue(textoCelula);
        return cell;
    }

    private static void criarCelula(HSSFWorkbook hssfWorkbook, int numeroCelula, Row row, Object valor) {
        Cell cell = row.createCell(numeroCelula);
        setValorCelula(cell, valor);
    }

    public static void main(String... args) {
        List<ImportacaoVindiDTO> lista = new ArrayList<>();
        String nomeBanco = "";
        try {
            Connection con = DriverManager.getConnection("**************************************", "postgres", "pactodb");
            nomeBanco = con.getCatalog();

            //Chave API Vindi e código do convênio
            Map<String, Integer> mapaConvenio = new HashMap<>();
            mapaConvenio.put("rm-sYxDbH62VM7IEFH9chcbswUCxyvAAtY89sQ_JhT0", 3);


            //buscar pelo nome do Aluno ou CPF caso tenha
            boolean compararPeloNome = false;
            if (compararPeloNome) {

                for (String k : mapaConvenio.keySet()) {
                    consultarServicoPeloNome(k, con, mapaConvenio.get(k), lista);
                }

            } else {

                ignorarNomesDiferentes = false;
                vemDaEVO = false;
                cont = 1;
                compararIdVindiComMatriculaExterna = true;
                for (String k : mapaConvenio.keySet()) {
                    consultarServico(k, con, mapaConvenio.get(k), lista);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            gerarArquivoExcel(nomeBanco, lista);
        }
    }
}
