package servicos.impl.login;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UsuarioGeralDTO {

    private String id;
    private String nome;
    private String fotokey;
    private String idioma;
    private String email;
    private String telefone;
    private String senha; //necessario apenas na primeira inclusão
    private List<DadosAcessoDTO> dadosAcesso;
    private TokenSolicitacaoDTO solicitacao; //informações que quem solicitou

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getFotokey() {
        return fotokey;
    }

    public void setFotokey(String fotokey) {
        this.fotokey = fotokey;
    }

    public String getIdioma() {
        return idioma;
    }

    public void setIdioma(String idioma) {
        this.idioma = idioma;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public List<DadosAcessoDTO> getDadosAcesso() {
        return dadosAcesso;
    }

    public void setDadosAcesso(List<DadosAcessoDTO> dadosAcesso) {
        this.dadosAcesso = dadosAcesso;
    }

    public TokenSolicitacaoDTO getSolicitacao() {
        return solicitacao;
    }

    public void setSolicitacao(TokenSolicitacaoDTO solicitacao) {
        this.solicitacao = solicitacao;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
