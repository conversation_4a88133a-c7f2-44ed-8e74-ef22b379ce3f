package servicos.impl.login;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class SolicitacaoLoginV1DTO {

    private String chave;
    private String username; //username do usuario do zw
    private Integer empresa; //codigo da empresa
    private Integer usuariozw; //codigo do usuario do zw
    private Integer usuariotw; //codigo do usuario do tw
    private String email;
    private String telefone;
    private String nome; //nome do usuario
    private Boolean codigoViaEmail; //codigo do sms enviado
    private TokenSolicitacaoDTO solicitacao;

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public Integer getUsuariozw() {
        return usuariozw;
    }

    public void setUsuariozw(Integer usuariozw) {
        this.usuariozw = usuariozw;
    }

    public Integer getUsuariotw() {
        return usuariotw;
    }

    public void setUsuariotw(Integer usuariotw) {
        this.usuariotw = usuariotw;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public TokenSolicitacaoDTO getSolicitacao() {
        return solicitacao;
    }

    public void setSolicitacao(TokenSolicitacaoDTO solicitacao) {
        this.solicitacao = solicitacao;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public Boolean getCodigoViaEmail() {
        return codigoViaEmail;
    }

    public void setCodigoViaEmail(Boolean codigoViaEmail) {
        this.codigoViaEmail = codigoViaEmail;
    }
}
