package servicos.impl.login;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.UsuarioPerfilAcessoVO;
import negocio.comuns.utilitarias.UteisValidacao;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TrocaEmpresaDTO {

    private boolean novo = false;
    private String chave;
    private Integer empresa;
    private String codigoEmpresaRede;
    private String nomeEmpresa;
    private EmpresaAcessoDTO empresasAcesso;

    public TrocaEmpresaDTO() {

    }

    public TrocaEmpresaDTO(String chave, UsuarioPerfilAcessoVO usuarioPerfilAcessoVO) {
        this.novo = false;
        this.chave = chave;
        this.empresa = usuarioPerfilAcessoVO.getEmpresa().getCodigo();
        this.nomeEmpresa = usuarioPerfilAcessoVO.getEmpresa().getNome();
        this.codigoEmpresaRede = usuarioPerfilAcessoVO.getEmpresa().getCodigoRede();
    }

    public TrocaEmpresaDTO(EmpresaAcessoDTO dto) {
        this.novo = true;
        this.chave = dto.getKey();
        this.empresa = dto.getEmp();
        this.nomeEmpresa = UteisValidacao.emptyString(dto.getNomeEmp()) ? (dto.getKey() + " - Unid " + dto.getEmp()) : dto.getNomeEmp();
        this.empresasAcesso = dto;
        this.codigoEmpresaRede = dto.getEmpRede();
    }

    public String getChave() {
        return chave;
    }

    public void setChave(String chave) {
        this.chave = chave;
    }

    public Integer getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Integer empresa) {
        this.empresa = empresa;
    }

    public String getNomeEmpresa() {
        return nomeEmpresa;
    }

    public String getNomeEmpresaApresentar() {
        return this.getNomeEmpresa() + (UteisValidacao.emptyString(this.getCodigoEmpresaRede()) ? "" : (" - " + this.getCodigoEmpresaRede()));
    }

    public void setNomeEmpresa(String nomeEmpresa) {
        this.nomeEmpresa = nomeEmpresa;
    }

    public boolean isNovo() {
        return novo;
    }

    public void setNovo(boolean novo) {
        this.novo = novo;
    }

    public EmpresaAcessoDTO getEmpresasAcesso() {
        return empresasAcesso;
    }

    public void setEmpresasAcesso(EmpresaAcessoDTO empresasAcesso) {
        this.empresasAcesso = empresasAcesso;
    }

    public String getCodigoEmpresaRede() {
        return codigoEmpresaRede;
    }

    public void setCodigoEmpresaRede(String codigoEmpresaRede) {
        this.codigoEmpresaRede = codigoEmpresaRede;
    }
}
