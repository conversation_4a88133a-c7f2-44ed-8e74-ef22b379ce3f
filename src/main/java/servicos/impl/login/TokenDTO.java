package servicos.impl.login;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TokenDTO {

    private String token;
    private String usuarioGeral;
    //dados de onde originou a solicitação
    private String ip;
    private String origem;
    //utilizado para confirmar o código sms
    private String codigoVerificacao;

    public TokenDTO() {
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUsuarioGeral() {
        return usuarioGeral;
    }

    public void setUsuarioGeral(String usuarioGeral) {
        this.usuarioGeral = usuarioGeral;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getOrigem() {
        return origem;
    }

    public void setOrigem(String origem) {
        this.origem = origem;
    }

    public String getCodigoVerificacao() {
        return codigoVerificacao;
    }

    public void setCodigoVerificacao(String codigoVerificacao) {
        this.codigoVerificacao = codigoVerificacao;
    }
}
