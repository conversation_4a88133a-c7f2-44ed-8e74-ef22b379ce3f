package servicos.impl.boleto.brb;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.ContaCorrenteVO;
import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by GlaucoT on 20/10/2016
 */
public class LayoutRemessaBRBCNAB400 extends LayoutRemessaBase {


    public static void preencherArquivoRemessa(RemessaVO remessa) {

        ContaCorrenteVO contaEmpresa = remessa.getConvenioCobranca().getContaEmpresa();

        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        header.put(DCCAttEnum.NomeBanco, "DCB");//Literal DCB
        header.put(DCCAttEnum.VersaoLayout, "001");//Versão
        header.put(DCCAttEnum.NumeroArquivo, "075");//Arquivo

        String contaCorrente = StringUtilities.formatarCampo(new BigDecimal(contaEmpresa.getAgencia() + contaEmpresa.getAgenciaDV()), 3);
        contaCorrente += StringUtilities.formatarCampo(new BigDecimal(contaEmpresa.getContaCorrente() + contaEmpresa.getContaCorrenteDV()), 7);

        String agencia = StringUtilities.formatarCampo(new BigDecimal(contaEmpresa.getAgencia()), 3);
        agencia += contaEmpresa.getAgenciaDV();

        header.put(DCCAttEnum.ContaCorrente, StringUtilities.formatarCampo(new BigDecimal(contaCorrente), 10));//Código do Beneficiário (Conta)
        header.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(remessa.getDataRegistro(), "yyyyMMdd"));//Data de Formatação
        header.put(DCCAttEnum.HoraGeracao, StringUtilities.formatarCampoData(remessa.getDataRegistro(), "HHmmss"));//Hora da Formatação
        header.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.formatarCampo(new BigDecimal(remessa.getListaItens().size() + 1), 6));//Quantidade de Registros

        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();
        int qtdAceito = 0;
        int seq = 2;
        for (RemessaItemVO item : lista) {
            EnderecoVO enderecoVO = new EnderecoVO();
            for (EnderecoVO endereco : item.getPessoa().getEnderecoVOs()) {
                if (endereco.getEnderecoCorrespondencia() || endereco.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo())) {
                    enderecoVO = endereco;
                }
            }
            if(UteisValidacao.emptyNumber(enderecoVO.getCodigo())) {
                if(!UteisValidacao.emptyList(item.getPessoa().getEnderecoVOs())){
                    enderecoVO = item.getPessoa().getEnderecoVOs().get(0);
                }
            }

            RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);

            detail.put(DCCAttEnum.Identificacao, item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? "01" : "02"); //Identificação do Registro
            detail.put(DCCAttEnum.ContaCorrente, StringUtilities.formatarCampo(new BigDecimal(contaCorrente), 10)); //Código do Beneficiário (Conta)
            String cpfCnpj = item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? getCPF(item) : getCNPJ(item); //CPF/CNPJ do Pagador
            detail.put(DCCAttEnum.InscricaoPagador, StringUtilities.formatarCpfCnjp(cpfCnpj, 14));
            //detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(3));//Brancos do CPF {Não existe no layout};
            detail.put(DCCAttEnum.NomePessoa, StringUtilities.formatarCampoEmBranco(item.getPessoa().getNome(), 35));//Nome do Pagador
            detail.put(DCCAttEnum.EnderecoPagador, StringUtilities.formatarCampoEmBranco(enderecoVO.getEndereco(), 35));//Endereço do Pagador
            detail.put(DCCAttEnum.CidadePagador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getCidade().getNome(), 15));//Cidade do Pagador
            detail.put(DCCAttEnum.EstadoPagador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getEstadoVO().getSigla(), 2));//UF do Pagador
            detail.put(DCCAttEnum.CEPPagador, StringUtilities.formatarCampoEmBranco(Uteis.removerMascara(enderecoVO.getCep().trim()), 8));//CEP do Pagador
            detail.put(DCCAttEnum.TipoPagador, "1");// Código Tipo Pessoa
            detail.put(DCCAttEnum.NumDocumento, StringUtilities.formatarCampo(new BigDecimal(item.getCodigo()), 13));//Número do Documento / Seu Número
            detail.put(DCCAttEnum.CodCarteira, "1");//Modalidade da Cobrança
            detail.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(remessa.getDataRegistro(), "ddMMyyyy"));//Data de Emissão do Título
            detail.put(DCCAttEnum.TipoVenda, "21");//Código Tipo Documento
            detail.put(DCCAttEnum.NaturezaCobranca, "0");//Código da Natureza
            detail.put(DCCAttEnum.CondicaoPagamento, "0");//Código da Condição Pagto
            detail.put(DCCAttEnum.Moeda, "02");//Código da Moeda
            detail.put(DCCAttEnum.Banco, "070");//Número do Banco
            detail.put(DCCAttEnum.Agencia, StringUtilities.formatarCampo(new BigDecimal(agencia), 4));//Número da Agência Cobradora
            detail.put(DCCAttEnum.CidadePagador, StringUtilities.formatarCampoEmBranco(30));//Praça de Cobrança
            detail.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(item.getDataVencimentoBoleto(), "ddMMyyyy"));//Data de Vencimento do Tit.
            detail.put(DCCAttEnum.ValorVenda, StringUtilities.formatarCampoMonetario(item.getValorBoleto(), 14));//Valor do Título
            detail.put(DCCAttEnum.NossoNumero, calcularNossoNumero(item));//Nosso Número

            boolean cobrarJuros = remessa.getConvenioCobranca().getEmpresa().getCobrarAutomaticamenteMultaJuros();
            detail.put(DCCAttEnum.CobrarJuros, cobrarJuros ? "51" : "00");//Código do Tipo de Juros

            double valorJuros = cobrarJuros ? Uteis.arredondarForcando2CasasDecimais(remessa.getConvenioCobranca().getEmpresa().getJurosCobrancaAutomatica()) : 0.0;
            double valorMulta = remessa.getConvenioCobranca().getEmpresa().getMultaCobrancaAutomatica();
            detail.put(DCCAttEnum.Juros, StringUtilities.formatarCampoMonetario(valorJuros, 14));//Valor do Juro (Nominal/Tx)
            detail.put(DCCAttEnum.ValorAbatimento, StringUtilities.formatarCampoZerado(14));//Valor do Abatimento
            detail.put(DCCAttEnum.DescontoDia, "00");//Código do Desconto
            detail.put(DCCAttEnum.DataLimiteDesconto, StringUtilities.formatarCampoZerado(8));//Data limite para Desconto
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoZerado(14));//Valor do Desconto
            detail.put(DCCAttEnum.Instrucao1, "94");//Código da 1º Instrução
            detail.put(DCCAttEnum.PrazoInstrucao1, "30");//Prazo da 1º Instrução
            detail.put(DCCAttEnum.Instrucao2, (cobrarJuros && valorMulta > 0.0) ? "03" : "00");//Código da 2º Instrução
            detail.put(DCCAttEnum.PrazoInstrucao2, "00");//Prazo da 2º Instrução
            detail.put(DCCAttEnum.TaxaInstrucao, StringUtilities.formatarCampoMonetario(valorMulta, 5));//Taxa ref, a uma das duas Inst.
            detail.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getNome(), 40));//Emitente do Título
            detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(40));//Mensagem Livre (Observações)
            detail.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(29));//Branco
            detail.put(DCCAttEnum.IncrementoCampoLivre, StringUtilities.formatarCampoEmBranco(3));//Incremento do Campo Livre do Nosso Número

            if (item.getCodigo() != 0) {
                qtdAceito += 1;
            }
            seq++;
            listaDetalhe.add(detail);
        }
        remessa.setQtdAceito(qtdAceito);

        remessa.setHeaderRemessa(header);
        remessa.setDetailsRemessa(listaDetalhe);

        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));

        //Usar o Props pelo fato do Banco não aceitar informações no Trailer
        Map<String, String> infoAdicional = new HashMap<String, String>();
        double valorTotalBruto = 0.0;
        for (RemessaItemVO boleto : remessa.getListaItens()) {
            for (RemessaItemMovParcelaVO parcela : boleto.getMovParcelas()) {
                valorTotalBruto += parcela.getMovParcelaVO().getValorParcela();
            }
        }
        infoAdicional.put(DCCAttEnum.QuantidadeRegistros.name(), String.valueOf(remessa.getListaItens().size()));
        infoAdicional.put(DCCAttEnum.ValorTotalBruto.name(), StringUtilities.formatarCampoMonetario(valorTotalBruto, 10));
        remessa.setProps(infoAdicional);
    }

    public static String calcularNossoNumero(RemessaItemVO item) {
        StringBuilder nossoNumero = new StringBuilder();

        Integer carteira = item.getRemessa().getConvenioCobranca().getCarteira();
        Integer sequencial = item.getIdentificador();
        String codFixo = "070";

        nossoNumero.append(carteira);
        nossoNumero.append(StringUtilities.formatarCampo(new BigDecimal(sequencial), 6));
        nossoNumero.append(codFixo);
        nossoNumero.append(calcularDV1DV2(item));

        return nossoNumero.toString();
    }

    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            h = new RegistroRemessa(TipoRegistroEnum.HEADER);

            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("02RETORNO")) {
                    lerAtributos(linha, h);
                }
                break;
            }
        }
        return h;
    }

    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("02RETORNO")) {
                    lerAtributos(linha, h);
                } else if (linha.startsWith("1")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("9")) {
                    lerAtributos(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);

            validarArquivoRemessaRetorno(remessa);

            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {
        if (r.getTipo() == TipoRegistroEnum.HEADER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));//Identificação do Registro
            r.put(DCCAttEnum.CodigoRetorno, StringUtilities.readString(1, linha));//Identificação do Arquivo
            r.put(DCCAttEnum.IdentificacaoRetorno, StringUtilities.readString(2, 9, linha));//Literal Retorno
            r.put(DCCAttEnum.CodigoServico, StringUtilities.readString(9, 11, linha));//Código do Serviço
            r.put(DCCAttEnum.IdentificacaoServico, StringUtilities.readString(11, 19, linha));//Literal Cobranca
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(19, 26, linha));//Branco
            r.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(26, 46, linha));//Código do Beneficiário (Conta)
            r.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(46, 76, linha));//Nome do Beneficiário
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(76, 79, linha));//Numero do BRB
            r.put(DCCAttEnum.NomeBanco, StringUtilities.readString(79, 82, linha));//Nome do Banco
            r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(82, 94, linha));//Branco
            r.put(DCCAttEnum.DataGeracao, StringUtilities.readString(94, 102, linha));//Data da Gravação
            r.put(DCCAttEnum.EmBranco3, StringUtilities.readString(102, 379, linha));//Branco,Branco,Branco
            r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(379, 387, linha));//Data do Movimento
            r.put(DCCAttEnum.EmBranco4, StringUtilities.readString(387, 394, linha));//Branco
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));//Sequêncial de Registro
        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));//Identificação do Registro
            r.put(DCCAttEnum.TipoPagador, StringUtilities.readString(1, 3, linha));//Tipo de Inscrição
            r.put(DCCAttEnum.InscricaoPagador, StringUtilities.readString(3, 17, linha));//CNPJ ou CPF do Pagador
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(17, 20, linha));//Branco
            r.put(DCCAttEnum.Zeros, StringUtilities.readString(20, 24, linha));//zero
            r.put(DCCAttEnum.IncrementoCampoLivre, StringUtilities.readString(24, 27, linha));//Incremento do Campo Livre do Nosso Número
            r.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(27, 37, linha));//Código de Convênio do Beneficiário (Conta)
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(37, 70, linha));//Branco, Branco
            r.put(DCCAttEnum.NossoNumero, StringUtilities.readString(70, 82, linha));//Nosso Número
            r.put(DCCAttEnum.NumeroEstabelecimento, StringUtilities.readString(82, 92, linha));//Cód de Convênio do Beneficiário Centralizador
            r.put(DCCAttEnum.NumDocumento, StringUtilities.readString(92, 105, linha));//Número do Documento / Seu Número
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(105, 107, linha));//Branco
            r.put(DCCAttEnum.BoletoDDA, StringUtilities.readString(107, linha));//Indicação de Pagador eletrônico DDA
            r.put(DCCAttEnum.IdentificacaoOcorrencia, StringUtilities.readString(108, 110, linha));//Ocorrência
            r.put(DCCAttEnum.DataOcorrencia, StringUtilities.readString(110, 118, linha));//Data Ocorrência
            r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(118, 126, linha));//Data de Crédito
            r.put(DCCAttEnum.StatusVenda, StringUtilities.readString(126, 128, linha));//Tipo de liquidação
            r.put(DCCAttEnum.NossoNumero, StringUtilities.readString(128, 140, linha));//Nosso Número
            r.put(DCCAttEnum.RateioCredito, StringUtilities.readString(140, 144, linha));//Código do Rateio
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(144, 148, linha));//Branco
            r.put(DCCAttEnum.DataVencimento, StringUtilities.readString(148, 156, linha));//Data Vencimento
            r.put(DCCAttEnum.ValorVenda, StringUtilities.readString(156, 169, linha));//Valor do Titulo
            r.put(DCCAttEnum.BancoCobranca, StringUtilities.readString(169, 172, linha));//Banco Cobrador
            r.put(DCCAttEnum.Agencia, StringUtilities.readString(172, 177, linha));//Agência Cobradora
            r.put(DCCAttEnum.EspecieTitulo, StringUtilities.readString(177, 179, linha));//Espécie de Título
            r.put(DCCAttEnum.Zeros, StringUtilities.readString(179, 218, linha));//Zeros, Zeros, Zeros
            r.put(DCCAttEnum.IOF, StringUtilities.readString(218, 231, linha));//IOF Devido
            r.put(DCCAttEnum.Zeros, StringUtilities.readString(231, 244, linha));//Zeros
            r.put(DCCAttEnum.ValorDesconto, StringUtilities.readString(244, 257, linha));//Desconto do Título
            r.put(DCCAttEnum.ValorPago, StringUtilities.readString(257, 270, linha));//Valor pago
            r.put(DCCAttEnum.ValorAbatimentoConcedido, StringUtilities.readString(270, 283, linha));//Outros Débitos (Abatimentos e descontos)
            r.put(DCCAttEnum.OutrosCreditos, StringUtilities.readString(283, 296, linha));//Outros Créditos (Soma de Multa e Juros)
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(296, 299, linha));//Branco, Branco
            r.put(DCCAttEnum.DataPagamento, StringUtilities.readString(299, 307, linha));//Data do Pagamento/Liquidação
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(307, 320, linha));//Ignorar Esse Campo,Ignorar Esse Campo,Ignorar Esse Campo
            r.put(DCCAttEnum.Zeros, StringUtilities.readString(320, 360, linha));//Zeros, Zeros, Zeros, Zeros, Zeros
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(360, 364, linha));//Branco
            r.put(DCCAttEnum.Motivo, StringUtilities.readString(364, 394, linha));//Motivo de Rejeição
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));//Sequencial de Registro
        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));//Identificação do Registro
            r.put(DCCAttEnum.IdentificacaoRetorno, StringUtilities.readString(1, linha));//Identificação do Retorno
            r.put(DCCAttEnum.IdentificacaoServico, StringUtilities.readString(2, 4, linha));//Tipo de Registro {Seguindo padrão do Santander}
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(4, 7, linha));//Código do Banco
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(7, 57, linha));//Branco
            r.put(DCCAttEnum.QuantidadeRegistros, StringUtilities.readString(57, 62, linha));//Quantidade de Entrada
            r.put(DCCAttEnum.ValorTotalBruto, StringUtilities.readString(62, 74, linha));//Valor da Entrada
            r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(74, 86, linha));//Branco
            r.put(DCCAttEnum.QtdConfirmados, StringUtilities.readString(86, 91, linha));//Qte Total de Liquidações
            r.put(DCCAttEnum.ValorConfirmados, StringUtilities.readString(91, 103, linha));//Valor Total das Liquidações
            r.put(DCCAttEnum.EmBranco3, StringUtilities.readString(103, 394, linha));//Branco
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));//Sequência do Registro
        }
    }


    private static Integer calcularDV1(RemessaItemVO item) {
        StringBuilder chave = getChave(item);

        int somatorio = 0;
        for (int i = 0; i < chave.length(); i++) {
            int peso = ((i % 2) == 0) ? 2 : 1;
            int mult = Integer.parseInt(chave.substring(i, i + 1)) * peso;
            somatorio += (mult > 9) ? mult - 9 : mult;
        }

        int resto10 = somatorio % 10;
        if (resto10 == 0) {
            return 0;
        }
        return 10 - resto10;
    }

    private static Integer calcularDV2(RemessaItemVO item, Integer dv1) {
        StringBuilder chave = getChave(item);
        chave.append(dv1);

        int[] pesos = {7, 6, 5, 4, 3, 2};

        int somatorio = 0;
        for (int i = 0; i < chave.length(); i++) {
            int peso = pesos[(i % pesos.length)];
            int mult = Integer.parseInt(chave.substring(i, i + 1)) * peso;
            somatorio += mult;
        }

        return somatorio % 11;
    }

    private static StringBuilder getChave(RemessaItemVO item) {
        ContaCorrenteVO contaEmpresa = item.getRemessa().getConvenioCobranca().getContaEmpresa();
        String contaCorrente = StringUtilities.formatarCampo(new BigDecimal(contaEmpresa.getAgencia() + contaEmpresa.getAgenciaDV()), 3);
        contaCorrente += StringUtilities.formatarCampo(new BigDecimal(contaEmpresa.getContaCorrente() + contaEmpresa.getContaCorrenteDV()), 7);

        Integer carteira = item.getRemessa().getConvenioCobranca().getCarteira();
        Integer sequencial =  item.getRemessa().getConvenioCobranca().getSequencialItem();
        String codFixo = "070";

        StringBuilder chave = new StringBuilder();
        chave.append("000"); //Incremento do sequencial, não usando.
        chave.append(StringUtilities.formatarCampo(new BigDecimal(contaCorrente), 10));
        chave.append(carteira);
        chave.append(StringUtilities.formatarCampo(new BigDecimal(sequencial), 6));
        chave.append(codFixo);
        return chave;
    }

    private static String calcularDV1DV2(RemessaItemVO item) {
        Integer dv1 = calcularDV1(item);
        Integer dv2 = calcularDV2(item, dv1);

        if (dv2 == 0) {
            return dv1 + "" + dv2;
        } else if (dv2 == 1) {
            dv1 += 1;
            if (dv1 == 10) {
                dv1 = 0;
            }
            dv2 = calcularDV2(item, dv1);
        }

        dv2 = 11 - dv2;
        return dv1 + "" + dv2;
    }
}