package servicos.impl.boleto.sicredi;

public enum SicrediCNAB400StatusEnum {

    StatusNENHUM("NENHUM", "Nenhum Status"),
    Status02("02", "Entrada confirmada"),
    Status03("03", "Entrada rejeitada"),
    Status06("06", "Liquidação normal"),
    Status09("09", "Baixado automaticamente via arquivo"),
    Status10("10", "Baixado conforme instruções da cooperativa de crédito"),
    Status12("12", "Abatimento concedido"),
    Status13("13", "Abatimento cancelado"),
    Status14("14", "Vencimento alterado"),
    Status15("15", "Liquidação em cartório"),
    Status17("17", "Liquidação após baixa"),
    Status19("19", "Confirmação de recebimento de instrução de protesto"),
    Status20("20", "Confirmação de recebimento de instrução de sustação de protesto"),
    Status23("23", "Entrada de título em cartório"),
    Status24("24", "Entrada rejeitada por CEP irregular"),
    Status27("27", "Baixa rejeitada"),
    Status28("28", "Tarifa"),
    Status29("29", "Rejeição do pagador"),
    Status30("30", "Alteração rejeitada"),
    Status32("32", "Instrução rejeitada"),
    Status33("33", "Confirmação de pedido de alteração de outros dados"),
    Status34("34", "Retirado de cartório e manutenção em carteira"),
    Status35("35", "Aceite do pagador");

    private String id;
    private String descricao;

    private SicrediCNAB400StatusEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public static SicrediCNAB400StatusEnum valueOff(String id) {
        SicrediCNAB400StatusEnum[] values = SicrediCNAB400StatusEnum.values();
        for (SicrediCNAB400StatusEnum statusEnum : values) {
            if (statusEnum.getId().equals(id)) {
                return statusEnum;
            }
        }
        return SicrediCNAB400StatusEnum.StatusNENHUM;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

}
