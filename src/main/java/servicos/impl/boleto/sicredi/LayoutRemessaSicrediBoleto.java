package servicos.impl.boleto.sicredi;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import org.jboleto.JBoletoBean;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class LayoutRemessaSicrediBoleto extends LayoutRemessaBase {

    public static void gerarArquivoRemessa(RemessaVO remessa) {
        RegistroRemessa header = gerarHeader(remessa);
        List<RegistroRemessa> detalhe = gerarDetalheCobrancaComRegistro(remessa);
        RegistroRemessa trailer = gerarTrailer(remessa);

        remessa.setHeaderRemessa(header);
        remessa.setDetailsRemessa(detalhe);
        remessa.setTrailerRemessa(trailer);
        remessa.setTrailerRemessaArquivo(trailer);

        remessa.setHeaderArquivo(new StringBuilder(header.toString()));
        remessa.setHead(new StringBuilder(header.toString()));
        remessa.setDetail(new StringBuilder(detalhe.toString()));
        remessa.setTrailerArquivo(new StringBuilder(trailer.toString()));
        remessa.setTrailer(new StringBuilder(trailer.toString()));
    }

    private static RegistroRemessa gerarHeader(RemessaVO remessa) {
        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER_ARQUIVO);
        header.put(DCCAttEnum.TipoRegistro, "0");//1
        header.put(DCCAttEnum.CodigoRemessa, "1");//1
        header.put(DCCAttEnum.IdentificacaoRemessa, "REMESSA");//7
        header.put(DCCAttEnum.CodigoServico, "01");//2
        header.put(DCCAttEnum.IdentificacaoServico, StringUtilities.formatarCampoEmBranco("COBRANCA", 15));
//        027 a 031   005   Código do beneficiário   Código do beneficiário
        header.put(DCCAttEnum.CodigoBeneficiario, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getNumeroContrato(), 5));
//        032 a 045   014   CPF/CGC do beneficiário  Informar CPF/CNPJ do beneficiário. Alinhado à direita e zeros à esquerda;
        header.put(DCCAttEnum.CnpjEmpresa, StringUtilities.formatarCampoForcandoZerosAEsquerda(Uteis.removerMascara(remessa.getConvenioCobranca().getEmpresa().getCNPJ()), 14));
//        046 a 076   031   Filler   Deixar em Brancos (sem preenchimento)
        header.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(31));
//        077 a 079   003   Número do Sicredi   ?748?
//        080 a 094   015   Literal Sicredi   ?SICREDI?
        header.put(DCCAttEnum.IdentificacaoBanco, StringUtilities.formatarCampoEmBranco("748SICREDI", 18));
//        095 a 102   008   Data de gravação do arquivo     O Formato da data de geração do arquivo deve estar no padrão: AAAAMMDD
        header.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(remessa.getDataRegistro(), "yyyyMMdd"));
//        103 a 110   008   Filler   Deixar em Branco (sem preenchimento)
        header.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(8));
//        111 a 117   007   Número da remessa   Deve ser maior que zero: número do último arquivo remessa + 1;
        header.put(DCCAttEnum.NumeroResumoOperacoes, StringUtilities.formatarCampo(new BigDecimal(remessa.getSequencialArquivo()), 7));//sequencial de Remessa
        remessa.getProps().put(DCCAttEnum.NumeroResumoOperacoes.name(), remessa.getSequencialArquivo());
//        118 a 390   273   Filler   Deixar em Branco (sem preenchimento)
        header.put(DCCAttEnum.EmBranco3, StringUtilities.formatarCampoEmBranco(273));
//        391 a 394   004   Versão do sistema   2.00 (o ponto deve ser colocado)
        header.put(DCCAttEnum.VersaoLayout, "2.00");
//        395 a 400   006   Número sequencial do registro   Alinhado à direita e zeros à esquerda
        header.put(DCCAttEnum.SequencialRegistro, "000001");//6
        return header;
    }

    private static String nossoNumero(RemessaItemVO item, RemessaVO remessa){
        return gerarNossoNumero(item, item.getIdentificador(),
                    2,
                    Integer.parseInt(remessa.getConvenioCobranca().getContaEmpresa().getAgencia()),
                    Integer.parseInt(StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getContaEmpresa().getAgenciaDV().replace(" ", ""), 2)),
                    Integer.parseInt(remessa.getConvenioCobranca().getNumeroContrato().replace("-","").replace(" ", "")));
    }

    private static List<RegistroRemessa> gerarDetalheCobrancaComRegistro(RemessaVO remessa){

        List<RegistroRemessa> detalhes =  new ArrayList<RegistroRemessa>();

        int sequencial = 2;
        double valorBruto = 0;
        for (RemessaItemVO item:remessa.getListaItens()) {
            RegistroRemessa detalhe = new RegistroRemessa(TipoRegistroEnum.DETALHE);

            detalhe.put(DCCAttEnum.TipoRegistro, "1");
            detalhe.put(DCCAttEnum.TipoCobranca, "A");
            detalhe.put(DCCAttEnum.TipoCarteira, "A");
            detalhe.put(DCCAttEnum.TipoImpressao, "A");
            detalhe.put(DCCAttEnum.Filler, filler(12));
            detalhe.put(DCCAttEnum.Moeda, "A");
            detalhe.put(DCCAttEnum.TipoDesconto, "A");
            detalhe.put(DCCAttEnum.TipoJuros, "A");
            detalhe.put(DCCAttEnum.Filler, filler(28));
            detalhe.put(DCCAttEnum.NossoNumero, nossoNumero(item, remessa));
            detalhe.put(DCCAttEnum.Filler, filler(6));
            detalhe.put(DCCAttEnum.DataGeracao, data8Dig(new Date()));
            detalhe.put(DCCAttEnum.CampoAlterado, " ");
            detalhe.put(DCCAttEnum.PostagemDoTitulo, "N");
            detalhe.put(DCCAttEnum.Filler, filler(1));
            detalhe.put(DCCAttEnum.TipoEmissao, "B");
            detalhe.put(DCCAttEnum.NumeroParcelaCarne, "00");
            detalhe.put(DCCAttEnum.TotalParcelasCarne, "00");
            detalhe.put(DCCAttEnum.Filler, filler(4));
            detalhe.put(DCCAttEnum.ValorDescontoDiaAntecipacao, valorDesconto(item, 10));
            detalhe.put(DCCAttEnum.PercentualMulta, percentualMulta(item));
            detalhe.put(DCCAttEnum.Filler, filler(12));
            detalhe.put(DCCAttEnum.Instrucao, "01");
            detalhe.put(DCCAttEnum.SeuNumero, zeros(10));
            detalhe.put(DCCAttEnum.DataVencimento, data(getDataVencimento(item.getDataVencimentoBoleto(),remessa.getDataRegistro(),remessa)));
            detalhe.put(DCCAttEnum.ValorTitulo, valorBoleto(item));
            detalhe.put(DCCAttEnum.Filler, filler(9));
            detalhe.put(DCCAttEnum.EspecieDocumento,"J");
            detalhe.put(DCCAttEnum.Aceite, "N");
            detalhe.put(DCCAttEnum.DataEmissao, data(remessa.getDataRegistro()));
            detalhe.put(DCCAttEnum.Protesto, "00");
            detalhe.put(DCCAttEnum.NumeroDiasProtesto, "00" );
            detalhe.put(DCCAttEnum.ValorJurosDiaAtraso, zeros(13));
            detalhe.put(DCCAttEnum.DataLimiteDesconto, data(item.getDataVencimentoBoleto()));
            detalhe.put(DCCAttEnum.ValorDesconto, valorDesconto(item, 13));
//        193 a 205   013   Filler   Sempre preencher com zeros neste campo.
            detalhe.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(13));
//        206 a 218   013   Valor do abatimento     Informar valor do abatimento (alinhado à direita e zeros à esquerda) ou preencher com zeros.
            detalhe.put(DCCAttEnum.ValorAbatimento, StringUtilities.formatarCampoZerado(13));
//        219 a 219   001     Tipo de pessoa do pagador: PF ou PJ       //'1' - Pessoa Física //'2' - Pessoa Jurídica
            detalhe.put(DCCAttEnum.CpfOuCnpj, item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? "1" : "2");
//        220 a 220   001   Filler   Sempre preencher com zeros neste campo.
            detalhe.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoZerado(1));
//        221 a 234   014   CPF/CNPJ do Pagador     Alinhado à direita e zeros à esquerda;   Obs.: No momento dos testes para homologação estes dados devem ser enviados com informações válidas.
            String cpfCnpj = item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? getCPF(item) : getCNPJ(item);
            detalhe.put(DCCAttEnum.CpfCnpjPagador, StringUtilities.formatarCampoForcandoZerosAEsquerda(cpfCnpj, 14));
//        235 a 274   040   Nome do pagador         Neste campo informar o nome do pagador sem acentuação ou caracteres especiais.
            detalhe.put(DCCAttEnum.NomePagador, getNome(item, 40));
            detalhe.put(DCCAttEnum.EnderecoPagador, retirarCaracteresInvalidos(espacos(endereco(item).getEndereco(), 40), new String[]{","}));
            detalhe.put(DCCAttEnum.CodigoPagador, zeros(5));
            detalhe.put(DCCAttEnum.Filler, zeros(6));
            detalhe.put(DCCAttEnum.Filler, filler(1));
            detalhe.put(DCCAttEnum.CEPPagador, zeros(cep(item).trim(), 8));
            detalhe.put(DCCAttEnum.CodigoPagadorZW, zeros(5));
            detalhe.put(DCCAttEnum.CpfCnpjSacadorAvalista, zeros(14));
            detalhe.put(DCCAttEnum.NomeSacadorAvalista, espacos(41));
            detalhe.put(DCCAttEnum.NumeroSequencialRegistro, zeros(sequencial, 6));

            detalhes.add(detalhe);

            sequencial++;
            valorBruto += item.getValorItemRemessa();
        }

        remessa.setValorBruto(valorBruto);

        return detalhes;
    }

    private static RegistroRemessa gerarTrailer(RemessaVO remessa) {
        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER_ARQUIVO);
        trailer.put(DCCAttEnum.TipoRegistro, "9");
        trailer.put(DCCAttEnum.IdentificacaoRemessa, "1");
        trailer.put(DCCAttEnum.Banco, "748");
        trailer.put(DCCAttEnum.CodigoBeneficiario, zeros(remessa.getConvenioCobranca().getNumeroContrato(), 5));
        trailer.put(DCCAttEnum.Filler, espacos(384));
        int sequencial = remessa.getListaItens().size() + 2;
        trailer.put(DCCAttEnum.SequencialRegistro, zeros(sequencial, 6));

        remessa.setQtdRegistros(sequencial);

        return trailer;
    }

    private static String valorBoleto(RemessaItemVO item){
        return String.format("%014.2f", item.getValorItemRemessa())
                .replace(".", "")
                .replace(",", "");
    }

    private static String cep(RemessaItemVO item){
        return endereco(item).getCep()
                .replace(".", "")
                .replace("-", "")
                .replace(",", "");
    }

    private static EnderecoVO endereco(RemessaItemVO item) {
        return LayoutRemessaBase.getEndereco(item);
    }

    private static String percentualMulta(RemessaItemVO item){
        return String.format("%05.2f",
                item.getRemessa().getConvenioCobranca().getEmpresa().getMultaCobrancaAutomatica()
        ).replace(".", "").replace(",", "");
    }

    private static String valorDesconto(RemessaItemVO item, int tamanho) {

        if( item.possuiDesconto() ){
            Double valorDesconto =  item.getValorBoleto() * item.getPorcentagemDescontoBoletoPagAntecipado() / 100;
            return zeros(String.format("%011.2f",valorDesconto)
                    .replace(".", "")
                    .replace(",", ""), tamanho);
        }else{
            return zeros(tamanho);
        }
    }

    private static String data(Date data){
        return StringUtilities.formatarCampoData(data, "ddMMyy");
    }

    private static String data8Dig(Date data){
        return StringUtilities.formatarCampoData(data, "yyyyMMdd");
    }

    private static String zeros(String str, int tamanho){
        return StringUtilities.formatarCampoForcandoZerosAEsquerda(str, tamanho);
    }

    private static String zeros(int num, int tamanho){
        return StringUtilities.formatarCampoForcandoZerosAEsquerda(num, tamanho);
    }

    private static String filler(int tamanho){
        return StringUtilities.formatarCampoEmBranco(tamanho);
    }

    private static String zeros(int num){
        return StringUtilities.formatarCampoZerado(num);
    }

    private static String espacos(String str, int tamanho){
        return StringUtilities.formatarCampoEmBranco(str, tamanho);
    }

    private static String espacos(int tamanho){
        return StringUtilities.formatarCampoEmBranco(tamanho);
    }

    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("02RETORNO")) {
                    lerAtributos(linha, h);
                } else if (linha.startsWith("1")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributos(linha, detail);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("9")) {
                    lerAtributos(linha, t);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);

            validarArquivoRemessaRetorno(remessa);

            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {
        if (r.getTipo() == TipoRegistroEnum.HEADER) {
//            001 a 001 001 Identificação do registro header '0'
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));
//            002 a 002 001 Identificação do arquivo retorno '2'
            r.put(DCCAttEnum.CodigoRetorno, StringUtilities.readString(1, linha));
//            003 a 009 007 Literal retorno 'RETORNO'
            r.put(DCCAttEnum.IdentificacaoRetorno, StringUtilities.readString(2, 9, linha));
//            010 a 011 002 Código do serviço de cobrança '01'
            r.put(DCCAttEnum.CodigoServico, StringUtilities.readString(9, 11, linha));
//            012 a 026 015 Literal cobrança 'COBRANCA'
            r.put(DCCAttEnum.IdentificacaoServico, StringUtilities.readString(11, 26, linha));
//            027 a 031 005 Código do beneficiário Campo numérico
            r.put(DCCAttEnum.CodigoBeneficiario, StringUtilities.readString(26, 31, linha));
//            032 a 045 014 CIC/CGC do beneficiário Alinhado à direita e zeros à esquerda;
            r.put(DCCAttEnum.CnpjEmpresa, StringUtilities.readString(31, 45, linha));
//            046 a 076 031 Filler Brancos
            r.put(DCCAttEnum.Filler, StringUtilities.readString(45, 76, linha));
//            077 a 079 003 Número do Sicredi '748'
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(76, 79, linha));
//            080 a 094 015 Literal BANS 'BANSICREDI'
            r.put(DCCAttEnum.NomeBanco, StringUtilities.readString(79, 94, linha));
//            095 a 102 008 Data de gravação do arquivo Formato padrão: AAAAMMDD
            r.put(DCCAttEnum.DataGeracao, StringUtilities.readString(94, 102, linha));
//            103 a 110 008 Filler Brancos (sem preenchimento)
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(102, 110, linha));
//            111 a 117 007 Número do retorno Alinhado à direita e zeros à esquerda;
            r.put(DCCAttEnum.NumAvisoBancario, StringUtilities.readString(110, 117, linha));
//            118 a 389 272 Filler Brancos (sem preenchimento)
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(117, 389, linha));
//            390 a 394 005 Versão do sistema 99.99
            r.put(DCCAttEnum.VersaoLayout, StringUtilities.readString(389, 394, linha));
//            395 a 400 006 Número seqüencial do registro Alinhado à direita e zeros à esquerda;
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));
        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {
            if (linha.length() >= 400) {
//                001 a 001 001 Identificação do registro detalhe '1'
                r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, linha));
//                002 a 013 012 Filler Brancos (sem preenchimento)
                r.put(DCCAttEnum.EmBranco, StringUtilities.readString(1, 13, linha));
//                014 a 014 001 Tipo de cobrança 'A' - Sicredi Cobrança com registro
                r.put(DCCAttEnum.TipoCobranca, StringUtilities.readString(13, 14, linha));
//                015 a 019 005 Código do pagador na cooperativa do beneficiário
                r.put(DCCAttEnum.CodigoPagador, StringUtilities.readString(14, 19, linha));
//                020 a 024 005 Código do pagador junto ao associado
                r.put(DCCAttEnum.CodigoPagadorZW, StringUtilities.readString(19, 24, linha));
//                025 a 025 001 Boleto DDA 1 - Boleto enviado a CIP/DDA 2 - Boleto normal
                r.put(DCCAttEnum.BoletoDDA, StringUtilities.readString(24, 25, linha));
//                026 a 047 022 Filler Brancos (sem preenchimento)
                r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(25, 47, linha));
//                048 a 062 015 Nosso número Sicredi sem edição
                String nossoNumero = StringUtilities.readString(47, 61, linha);
                nossoNumero = nossoNumero.trim();
                r.put(DCCAttEnum.NossoNumero, nossoNumero.substring(3, nossoNumero.length() - 1));
                r.put(DCCAttEnum.NossoNumeroDV, nossoNumero.substring(nossoNumero.length() - 1));
//                063 a 108 046 Filler Brancos (sem preenchimento)
                r.put(DCCAttEnum.EmBranco3, StringUtilities.readString(62,108, linha));
//                109 a 110 002 Ocorrência Confira tabela de Ocorrências no item 6.2
                r.put(DCCAttEnum.StatusVenda, StringUtilities.readString(108, 110, linha));
//                111 a 116 006 Data da ocorrência Formato padrão: DDMMAA
                r.put(DCCAttEnum.DataOcorrencia, StringUtilities.readString(110, 116, linha));
//                117 a 126 010 Seu número Seu número enviado na Remessa
                r.put(DCCAttEnum.SeuNumero, StringUtilities.readString(116,126, linha));
//                127 a 146 020 Filler
                r.put(DCCAttEnum.Filler, StringUtilities.readString(126, 146, linha));
//                147 a 152 006 Data de vencimento Formato padrão: DDMMAA
                r.put(DCCAttEnum.DataVencimento, StringUtilities.readString(146,152, linha));
//                153 a 165 013 Valor do título Alinhado à direita e zeros à esquerda;
                r.put(DCCAttEnum.ValorVenda, StringUtilities.readString(152, 165, linha));
//                166 a 174 009 Filler Brancos (sem preenchimento)
                r.put(DCCAttEnum.EmBranco4, StringUtilities.readString(165, 174, linha));
//                175 a 175 001 Espécie de documento
                r.put(DCCAttEnum.EspecieDocumento, StringUtilities.readString(174, 175, linha));
//                176 a 188 013 Despesas de cobrança Alinhado à direita e zeros à esquerda;
                r.put(DCCAttEnum.DespesaCobranca, StringUtilities.readString(175, 188, linha));
//                189 a 201 013 Despesas de custas de protesto Alinhado à direita e zeros à esquerda;
                r.put(DCCAttEnum.DespesasProtesto, StringUtilities.readString(188, 201, linha));
//                202 a 227 026 Filler Zeros
                r.put(DCCAttEnum.Zeros, StringUtilities.readString(201, 227, linha));
//                228 a 240 013 Abatimento concedido Alinhado à direita e zeros à esquerda;
                r.put(DCCAttEnum.Abatimento, StringUtilities.readString(227, 240, linha));
//                241 a 253 013 Desconto concedido Alinhado à direita e zeros à esquerda;
                r.put(DCCAttEnum.ValorDesconto, StringUtilities.readString(240, 253, linha));
//                254 a 266 013 Valor efetivamente pago Alinhado à direita e zeros à esquerda;
                r.put(DCCAttEnum.ValorPago, StringUtilities.readString(253, 266, linha));
//                267 a 279 013 Juros de mora Alinhado à direita e zeros à esquerda;
                r.put(DCCAttEnum.Juros, StringUtilities.readString(266, 279, linha));
//                280 a 292 013 Multa Alinhado à direita e zeros à esquerda;
                r.put(DCCAttEnum.ValorMora, StringUtilities.readString(279, 292, linha));
//                293 a 294 002 Filler Brancos (sem preenchimento)
                r.put(DCCAttEnum.EmBranco5, StringUtilities.readString(292, 294, linha));
//                295 a 295 001 Somente para ocorrência '19' 'A' - aceito 'D' - desprezado
                r.put(DCCAttEnum.MotivoProtesto, StringUtilities.readString(294, 295, linha));
//                296 a 318 023 Filler Brancos (sem preenchimento)
                r.put(DCCAttEnum.EmBranco6, StringUtilities.readString(295, 318, linha));
//                319 a 328 010 Motivos da ocorrência XXXXXXXXXX Cada dois dígitos 'XX' correspondem a um motivo. Se '00', não há motivo de ocorrência; Confira tabela no item 6.3.
                r.put(DCCAttEnum.Motivo, StringUtilities.readString(318, 328, linha));
//                329 a 336 008 Data prevista para lançamento no conta corrente Formato padrão: AAAAMMDD
                r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(328, 336, linha));
//                337 a 394 058 Filler Brancos (sem preenchimento)
                r.put(DCCAttEnum.EmBranco7, StringUtilities.readString(336, 394, linha));
//                395 a 400 006 Número seqüencial do registro Alinhado à direita e zeros à esquerda;
                r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));
            }
        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {
//            001 a 001 001 Identificação do registro trailer '9'
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
//            002 a 002 001 Identificação do arquivo retorno '2'
            r.put(DCCAttEnum.IdentificacaoRetorno, StringUtilities.readString(1, 2, linha));
//            003 a 005 003 Número do Sicredi '748'
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(2, 5, linha));
//            006 a 010 005 Código do beneficiário Conta Corrente sem o DV ou conta beneficiário.
            r.put(DCCAttEnum.CodigoBeneficiario, StringUtilities.readString(5, 10, linha));
//            011 a 394 384 Filler Brancos (sem preenchimento)
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(10, 394, linha));
//            95 a 400 006 Número seqüencial do registro Alinhado à direita e zeros à esquerda;
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));
        }
    }
    //usado apenas na hora de imprimir o boleto, para gerar usa o da biblioteca do proprio Sicredi BancoSicredi.class
    public static String gerarNossoNumero(RemessaItemVO remessaItemVO, Integer numeroLivre, Integer byteGeracao, Integer agencia, Integer dvAgencia, Integer contaCorrente) {
        String numeroLivreFormat = String.format("%05d", numeroLivre);
        String agenciaFormat = String.format("%04d", agencia);
        String dvAgenciaFormat = String.format("%02d", dvAgencia);
        String contaCorrenteFormat = String.format("%05d", contaCorrente);
        return getAnoQueGerouRemessa(remessaItemVO) + byteGeracao + numeroLivreFormat + gerarDVNossoNumero(remessaItemVO, agenciaFormat, dvAgenciaFormat, contaCorrenteFormat, numeroLivreFormat, byteGeracao.toString());
    }

    //usado apenas na hora de imprimir o boleto, para gerar usa o da biblioteca do proprio Sicredi BancoSicredi.class
    private static String gerarDVNossoNumero(RemessaItemVO remessaItemVO, String agencia, String dvAgencia, String contaCorrente, String numeroLivre, String byteGeracao) {
        JBoletoBean boletoBean = new JBoletoBean();
        String campo = agencia + dvAgencia + contaCorrente + getAnoQueGerouRemessa(remessaItemVO) + byteGeracao + numeroLivre;
        return boletoBean.getModulo11(campo, 9, true);
    }
    //usado apenas na hora de imprimir o boleto, para gerar usa o da biblioteca do proprio Sicredi BancoSicredi.class
    private static String getAnoQueGerouRemessa(RemessaItemVO remessaItemVO) {
        DateFormat df = new SimpleDateFormat("yy");
        return df.format(remessaItemVO.getRemessa().getDataRegistro());
    }
}
