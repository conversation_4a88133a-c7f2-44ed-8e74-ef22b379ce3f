package servicos.impl.boleto.bancobrasil;

/**
 * Created by <PERSON><PERSON><PERSON> on 29/08/2024
 */
public enum TipoCobrancaMultaEJurosBancoBrasilEnum {

    ISENTO(0, "ISENTO", "Não cobrar Juros"),
    VALOR_POR_DIA(1, "VALOR_POR_DIA", "Cobrar Juros valor fixo por dia de atraso"),
    TAXA_MENSAL(2, "TAXA_MENSAL", "Cobrar Juros por porcentagem mensal")
    ;

    private int codigo;
    private String descricao;
    private String explicacao;

    TipoCobrancaMultaEJurosBancoBrasilEnum(int codigo, String descricao, String explicacao) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.explicacao = explicacao;
    }

    public static String consultarDescricaoPorCodigo(int codigo){
        for (TipoCobrancaMultaEJurosBancoBrasilEnum o : TipoCobrancaMultaEJurosBancoBrasilEnum.values() ) {
            if(o.codigo == codigo){
                return o.descricao;
            }
        }
        return "";
    }

    public static TipoCobrancaMultaEJurosBancoBrasilEnum consultarPorCodigo(int codigo){
        for (TipoCobrancaMultaEJurosBancoBrasilEnum bancoEnum: TipoCobrancaMultaEJurosBancoBrasilEnum.values()){
            if (bancoEnum.getCodigo() == codigo){
                return bancoEnum;
            }
        }
        return null;
    }

    public static String consultarExplicacaoPorCodigo(int codigo){
        for (TipoCobrancaMultaEJurosBancoBrasilEnum o : TipoCobrancaMultaEJurosBancoBrasilEnum.values() ) {
            if(o.codigo == codigo){
                return o.explicacao;
            }
        }
        return "";
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
