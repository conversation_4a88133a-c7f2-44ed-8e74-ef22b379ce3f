package servicos.impl.boleto.bancobrasil;

public class ObjetoJSONPagadorBancoBrasil {
    Integer tipoInscricao; //Código que identifica o tipo de inscrição do Pagador. Domínios: 1 - CPF; 2 - CNPJ
    Long numeroInscricao;
    String nome;

    public ObjetoJSONPagadorBancoBrasil() {
    }

    public Integer getTipoInscricao() {
        return tipoInscricao;
    }

    public void setTipoInscricao(Integer tipoInscricao) {
        this.tipoInscricao = tipoInscricao;
    }

    public Long getNumeroInscricao() {
        return numeroInscricao;
    }

    public void setNumeroInscricao(Long numeroInscricao) {
        this.numeroInscricao = numeroInscricao;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }
}
