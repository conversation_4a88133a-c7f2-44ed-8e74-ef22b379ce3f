package servicos.impl.boleto.bancobrasil;

import org.json.JSONObject;

public class ObjetoJSONBancoBrasil {
    Integer numeroConvenio;
    Integer numeroCarteira; //Dado fornecido pelo banco. Determina as características do serviço de Cobrança e define como os boletos serão tratados pelo BB. São identificadas pelos códigos 11, 12, 16,17, 18, 31 e 51.
    Integer numeroVariacaoCarteira; //Dado fornecido pelo banco.
    Integer codigoModalidade = 1; //Código que identifica a característica dos boletos dentro das modalidades de cobrança existentes no BB. Domínio: 1 - Simples; 4 - Vinculada. Não encontrei material explicando como usar cada um desses valores, então usei o valor 1 que foi sugerido no Postman.
    String dataEmissao;
    String dataVencimento;
    Double valorOriginal;
    String indicadorAceiteTituloVencido = "S"; //Indicador de que o boleto pode ou não ser recebido após o vencimento. Domínio: S - Sim; N - Não
    Integer numeroDiasLimiteRecebimento = 90; //Número de dias para recebimento após o vencimento, caso o indicador de aceite de título vencido seja "S".
    String codigoAceite = "A"; //Código para identificar se o boleto de cobrança foi aceito (reconhecimento da dívida pelo Pagador). Domínio: A - Aceito; N - Não aceito
    String codigoTipoTitulo = "02"; //Código que identifica a espécie do Título de Cobrança. Domínios: 1- CHEQUE 2- DUPLICATA MERCANTIL 3- DUPLICATA MTIL POR INDICACAO 4- DUPLICATA DE SERVICO 5- DUPLICATA DE SRVC P/INDICACAO 6- DUPLICATA RURAL 7- LETRA DE CAMBIO 8- NOTA DE CREDITO COMERCIAL 9- NOTA DE CREDITO A EXPORTACAO 10- NOTA DE CREDITO INDULTRIAL 11- NOTA DE CREDITO RURAL 12- NOTA PROMISSORIA 13- NOTA PROMISSORIA RURAL 14- TRIPLICATA MERCANTIL 15- TRIPLICATA DE SERVICO 16- NOTA DE SEGURO 17- RECIBO 18- FATURA 19- NOTA DE DEBITO 20- APOLICE DE SEGURO 21- MENSALIDADE ESCOLAR 22- PARCELA DE CONSORCIO 23- DIVIDA ATIVA DA UNIAO 24- DIVIDA ATIVA DE ESTADO 25- DIVIDA ATIVA DE MUNICIPIO 31- CARTAO DE CREDITO 32- BOLETO PROPOSTA 33- BOLETO APORTE 99- OUTROS.
    String indicadorPermissaoRecebimentoParcial = "N"; //Indicador de permissão para recebimento parcial. Domínio: N - Não; S - Sim
    String numeroTituloBeneficiario;
    String numeroTituloCliente; // Nosso Número, sendo o Identificador do Título no Banco do Brasil
    String mensagemBloquetoOcorrencia;
    ObjetoJSONPagadorBancoBrasil pagador;
    ObjetoJSONJurosMoraBancoBrasil jurosMora;
    ObjetoJSONMultaBancoBrasil multa;

    public ObjetoJSONBancoBrasil() {
    }

    public Integer getNumeroConvenio() {
        return numeroConvenio;
    }

    public void setNumeroConvenio(Integer numeroConvenio) {
        this.numeroConvenio = numeroConvenio;
    }

    public String getDataVencimento() {
        return dataVencimento;
    }

    public void setDataVencimento(String dataVencimento) {
        this.dataVencimento = dataVencimento;
    }

    public Double getValorOriginal() {
        return valorOriginal;
    }

    public void setValorOriginal(Double valorOriginal) {
        this.valorOriginal = valorOriginal;
    }

    public String getNumeroTituloCliente() {
        return numeroTituloCliente;
    }
    public void setNumeroTituloCliente(String numeroTituloCliente) {
        this.numeroTituloCliente = numeroTituloCliente;
    }

    public Integer getNumeroCarteira() {
        return numeroCarteira;
    }

    public void setNumeroCarteira(Integer numeroCarteira) {
        this.numeroCarteira = numeroCarteira;
    }

    public Integer getNumeroVariacaoCarteira() {
        return numeroVariacaoCarteira;
    }

    public void setNumeroVariacaoCarteira(Integer numeroVariacaoCarteira) {
        this.numeroVariacaoCarteira = numeroVariacaoCarteira;
    }

    public Integer getCodigoModalidade() {
        return codigoModalidade;
    }

    public void setCodigoModalidade(Integer codigoModalidade) {
        this.codigoModalidade = codigoModalidade;
    }

    public String getDataEmissao() {
        return dataEmissao;
    }

    public void setDataEmissao(String dataEmissao) {
        this.dataEmissao = dataEmissao;
    }

    public String getCodigoAceite() {
        return codigoAceite;
    }

    public void setCodigoAceite(String codigoAceite) {
        this.codigoAceite = codigoAceite;
    }

    public String getCodigoTipoTitulo() {
        return codigoTipoTitulo;
    }

    public void setCodigoTipoTitulo(String codigoTipoTitulo) {
        this.codigoTipoTitulo = codigoTipoTitulo;
    }

    public String getIndicadorPermissaoRecebimentoParcial() {
        return indicadorPermissaoRecebimentoParcial;
    }

    public void setIndicadorPermissaoRecebimentoParcial(String indicadorPermissaoRecebimentoParcial) {
        this.indicadorPermissaoRecebimentoParcial = indicadorPermissaoRecebimentoParcial;
    }

    public String getNumeroTituloBeneficiario() {
        return numeroTituloBeneficiario;
    }

    public void setNumeroTituloBeneficiario(String numeroTituloBeneficiario) {
        this.numeroTituloBeneficiario = numeroTituloBeneficiario;
    }

    public String getMensagemBloquetoOcorrencia() {
        return mensagemBloquetoOcorrencia;
    }

    public void setMensagemBloquetoOcorrencia(String mensagemBloquetoOcorrencia) {
        this.mensagemBloquetoOcorrencia = mensagemBloquetoOcorrencia;
    }

    public ObjetoJSONPagadorBancoBrasil getPagador() {
        return pagador;
    }

    public void setPagador(ObjetoJSONPagadorBancoBrasil pagador) {
        this.pagador = pagador;
    }

    public String getIndicadorAceiteTituloVencido() {
        return indicadorAceiteTituloVencido;
    }

    public void setIndicadorAceiteTituloVencido(String indicadorAceiteTituloVencido) {
        this.indicadorAceiteTituloVencido = indicadorAceiteTituloVencido;
    }

    public Integer getNumeroDiasLimiteRecebimento() {
        return numeroDiasLimiteRecebimento;
    }

    public void setNumeroDiasLimiteRecebimento(Integer numeroDiasLimiteRecebimento) {
        this.numeroDiasLimiteRecebimento = numeroDiasLimiteRecebimento;
    }

    public ObjetoJSONJurosMoraBancoBrasil getJurosMora() {
        return jurosMora;
    }

    public void setJurosMora(ObjetoJSONJurosMoraBancoBrasil jurosMora) {
        this.jurosMora = jurosMora;
    }

    public ObjetoJSONMultaBancoBrasil getMulta() {
        return multa;
    }

    public void setMulta(ObjetoJSONMultaBancoBrasil multa) {
        this.multa = multa;
    }
}
