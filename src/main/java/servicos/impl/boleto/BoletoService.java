package servicos.impl.boleto;

import br.com.pactosolucoes.autorizacaocobranca.dao.AutorizacaoCobrancaCliente;
import br.com.pactosolucoes.autorizacaocobranca.modelo.AutorizacaoCobrancaClienteVO;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoAutorizacaoCobrancaEnum;
import br.com.pactosolucoes.autorizacaocobranca.modelo.TipoObjetosCobrarEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import controle.financeiro.BoletoBancarioControle;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.*;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.enumerador.SituacaoRemessaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UtilReflection;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.ConfiguracaoSistema;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.MovParcela;
import negocio.facade.jdbc.financeiro.MovProdutoParcela;
import negocio.facade.jdbc.financeiro.Remessa;
import negocio.facade.jdbc.financeiro.RemessaItem;
import negocio.interfaces.arquitetura.UsuarioInterfaceFacade;
import negocio.interfaces.basico.ClienteInterfaceFacade;
import negocio.interfaces.basico.ConfiguracaoSistemaInterfaceFacade;
import negocio.interfaces.basico.EmpresaInterfaceFacade;
import negocio.interfaces.financeiro.ConvenioCobrancaInterfaceFacade;
import negocio.interfaces.financeiro.MovParcelaInterfaceFacade;
import negocio.interfaces.financeiro.RemessaInterfaceFacade;
import negocio.interfaces.financeiro.RemessaItemInterfaceFacade;
import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRExporterParameter;
import net.sf.jasperreports.engine.JRGroup;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanArrayDataSource;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.util.JRLoader;
import org.jboleto.JBoleto;
import relatorio.negocio.comuns.financeiro.DescontoBoletoTO;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.interfaces.BoletoServiceInterface;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;
import servicos.propriedades.PropsService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.sql.Connection;
import java.text.DateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by ulisses on 24/05/2016
 */

public class BoletoService extends SuperEntidade implements BoletoServiceInterface {

    private MovParcelaInterfaceFacade movParcelaDAO;
    private MovProdutoParcela movProdutoParcelaDAO;
    private RemessaItemInterfaceFacade remessaItemDAO;
    private UsuarioInterfaceFacade usuarioInterfaceFacade;
    private EmpresaInterfaceFacade empresaDao;
    private ClienteInterfaceFacade clienteDao;
    private AutorizacaoCobrancaCliente autorizacaoCobrancaClienteDao;
    private RemessaInterfaceFacade remessaDAO;
    private ConvenioCobrancaInterfaceFacade convenioCobrancaDAO;
    private ConfiguracaoSistemaInterfaceFacade configuracaoSistemaDAO;
    private Integer sequencialArquivo;

    private static final String SUBDIRETORIO_BOLETO = "BOLETO";

    public BoletoService(Connection con) throws Exception {
        super(con);
    }

    public BoletoService() throws Exception {
        super();
    }

    private void criarDiretorioBoleto(){
        File arquivo = new File(getDiretorioArquivos());
        criarArquivo(arquivo);
        arquivo = new File(getDiretorioArquivos() + File.separator + SUBDIRETORIO_BOLETO);
        criarArquivo(arquivo);
        }

    private void criarArquivo(File arquivo) {
        if (!arquivo.exists()) {
            boolean criou = arquivo.mkdir();
            if (criou) {
                Uteis.logar(null, "Criou com sucesso o diretório de arquivos.");
            } else {
                Uteis.logar(null, "Não Criou diretório de arquivos.");
        }
    }
    }

    private String getNomeArquivoGerado(String key, Integer codigoItemRemessa) {
        return   SUBDIRETORIO_BOLETO + File.separator + "boleto_"+ key + "_"+ codigoItemRemessa + ".pdf";
    }

    private String getDiretorioArquivos() {
        return PropsService.getPropertyValue(PropsService.diretorioArquivos);
    }

    private String obterCaminhoRaizRelatorio(){
        File arq = new File(this.getClass().getResource(this.getClass().getSimpleName() + ".class").getFile());
        return arq.getParent().substring(0, arq.getParent().indexOf("classes")) + "classes";
    }


    private String getUrlArquivo(String key, Integer codigoItemRemessa) {
        criarDiretorioBoleto();
        File f = new File(getDiretorioArquivos() +  File.separator + getNomeArquivoGerado(key, codigoItemRemessa));
        return f.getAbsolutePath().replaceAll("\\\\", "\\\\\\\\");
    }

    private JasperPrint gerarRelatorioJasperPrintObjeto(HttpServletRequest request, HttpServletResponse response, String nomeDesignIReport) throws Exception {
        JRDataSource jr = new JRBeanArrayDataSource(((List) request.getAttribute("listaObjetos")).toArray(), false);
        String nomeJasperReportDesignIReport = nomeDesignIReport.substring(0, nomeDesignIReport.lastIndexOf(".")) + ".jasper";
        File arquivoIReport = new File(obterCaminhoRaizRelatorio() + File.separator + nomeJasperReportDesignIReport);
        JasperReport jasperReport = (JasperReport) JRLoader.loadObject(arquivoIReport);
        jasperReport.setProperty("net.sf.jasperreports.awt.ignore.missing.font", "true");
        jasperReport.setProperty("net.sf.jasperreports.default.pdf.font.name", "SansSerif");

        if (request.getAttribute("group_startnewpage") != null) {
            JRGroup[] groups = jasperReport.getGroups();
            if (groups != null) {
                for (JRGroup jRGroup : groups) {
                    Boolean b = (Boolean) request.getAttribute("group_startnewpage");
                    jRGroup.setStartNewPage(b);
                }
            }
        }

        return JasperFillManager.fillReport(jasperReport, (Map) request.getAttribute("parametrosRelatorio"), jr);
    }


    private JasperPrint gerarRelatorioJasperPrintObjeto(BoletoBancarioControle controle, Map<String, Object> params) throws Exception {
        List lista = (List) params.get("listaObjetos");
        JRDataSource jr = new JRBeanArrayDataSource((lista).toArray(), false);
        String nomeJasperReportDesignIReport = controle.getDesignIReportRelatorio().substring(0, controle.getDesignIReportRelatorio().lastIndexOf(".")) + ".jasper";
        File arquivoIReport = new File(obterCaminhoRaizRelatorio() + File.separator + nomeJasperReportDesignIReport);
        JasperReport jasperReport = (JasperReport) JRLoader.loadObject(arquivoIReport);
        jasperReport.setProperty("net.sf.jasperreports.awt.ignore.missing.font", "true");
        jasperReport.setProperty("net.sf.jasperreports.default.pdf.font.name", "SansSerif");

        return JasperFillManager.fillReport(jasperReport, (Map) params, jr);
    }

    private boolean existePdfBoleto(String key, Integer  codigoItemRemessa){
        File pdfFile = new File(getUrlArquivo(key, codigoItemRemessa));
        return pdfFile.exists();
    }

    private String gerarBoletoPDF(BoletoBancarioControle controle, Map<String, Object> params, String key, Integer  codigoItemRemessa) throws Exception {
        JasperPrint print = gerarRelatorioJasperPrintObjeto(controle, params);
        File pdfFile = new File(getUrlArquivo(key, codigoItemRemessa));
        if (pdfFile.exists()) {
            try {
                pdfFile.delete();
            } catch (Exception e) {
                DateFormat formatador = DateFormat.getDateInstance(DateFormat.MEDIUM);
                String dataStr = formatador.format(negocio.comuns.utilitarias.Calendario.hoje());
                pdfFile = new File(getUrlArquivo(key, codigoItemRemessa));
            }

        }

        JRPdfExporter jrpdfexporter = new JRPdfExporter();
        jrpdfexporter.setParameter(JRExporterParameter.JASPER_PRINT, print);
        jrpdfexporter.setParameter(JRExporterParameter.OUTPUT_FILE, pdfFile);
        System.out.println("EXPORTANDO PDF: " + pdfFile.getAbsolutePath());
        jrpdfexporter.exportReport();
        return getNomeArquivoGerado(key,  codigoItemRemessa);
    }

    private void validarAutorizacaoCobrancaBoletoCliente(BoletoBancarioControle controle, EmpresaVO empresaVO, Integer codigoPessoa)throws Exception{

        if (!controle.isExisteAutorizacaoBoleto(getCon())) {
            if (UtilReflection.objetoMaiorQueZero(empresaVO,"getConvenioBoletoPadrao().getCodigo()")){
                ClienteVO clienteVO = getClienteDao().consultarPorCodigoPessoa(codigoPessoa,Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
                AutorizacaoCobrancaClienteVO autorizacaoCobrancaClienteVO = new AutorizacaoCobrancaClienteVO();
                autorizacaoCobrancaClienteVO.setConvenio(empresaVO.getConvenioBoletoPadrao());
                autorizacaoCobrancaClienteVO.setTipoAutorizacao(TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO);
                autorizacaoCobrancaClienteVO.setCliente(clienteVO);
                autorizacaoCobrancaClienteVO.setTipoACobrar(TipoObjetosCobrarEnum.TUDO);

                getAutorizacaoCobrancaClienteDao().incluir(autorizacaoCobrancaClienteVO);
            }else{
                throw new ConsistirException("Operação não permitida. Não foi configurado o convênio de cobrança boleto padrão no cadastro de empresa.");
            }
        }


    }

    public String gerarBoletoParcelas(String key, List<MovParcelaVO> listaMovParcela, Integer codigoPessoa, boolean calcularMultaJuros, String requestURL)throws Exception{
        if ((listaMovParcela == null) || (listaMovParcela.size() <= 0)){
            throw new ConsistirException("Operação não permitida. O parâmetro listaMovParcela está vazia.");
        }
        MovParcelaVO movParcelaVOReferencia = listaMovParcela.get(0);
        for (MovParcelaVO movParcelaVO: listaMovParcela){
            movParcelaVO.setMovProdutoParcelaVOs(getMovProdutoParcelaDAO().consultarPorCodigoMovParcela(movParcelaVO.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSENTIDADESPRINCIPAIS));
        }
        EmpresaVO empresaVO = getEmpresaDao().consultarPorChavePrimaria(movParcelaVOReferencia.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        BoletoBancarioControle controle = new BoletoBancarioControle();
        UsuarioVO usuarioVO = getUsuarioInterfaceFacade().getUsuarioRecorrencia();
        List<MovParcelaVO> parcelasBoleto = new ArrayList<MovParcelaVO>();

        controle.setParcelaSacado(movParcelaVOReferencia);
        controle.setCalcularMultaJuros(calcularMultaJuros);
        // verifica se existe autorização de cobrança - tipo Boleto.
        validarAutorizacaoCobrancaBoletoCliente(controle,empresaVO,codigoPessoa);

        RemessaItemVO boleto = null;
        parcelasBoleto.addAll(listaMovParcela);
        controle.setParcelasBoleto(parcelasBoleto);
        controle.calcularDataVencimento();

        if (controle.isExisteAutorizacaoBoleto(getCon())) {
            controle.validarAutorizacaoCobrancaSacado();
            controle.calcularValorTitulo();
            controle.calcularMultaJuros();
            controle.verificarProcessarAgrupamentoBoletos();
            controle.gravarBoleto(usuarioVO, empresaVO);
            boleto = getRemessaItemDAO().consultarUltimoBoletoParcela(movParcelaVOReferencia);
        }

        RemessaItemVO boletoCompleto = getRemessaItemDAO().consultarPorChavePrimaria(boleto.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        controle.prepararBoletoParaImpressao(boleto);

        executarImprimirBoleto(controle,empresaVO,key,boletoCompleto.getCodigo());
        return  requestURL + "/UpdateServlet?op=downloadfilediretorioarquivos&file=" + getNomeArquivoGerado(key, boletoCompleto.getCodigo()) + "&mimetype=application/pdf";
    }

    public String gerarBoletoTodasParcelasDoContrato(String key, Integer codigoContrato, Integer codigoPessoa, boolean calcularMultaJuros, String requestURL)throws Exception{
        List<MovParcelaVO> listaMovParcela = getMovParcelaDAO().consultarPorContrato(codigoContrato, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        return gerarBoletoParcelas(key,listaMovParcela,codigoPessoa,calcularMultaJuros,requestURL);
    }

    public String gerarBoleto(String key, Integer codigoMovParcela, Integer codigoPessoa, boolean calcularMultaJuros, String requestURL)throws Exception{
        MovParcelaVO movParcelaVO = getMovParcelaDAO().consultarPorChavePrimaria(codigoMovParcela, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
        if (movParcelaVO == null){
            throw new ConsistirException("Não foi encontrada nenhuma parcela com o codigo: " + codigoMovParcela + " para a chave:" + key);
        }

        if (!movParcelaVO.getPessoa().getCodigo().equals(codigoPessoa)){
            throw new ConsistirException("O código da parcela " + codigoMovParcela + " não pertence a pessoa: " + codigoPessoa + " para a chave:" + key);
        }
        EmpresaVO empresaVO = getEmpresaDao().consultarPorChavePrimaria(movParcelaVO.getEmpresa().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);

        BoletoBancarioControle controle = new BoletoBancarioControle();
        UsuarioVO usuarioVO = getUsuarioInterfaceFacade().getUsuarioRecorrencia();
        List<MovParcelaVO> parcelasBoleto = new ArrayList<MovParcelaVO>();

        controle.setParcelaSacado(movParcelaVO);
        controle.setCalcularMultaJuros(calcularMultaJuros);
        // verifica se existe autorização de cobrança - tipo Boleto.
        validarAutorizacaoCobrancaBoletoCliente(controle,empresaVO,codigoPessoa);

        RemessaItemVO boleto = getRemessaItemDAO().consultarUltimoBoletoParcela(movParcelaVO);
        boolean gerarRemessa = (boleto == null || Calendario.menor(boleto.getDataVencimentoBoleto(), Calendario.hoje()));
        if ((!gerarRemessa) && (boleto != null)){
            if (getRemessaItemDAO().remessaItemVinculadaVariasParcelas(boleto)){
                gerarRemessa = true;
            }else{
                if (!existePdfBoleto(key,boleto.getCodigo())){
                    gerarRemessa = true;
                }
            }
        }
        if (gerarRemessa) {
            parcelasBoleto.add(movParcelaVO);
            controle.setParcelasBoleto(parcelasBoleto);
            controle.calcularDataVencimento();

            if (controle.isExisteAutorizacaoBoleto(getCon())) {
                controle.validarAutorizacaoCobrancaSacado();
                controle.calcularValorTitulo();
                controle.calcularMultaJuros();
                controle.verificarProcessarAgrupamentoBoletos();
                controle.gravarBoleto(usuarioVO, movParcelaVO.getEmpresa());
                boleto = getRemessaItemDAO().consultarUltimoBoletoParcela(movParcelaVO);
            }
        }else{
            return  requestURL + "/UpdateServlet?op=downloadfilediretorioarquivos&file=" + getNomeArquivoGerado(key, boleto.getCodigo()) + "&mimetype=application/pdf";
        }
        RemessaItemVO boletoCompleto = getRemessaItemDAO().consultarPorChavePrimaria(boleto.getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        controle.prepararBoletoParaImpressao(boleto);

        executarImprimirBoleto(controle,empresaVO,key,boletoCompleto.getCodigo());
        return  requestURL + "/UpdateServlet?op=downloadfilediretorioarquivos&file=" + getNomeArquivoGerado(key, boletoCompleto.getCodigo()) + "&mimetype=application/pdf";
    }

    public BoletoEmailTO executarImprimirBoletoMailingModeloPadrao(BoletoEmailTO boletoEmailTO, BoletoBancarioControle controle, EmpresaVO empresaVO, String key, Integer codigoItemRemessa)throws Exception{
        executarImprimirBoleto(controle, empresaVO, key, codigoItemRemessa);
        String urlBoleto = getUrlArquivo(key, codigoItemRemessa);
        boletoEmailTO.setUrlFileArquivoBoleto(urlBoleto);
        boletoEmailTO.setEmpresaVO(empresaVO);
        boletoEmailTO.setLinhaDigitavel(controle.getBoletos().get(0).getBoleto().getLinhaDigitavel());
        return boletoEmailTO;
    }

    public String executarImprimirBoletoMailing(BoletoBancarioControle controle, EmpresaVO empresaVO, String key, Integer codigoItemRemessa)throws Exception{
        executarImprimirBoleto(controle, empresaVO, key, codigoItemRemessa);
        return getUrlArquivo(key, codigoItemRemessa);
    }


    private void executarImprimirBoleto(BoletoBancarioControle controle, EmpresaVO empresaVO, String key, Integer codigoItemRemessa)throws Exception{
        controle.criarBoletos(controle.getItensImpressao(), key);
        imprimirRelatorio(controle, empresaVO, key, codigoItemRemessa);
    }

    private void imprimirRelatorio(BoletoBancarioControle controle, EmpresaVO empresaVO, String key, Integer  codigoItemRemessa) throws Exception {
        Map<String, Object> params = new HashMap<String, Object>();
        // prepara parâmetros
        prepareParams(params, controle, empresaVO);
        gerarBoletoPDF(controle,params,key, codigoItemRemessa);
    }


    private void prepareParams(Map<String, Object> params, BoletoBancarioControle controle, EmpresaVO empresaVO)throws Exception{

        params.put("nomeRelatorio", "Boleto");
        params.put("nomeEmpresa", empresaVO.getNome());

        params.put("tituloRelatorio", "Boleto");
        params.put("nomeDesignIReport", controle.getDesignIReportRelatorio());
        params.put("nomeDesignSubReport", controle.getDesignSubReportRelatorio());
        params.put("nomeUsuario", "admin");

        List<AuxiliarBoletoBancarioTO> auxiliarBoletoBancarioTOs = new ArrayList<AuxiliarBoletoBancarioTO>();
        for (JBoleto boleto : controle.getBoletos()) {
            AuxiliarBoletoBancarioTO auxiliar = new AuxiliarBoletoBancarioTO();
            auxiliar.setBoleto(boleto);
            auxiliarBoletoBancarioTOs.add(auxiliar);
        }

        params.put("listaObjetos", auxiliarBoletoBancarioTOs);

        StringBuilder sb = controle.getEnderecoEmpresa(empresaVO);

        params.put("enderecoEmpresa", sb.toString());
        params.put("cnpjEmpresa", empresaVO.getCNPJ());
        params.put("cidadeEmpresa", empresaVO.getCidade().getNome());
        params.put("SUBREPORT_DIR", controle.getCaminhoSubRelatorio());

        byte[] propaganda = getEmpresaDao().obterFoto(
                DAO.resolveKeyFromConnection(getEmpresaDao().getCon()),
                empresaVO.getCodigo(), MidiaEntidadeEnum.FOTO_EMPRESA_PROPAGANDA_BOLETO);
        InputStream fs = null;
        if (propaganda != null)
            fs = new ByteArrayInputStream(propaganda);
        params.put("propaganda", fs);
    }

    public RemessaVO obterRemessaBoleto(ConvenioCobrancaVO convenio, EmpresaVO empresaVO, UsuarioVO usuarioVO) throws Exception {
        RemessaVO remessa = getRemessaDAO().consultarRemessaAberta(convenio.getCodigo(), empresaVO.getCodigo(), usuarioVO);

        //Consultar remessa dia
        if (remessa == null) {
            remessa = new RemessaVO();
            remessa.setUsuario(usuarioVO);
            remessa.setConvenioCobranca(convenio);
            remessa.setTipo(convenio.getTipo().getTipoRemessa());
            remessa.setDataRegistro(Calendario.hoje());
            remessa.setDataInicio(Calendario.hoje());
            remessa.setEmpresaVO(empresaVO);
            remessa.setEmpresa(empresaVO.getCodigo());
            if (convenio.getUsarSequencialUnico()) {
                sequencialArquivo = getConfiguracaoSistemaDAO().incrementarSequencialArquivo();
                remessa.getProps().put(DCCAttEnum.NumeroResumoOperacoes.name(), sequencialArquivo.toString());
                convenio.setSequencialDoArquivo(sequencialArquivo);
                remessa.gerarIdentificador(con, sequencialArquivo);
            } else {
                if (convenio.getBanco().getCodigoBanco() == JBoleto.DAYCOVAL) {
                    Integer sequencial = getRemessaDAO().consultarOrdemDaycoval(Calendario.hoje(), convenio.getCodigo());;
                    remessa.gerarIdentificador(con, sequencial);
                } else {
                    remessa.gerarIdentificador(con);
                }
                if (convenio.getBanco().getCodigoBanco() == JBoleto.SICREDI) {
                    sequencialArquivo = convenio.getSequencialDoArquivo();
                    remessa.setSequencialArquivo(sequencialArquivo);
                    remessa.getProps().put(DCCAttEnum.NumeroResumoOperacoes.name(), sequencialArquivo.toString());
                }
            }

            remessa.setSituacaoRemessa(SituacaoRemessaEnum.GERADA);
            getRemessaDAO().incluir(remessa);

            if (!convenio.getUsarSequencialUnico()) {
                getConvenioCobrancaDAO().incrementarSequencialArquivo(convenio);
            }
        }

        return remessa;
    }

    public RemessaItemVO adicionarBoletoRemessa(BoletoBancarioTO boleto, RemessaVO remessaVO) throws Exception {
        RemessaItemVO item = new RemessaItemVO();

        ConvenioCobrancaVO convenio = remessaVO.getConvenioCobranca();

        boolean achou = false;
        List<AutorizacaoCobrancaClienteVO> autos = getFacade().getAutorizacaoCobrancaCliente().consultarPorPessoa(boleto.getSacado().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS);
        PessoaVO pessoa = getFacade().getPessoa().consultarPorChavePrimaria(boleto.getSacado().getCodigo(),Uteis.NIVELMONTARDADOS_TODOS);
        ClienteVO cliente = getFacade().getCliente().consultarPorCodigoPessoa(boleto.getSacado().getCodigo(),Uteis.NIVELMONTARDADOS_DADOSENTIDADESUBORDINADAS);
        for (AutorizacaoCobrancaClienteVO auto : autos) {
            if (TipoAutorizacaoCobrancaEnum.BOLETO_BANCARIO.equals(auto.getTipoAutorizacao())) {
                boolean compativel = false;
                if (convenio.getBanco() != null) {
                    compativel = Uteis.enumIn(convenio.getTipo(), auto.getTipoAutorizacao().getTiposConvenio());
                }
                if (compativel) {

                    List<MovParcelaVO> parcelasBoleto = new ArrayList<MovParcelaVO>(boleto.getParcelas().size());
                    for (MovParcelaVO parcela : boleto.getParcelas()) {
                        parcela.setTipoProdutos(getFacade().getMovParcela().consultaTiposProdutosMovParcela(parcela.getCodigo()));
                        if (auto.isParcelaCompativel(parcela, false, this.con)) {
                            achou = true;
                            RemessaItemMovParcelaVO itemMovParcelaVO = new RemessaItemMovParcelaVO();
                            itemMovParcelaVO.getMovParcelaVO().setCodigo(parcela.getCodigo());
                            itemMovParcelaVO.getMovParcelaVO().setValorParcela(parcela.getValorParcela());
                            itemMovParcelaVO.getMovParcelaVO().setPessoa(parcela.getPessoa());
                            itemMovParcelaVO.setValorOriginal(parcela.getValorParcela());
                            item.getMovParcelas().add(itemMovParcelaVO);
                            parcelasBoleto.add(parcela);
                        }
                    }
                    item.setPessoa(pessoa);
                    item.setClienteVO(cliente);
                    item.setRemessa(remessaVO);
                    item.setValorItemRemessa(boleto.getValorBoleto());
                    item.setDataVencimentoBoleto(boleto.getDataVencimento());
                    item.setTipo(remessaVO.getTipo());

                    auto.preencherRemessaItemSegundoAutorizacao(item, this.con);
                    if (!parcelasBoleto.isEmpty()) {
                        DescontoBoletoTO desconto = getFacade().getCliente().descobrirDescontoBoletoParaCliente(parcelasBoleto);
                        if (desconto != null && convenio.getDescontoBoleto() == 0.0) {
                            item.setDiaDoMesDescontoBoletoPagAntecipado(desconto.getDiaMaximoPagamentoDesconto());
                            item.setPorcentagemDescontoBoletoPagAntecipado(desconto.getPorcentagemDesconto());
                            item.setValorDescontoBoletoPagAntecipado(desconto.getValorDesconto());
                        }else if(item.possuiDescontoConvenio()){
                            item.setPorcentagemDescontoBoleto(remessaVO.getConvenioCobranca().getDescontoBoleto());
                        }
                    }
                    getRemessaItemDAO().incluir(item);
                    remessaVO.getListaItens().add(item);
                    break;
                }
            }
        }

        if (!achou) {
            throw new ConsistirException(String.format("Aluno \"%s\", "
                            + "não possui autorização compatível com a parcela. Verifique o Cadastro de Autorizações de Cobrança.",
                    boleto.getSacado().getNome()));
        }

        remessaVO.setSequencialArquivo(sequencialArquivo);
        LayoutRemessaBase.preencherArquivoRemessa(remessaVO);
        getRemessaDAO().alterar(remessaVO);

        return item;
    }


    public MovParcelaInterfaceFacade getMovParcelaDAO() throws Exception {
        if (this.movParcelaDAO == null)
            this.movParcelaDAO = new MovParcela(con);
        this.movParcelaDAO.setCon(con);
        return movParcelaDAO;
    }

    public void setMovParcelaDAO(MovParcelaInterfaceFacade movParcelaDAO) {
        this.movParcelaDAO = movParcelaDAO;
    }

    public MovProdutoParcela getMovProdutoParcelaDAO() throws Exception {
        if (this.movProdutoParcelaDAO == null)
            this.movProdutoParcelaDAO = new MovProdutoParcela(con);
        this.movProdutoParcelaDAO.setCon(con);
        return movProdutoParcelaDAO;
    }

    public RemessaItemInterfaceFacade getRemessaItemDAO()throws Exception  {
        if (this.remessaItemDAO == null)
            this.remessaItemDAO = new RemessaItem(con);
        this.remessaItemDAO.setCon(con);
        return remessaItemDAO;
    }

    public UsuarioInterfaceFacade getUsuarioInterfaceFacade()throws Exception  {
        if (this.usuarioInterfaceFacade == null)
            this.usuarioInterfaceFacade = new Usuario(con);
        this.usuarioInterfaceFacade.setCon(con);
        return usuarioInterfaceFacade;
    }

    public void setRemessaItemDAO(RemessaItemInterfaceFacade remessaItemDAO) {
        this.remessaItemDAO = remessaItemDAO;
    }

    public void setMovProdutoParcelaDAO(MovProdutoParcela movProdutoParcelaDAO) {
        this.movProdutoParcelaDAO = movProdutoParcelaDAO;
    }

    public void setUsuarioInterfaceFacade(UsuarioInterfaceFacade usuarioInterfaceFacade) {
        this.usuarioInterfaceFacade = usuarioInterfaceFacade;
    }

    public EmpresaInterfaceFacade getEmpresaDao() throws Exception{
        if (this.empresaDao == null)
            this.empresaDao = new Empresa(con);
        this.empresaDao.setCon(con);

        return empresaDao;
    }

    public void setEmpresaDao(EmpresaInterfaceFacade empresaDao) {
        this.empresaDao = empresaDao;
    }

    public ClienteInterfaceFacade getClienteDao() throws Exception{
        if (this.clienteDao == null)
            this.clienteDao = new Cliente(con);
        this.clienteDao.setCon(con);
        return clienteDao;
    }

    public void setClienteDao(ClienteInterfaceFacade clienteDao){
        this.clienteDao = clienteDao;
    }

    public AutorizacaoCobrancaCliente getAutorizacaoCobrancaClienteDao() throws Exception {
        if (this.autorizacaoCobrancaClienteDao == null)
            this.autorizacaoCobrancaClienteDao = new AutorizacaoCobrancaCliente(con);
        this.autorizacaoCobrancaClienteDao.setCon(con);
        return autorizacaoCobrancaClienteDao;
    }

    public void setAutorizacaoCobrancaClienteDao(AutorizacaoCobrancaCliente autorizacaoCobrancaClienteDao){

        this.autorizacaoCobrancaClienteDao = autorizacaoCobrancaClienteDao;
    }

    public RemessaInterfaceFacade getRemessaDAO() throws Exception {
        if (remessaDAO == null) {
            remessaDAO = new Remessa(con);
}
        remessaDAO.setCon(con);
        return remessaDAO;
    }

    public void setRemessaDAO(RemessaInterfaceFacade remessaDAO) {
        this.remessaDAO = remessaDAO;
    }

    public ConvenioCobrancaInterfaceFacade getConvenioCobrancaDAO() throws Exception {
        if (convenioCobrancaDAO == null) {
            convenioCobrancaDAO = new ConvenioCobranca(con);
        }
        convenioCobrancaDAO.setCon(con);
        return convenioCobrancaDAO;
    }

    public void setConvenioCobrancaDAO(ConvenioCobrancaInterfaceFacade convenioCobrancaDAO) {
        this.convenioCobrancaDAO = convenioCobrancaDAO;
    }

    public ConfiguracaoSistemaInterfaceFacade getConfiguracaoSistemaDAO() throws Exception {
        if (configuracaoSistemaDAO == null) {
            configuracaoSistemaDAO = new ConfiguracaoSistema(con);
        }
        configuracaoSistemaDAO.setCon(con);
        return configuracaoSistemaDAO;
    }

    public void setConfiguracaoSistemaDAO(ConfiguracaoSistemaInterfaceFacade configuracaoSistemaDAO) {
        this.configuracaoSistemaDAO = configuracaoSistemaDAO;
    }

    public Integer getSequencialArquivo() {
        return sequencialArquivo;
    }

    public void setSequencialArquivo(Integer sequencialArquivo) {
        this.sequencialArquivo = sequencialArquivo;
    }
}
