package servicos.impl.boleto.caixa;

/**
 * Created by <PERSON><PERSON><PERSON> on 09/04/2024
 */
public enum TipoRetornoConsultaCaixaEnum {

    EM_ABERTO(1, "EM ABERTO", "Boleto gerado, aguardando pagamento."),
    BAIXA_POR_DEVOLUCAO(2, "BAIXA POR DEVOLUCAO", "Não conheço cenário, adicionei por estar na documentação."),
    BAIXA_POR_ESTORNO(3, "BAIXA POR ESTORNO", "Não conheço cenário, adicionei por estar na documentação."),
    BAIXA_POR_PROTESTO(4, "BAIXA POR PROTESTO", "Não conheço cenário, adicionei por estar na documentação."),
    ENVIADO_AO_CARTORIO(5, "ENVIADO AO CARTORIO", "Não conheço cenário, adicionei por estar na documentação."),
    LIQUIDADO(6, "LIQUIDADO", "Boleto pago em algum dia anterior ao dia da consulta na api."),
    LIQUIDADO_NO_CARTORIO(7, "LIQUIDADO NO CARTORIO", "Não conheço cenário, adicionei por estar na documentação."),
    SOMENTE_PARA_PROTESTO(8, "SOMENTE PARA PROTESTO", "Não conheço cenário, adicionei por estar na documentação."),
    SUSTADO_CARTORIO(9, "SUSTADO CARTORIO", "Não conheço cenário, adicionei por estar na documentação."),
    TITULO_JA_PAGO_NO_DIA(10, "TITULO JA PAGO NO DIA", "Boleto pago no dia da consulta na api.")
    ;

    private int codigo;
    private String descricao;
    private String explicacao;

    TipoRetornoConsultaCaixaEnum(int codigo, String descricao, String explicacao) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.explicacao = explicacao;
    }

    public static String consultarDescricaoPorCodigo(int codigo){
        for (TipoRetornoConsultaCaixaEnum o : TipoRetornoConsultaCaixaEnum.values() ) {
            if(o.codigo == codigo){
                return o.descricao;
            }
        }
        return "";
    }

    public static TipoRetornoConsultaCaixaEnum consultarPorCodigo(int codigo){
        for (TipoRetornoConsultaCaixaEnum bancoEnum: TipoRetornoConsultaCaixaEnum.values()){
            if (bancoEnum.getCodigo() == codigo){
                return bancoEnum;
            }
        }
        return null;
    }

    public static String consultarExplicacaoPorCodigo(int codigo){
        for (TipoRetornoConsultaCaixaEnum o : TipoRetornoConsultaCaixaEnum.values() ) {
            if(o.codigo == codigo){
                return o.explicacao;
            }
        }
        return "";
    }

    public static TipoRetornoConsultaCaixaEnum obterPorDescricaoContains(String msg) {
        if (msg.contains("EM ABERTO")) {
            return TipoRetornoConsultaCaixaEnum.EM_ABERTO;
        } else if (msg.contains("BAIXA POR DEVOLUCAO") || msg.contains("BAIXADO POR DEVOLUCAO")) {
            return TipoRetornoConsultaCaixaEnum.BAIXA_POR_DEVOLUCAO;
        } else if (msg.contains("BAIXA POR ESTORNO") || msg.contains("BAIXADO POR ESTORNO")) {
            return TipoRetornoConsultaCaixaEnum.BAIXA_POR_ESTORNO;
        } else if (msg.contains("BAIXA POR PROTESTO") || msg.contains("BAIXADO POR PROTESTO")) {
            return TipoRetornoConsultaCaixaEnum.BAIXA_POR_PROTESTO;
        } else if (msg.contains("ENVIADO AO CARTORIO")) {
            return TipoRetornoConsultaCaixaEnum.ENVIADO_AO_CARTORIO;
        } else if (msg.contains("LIQUIDADO")) {
            return TipoRetornoConsultaCaixaEnum.LIQUIDADO;
        } else if (msg.contains("LIQUIDADO NO CARTORIO")) {
            return TipoRetornoConsultaCaixaEnum.LIQUIDADO_NO_CARTORIO;
        } else if (msg.contains("SOMENTE PARA PROTESTO")) {
            return TipoRetornoConsultaCaixaEnum.SOMENTE_PARA_PROTESTO;
        } else if (msg.contains("SUSTADO CARTORIO")) {
            return TipoRetornoConsultaCaixaEnum.SUSTADO_CARTORIO;
        } else if (msg.contains("TITULO JA PAGO NO DIA")) {
            return TipoRetornoConsultaCaixaEnum.TITULO_JA_PAGO_NO_DIA;
        } else {
            return null;
        }
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public boolean getSituacaoCancelado() {
        if (this.codigo == 3 || this.codigo == 4 || this.codigo == 2) {
            return true;
        }
        return false;
    }
}
