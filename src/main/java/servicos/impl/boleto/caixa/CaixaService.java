package servicos.impl.boleto.caixa;

import br.com.pactosolucoes.comuns.to.BoletoOnlineTO;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PessoaCPFTO;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoConvenioCobranca;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import org.json.JSONObject;
import org.json.XML;
import servicos.http.RequestHttpService;
import servicos.impl.boleto.AbstractBoletoOnlineServiceComum;
import servicos.impl.boleto.AtributoBoletoEnum;
import servicos.interfaces.BoletoOnlineServiceInterface;
import servicos.propriedades.PropsService;
import sun.misc.BASE64Encoder;

import java.io.InputStream;
import java.math.BigDecimal;
import java.security.MessageDigest;
import java.sql.Connection;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.Calendar;
import java.util.Date;
import java.net.URL;
import java.net.HttpURLConnection;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.GZIPInputStream;
import java.util.zip.InflaterInputStream;

/**
 * Created with IntelliJ IDEA.
 * User: Maurin Noleto
 * Date: 01/07/2024
 */
public class CaixaService extends AbstractBoletoOnlineServiceComum implements BoletoOnlineServiceInterface {

    private String chaveBanco;
    private Boleto boletoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private Pessoa pessoaDAO;
    private Cliente clienteDAO;
    private Cidade cidadeDAO;
    private Empresa empresaDAO;
    private ConvenioCobrancaVO convenioCobrancaVO;
    private boolean tentarCancelarBoletosCaixaSemRegistro = false;

    public CaixaService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        inicializarDAO(con);
        this.convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
    }

    public CaixaService(Connection con) throws Exception {
        super(con);
    }

    private void inicializarDAO(Connection con) throws Exception {
        this.chaveBanco = DAO.resolveKeyFromConnection(con);
        this.boletoDAO = new Boleto(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.cidadeDAO = new Cidade(con);
        this.empresaDAO = new Empresa(con);
    }

    private String obterAutenticacao(BoletoVO boletoVO, TipoOperacaoCaixaEnum tipoOperacaoCaixaEnum) throws Exception {
        StringBuilder autenticacao = new StringBuilder();

        String beneficiario = obterCodigoBeneficiario(boletoVO.getConvenioCobrancaVO().getNumeroContrato());
        autenticacao.append(beneficiario);

        String nossoNumero = obterNossoNumeroPadraoCaixa(boletoVO.getNossoNumero());
        autenticacao.append(nossoNumero);

        if (tipoOperacaoCaixaEnum.getDescricao().equals(TipoOperacaoCaixaEnum.INCLUI_BOLETO.getDescricao())) {
            String dataVencimento = obterDataVencimento(boletoVO.getDataVencimento());
            autenticacao.append(dataVencimento);
        } else if (tipoOperacaoCaixaEnum.getDescricao().equals(TipoOperacaoCaixaEnum.BAIXA_BOLETO.getDescricao()) ||
                tipoOperacaoCaixaEnum.getDescricao().equals(TipoOperacaoCaixaEnum.CONSULTA_BOLETO.getDescricao())) {
            autenticacao.append("00000000");
        }

        if (tipoOperacaoCaixaEnum.getDescricao().equals(TipoOperacaoCaixaEnum.INCLUI_BOLETO.getDescricao())) {
            String valor = obterValor(boletoVO.getValor());
            autenticacao.append(valor);
        } else if (tipoOperacaoCaixaEnum.getDescricao().equals(TipoOperacaoCaixaEnum.BAIXA_BOLETO.getDescricao()) ||
                tipoOperacaoCaixaEnum.getDescricao().equals(TipoOperacaoCaixaEnum.CONSULTA_BOLETO.getDescricao())) {
            autenticacao.append("000000000000000");
        }

        String cnpj = obterCNPJOuCPFApenasNumeros(boletoVO.getConvenioCobrancaVO().getCnpj());
        autenticacao.append(cnpj);

        if (autenticacao.length() != 61) {
            throw new Exception("Autenticação contém menos de 61 caracteres.");
        }

        String autenticacaoBase64 = obterHashBase64(autenticacao.toString());
        if (autenticacaoBase64 == null) {
            throw new Exception("Erro ao converter Autenticação para Base64.");
        }

        return autenticacaoBase64;
    }

    private String obterCodigoBeneficiario(String numeroContrato) {
        // Verifica se a string tem menos de 7 caracteres
        if (numeroContrato.length() < 7) {
            // Adiciona "0" à esquerda até que a string tenha 7 caracteres
            numeroContrato = String.format("%7s", numeroContrato).replace(' ', '0');
        }
        return numeroContrato;
    }

    private String obterNossoNumeroPadraoCaixa(String nossoNumero) {
        // Nosso Número precisa ter 17 Caracteres, iniciando por 14
        // Verifica se a string tem menos de 15 caracteres
        if (nossoNumero.length() < 15) {
            // Adiciona "0" à esquerda até que a string tenha 15 caracteres
            nossoNumero = String.format("%15s", nossoNumero).replace(' ', '0');
        }
        // Adiciona o 14 exigido pela Caixa
        nossoNumero = "14" + nossoNumero;
        return nossoNumero;
    }

    private String obterDataVencimento(Date dataVencimento) {
        String dataVencimentoString = "";
        SimpleDateFormat formatador = new SimpleDateFormat("ddMMyyyy");
        dataVencimentoString = formatador.format(dataVencimento);
        return dataVencimentoString;
    }

    private String obterValor(Double valor) {
        String valorString = "";

        // Multiplicando o valor por 100, para pegar as duas últimas casas decimais.
        double valorMultiplicado = valor * 100;

        // Arredondando o valor para o inteiro mais próximo, para evitar problemas de faltar 0,01 centavo do valor do boleto para o sistema.
        int valorArredondado = (int) Math.round(valorMultiplicado);

        // Converte o resultado para String
        valorString = String.valueOf(valorArredondado);

        // Verifica se a String tem menos de 15 caracteres
        if (valorString.length() < 15) {
            // Adiciona "0" à esquerda até que a string tenha 15 caracteres
            valorString = String.format("%15s", valorString).replace(' ', '0');
        }
        return valorString;
    }

    private String obterCNPJOuCPFApenasNumeros(String valor) {
        // Removendo espaços, pontos e barras, mantendo apenas os dígitos
        valor = valor.replaceAll("[^\\d]", "");
        return valor;
    }

    private String obterHashBase64(String autenticacao) {
        MessageDigest MD;
        byte[] HASH;
        try {
            MD = MessageDigest.getInstance("SHA-256");
            HASH = MD.digest(autenticacao.getBytes("ISO8859-1"));
            BASE64Encoder enc = new BASE64Encoder();
            return enc.encode(HASH);
        } catch (Exception EX) {
            return null;
        }
    }

    @Override
    public BoletoVO criar(BoletoOnlineTO boletoOnlineTO) throws Exception {
        BoletoVO boletoVO = null;
        RequestHttpService service = null;
        try {
            boletoVO = criarBoletoVO(boletoOnlineTO, this.convenioCobrancaVO, this.getCon());
            boletoVO.setSituacao(SituacaoBoletoEnum.GERADO);
            if (boletoOnlineTO.isVerificarBoletoExistente()) {
                BoletoVO boletoExistenteVO = verificarBoletoExistente(boletoVO);
                if (boletoExistenteVO != null && !UteisValidacao.emptyNumber(boletoExistenteVO.getCodigo()) &&
                        !UteisValidacao.emptyString(boletoExistenteVO.getCodigoBarrasNumerico())) {
                    return boletoExistenteVO;
                } else if (boletoExistenteVO != null && !UteisValidacao.emptyNumber(boletoExistenteVO.getCodigo()) &&
                        UteisValidacao.emptyString(boletoExistenteVO.getCodigoBarrasNumerico()) &&
                        boletoExistenteVO.getSituacao().equals(SituacaoBoletoEnum.GERADO) &&
                        !UteisValidacao.emptyNumber(boletoExistenteVO.getConvenioCobrancaVO().getCodigo())) {
                    boletoVO = boletoExistenteVO;
                    boletoVO.setConvenioCobrancaVO(this.convenioCobrancaDAO.consultarPorChavePrimaria(boletoExistenteVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_TODOS));
                }
            }

            EmpresaVO empresaVO = this.empresaDAO.consultarPorChavePrimaria(boletoVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            PessoaCPFTO pessoaCPFTO = this.boletoDAO.obterDadosPessoaPagador(empresaVO.getCodigo(), boletoVO.getPessoaVO(), true, true);
            PessoaVO pessoaVO = this.pessoaDAO.consultarPorChavePrimaria(pessoaCPFTO.getPessoa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            CidadeVO cidadeVO = new CidadeVO();
            if (!UteisValidacao.emptyNumber(pessoaVO.getCidade().getCodigo())) {
                cidadeVO = this.cidadeDAO.consultarPorChavePrimaria(pessoaVO.getCidade().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            EnderecoVO enderecoVO = obterEnderecoVO(pessoaVO);

            String nossoNumero = obterNossoNumeroPadraoCaixa(boletoVO);
            boletoVO.setNossoNumero(nossoNumero);

            validarDados(boletoVO);
            validarDadosObrigatorioPagador(pessoaCPFTO, cidadeVO, enderecoVO);

            // Se não tem código é porquê não foi encontrado o boleto, então inclui.
            if (UteisValidacao.emptyNumber(boletoVO.getCodigo())) {
                this.boletoDAO.incluir(boletoVO);
            }

            // Se o boleto não estiver marcado para Registrar Agora, retorna o boleto sem registrar.
            if (!boletoOnlineTO.isRegistrarBoletoAgora()) {
                return boletoVO;
            }

            String xmlInclui = obterXMLInclui(boletoVO, pessoaCPFTO, enderecoVO, cidadeVO, pessoaVO, empresaVO);
            boletoVO.setParamsEnvio(xmlInclui);
            boletoVO.setIdExterno(boletoVO.getNossoNumero());

            URL url = new URL(PropsService.getPropertyValue(PropsService.urlApiCaixaManutencao));
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            OutputStream outputStream = null;

            try {
                connection.setRequestMethod("POST");
                connection.setDoOutput(true);
                connection.setRequestProperty("SOAPAction", "IncluiBoleto");
                connection.setRequestProperty("Content-Type", "text/xml;charset=UTF-8");
                connection.setRequestProperty("Accept-Encoding","gzip,deflate");

                outputStream = connection.getOutputStream();

                byte[] b = xmlInclui.getBytes("UTF-8");
                outputStream.write(b);
                outputStream.flush();

                int responseCode = connection.getResponseCode();

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    InputStream inputStream = connection.getInputStream();

                    // Verifica se a resposta está compactada
                    String contentEncoding = connection.getContentEncoding();
                    if ("gzip".equalsIgnoreCase(contentEncoding)) {
                        inputStream = new GZIPInputStream(inputStream);
                    } else if ("deflate".equalsIgnoreCase(contentEncoding)) {
                        inputStream = new InflaterInputStream(inputStream);
                    }

                    // Agora lê o conteúdo descomprimido
                    BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
                    String line;
                    StringBuilder response = new StringBuilder();
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();

                    // Resposta completa para verificação do conteúdo e analise do desenvolvimento.
                    boletoVO.setParamsResposta(response.toString());

                    // Quanto tem erro interno na Caixa, continua retornando 200, mas com essa tag <MENSAGENS>, então precisa dessa parte para pegar a Mensagem de Erro para exibir ao usuário.
                    if (response.toString().contains("<MENSAGENS>")) {
                        System.out.println("BOLETO REGISTRADO COM ERRO.");
                        // Encontra o índice inicial do conteúdo dentro de <MENSAGENS>
                        int inicio = response.toString().indexOf("<MENSAGENS>") + "<MENSAGENS>".length();
                        // Encontra o índice final do conteúdo dentro de </MENSAGENS>
                        int fim = response.toString().indexOf("</MENSAGENS>");

                        String msg = response.toString().substring(inicio, fim);
                        if (UteisValidacao.emptyString(msg) || !msg.contains("<RETORNO>(0) OPERACAO EFETUADA</RETORNO>")) { //Tanto o retorndo do Inclui como Baixa tem o mesmo retorno.
                            if (msg.contains("HASH DIVERGENTE")) {
                                msg = "Boleto não pode ser gerado. Validar dados do convênio.";
                            }
                            throw new Exception(msg);
                        }
                    }

                    JSONObject jsonRetorno = XML.toJSONObject(response.toString());
                    JSONObject jsonEnvelope = jsonRetorno.getJSONObject("soapenv:Envelope");
                    JSONObject jsonBody = jsonEnvelope.getJSONObject("soapenv:Body");
                    JSONObject jsonManutencaoCobranca = jsonBody.getJSONObject("manutencaocobrancabancaria:SERVICO_SAIDA");
                    JSONObject jsonDados = jsonManutencaoCobranca.getJSONObject("DADOS");
                    JSONObject jsonIncluiBoleto = jsonDados.getJSONObject("INCLUI_BOLETO");

                    // Se não tem erro, nessa parte vai pegar os dados de sucesso.
                    System.out.println("BOLETO REGISTRADO COM SUCESSO.");
                    boletoVO.setLinhaDigitavel(jsonIncluiBoleto.getString("LINHA_DIGITAVEL"));
                    boletoVO.setLinkBoleto(jsonIncluiBoleto.getString("URL"));
                    boletoVO.setSituacao(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO);
                    boletoVO.setParamsResposta(response.toString());

                } else {
                    System.out.println("Erro na requisição: HTTP code " + responseCode);
                }
            } catch (Exception ex) {
                boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.msg_erro, ex.getMessage());
                throw ex;
            } finally {
                outputStream.close();
                connection.getInputStream().close();
            }

            this.boletoDAO.alterar(boletoVO);
            return boletoVO;
        } catch (Exception ex) {
            ex.printStackTrace();
            boletoVO.setSituacao(SituacaoBoletoEnum.ERRO);
            marcarBoletoComErro(boletoVO, ex);
            throw ex;
        } finally {
            service = null;
        }
    }

    public String obterNossoNumeroPadraoCaixa(BoletoVO boletoVO) {
        return StringUtilities.formatarCampo(new BigDecimal(boletoVO.getIdentificador().toString()), 8);
    }

    public String obterXMLInclui(BoletoVO boletoVO, PessoaCPFTO pessoaCPFTO, EnderecoVO enderecoVO, CidadeVO cidadeVO, PessoaVO pessoaVO, EmpresaVO empresaVO) throws Exception {
        StringBuilder arquivoXML = new StringBuilder();
        arquivoXML.append("<?xml version=\"1.0\" encoding=\"utf-8\"?> \n");
        arquivoXML.append("<soapenv:Envelope \n" +
                "xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" \n" +
                "xmlns:ext=\"http://caixa.gov.br/sibar/manutencao_cobranca_bancaria/boleto/externo\" \n" +
                "xmlns:sib=\"http://caixa.gov.br/sibar\">"); //Exemplo página 31 um pouco diferente da Estrutura página 14, usar do Exemplo e se der errado, trocar para Estrutura.
        arquivoXML.append("<soapenv:Header/> \n");
        arquivoXML.append("<soapenv:Body> \n");
        arquivoXML.append("<ext:SERVICO_ENTRADA> \n");
        arquivoXML.append("<sib:HEADER> \n");
        arquivoXML.append("<VERSAO>3.0</VERSAO> \n"); //3.0 Boleto Comum, 3.2 Boleto Hibrido com Pix.
        arquivoXML.append("<AUTENTICACAO>").append(obterAutenticacao(boletoVO, TipoOperacaoCaixaEnum.INCLUI_BOLETO)).append("</AUTENTICACAO> \n");
        arquivoXML.append("<USUARIO_SERVICO>SGCBS02P</USUARIO_SERVICO> \n");
        arquivoXML.append("<OPERACAO>INCLUI_BOLETO</OPERACAO> \n");
        arquivoXML.append("<SISTEMA_ORIGEM>SIGCB</SISTEMA_ORIGEM> \n");
        arquivoXML.append("<UNIDADE>").append(obterAgencia(this.convenioCobrancaVO.getContaEmpresa().getAgencia())).append("</UNIDADE> \n");
        arquivoXML.append("<DATA_HORA>").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyyMMddHHmmss")).append("</DATA_HORA> \n");
        arquivoXML.append("</sib:HEADER> \n");
        arquivoXML.append("<DADOS> \n");
        arquivoXML.append("<INCLUI_BOLETO> \n");
        arquivoXML.append("<CODIGO_BENEFICIARIO>").append(obterCodigoBeneficiario(boletoVO.getConvenioCobrancaVO().getNumeroContrato())).append("</CODIGO_BENEFICIARIO> \n");
        arquivoXML.append("<TITULO> \n");
        arquivoXML.append("<NOSSO_NUMERO>").append(obterNossoNumeroPadraoCaixa(boletoVO.getNossoNumero())).append("</NOSSO_NUMERO> \n");
        arquivoXML.append("<NUMERO_DOCUMENTO>").append(boletoVO.getNossoNumero()).append("</NUMERO_DOCUMENTO> \n");
        arquivoXML.append("<DATA_VENCIMENTO>").append(Uteis.getDataAplicandoFormatacao(boletoVO.getDataVencimento(), "yyyy-MM-dd")).append("</DATA_VENCIMENTO> \n");
        arquivoXML.append("<VALOR>").append(obterValorIncluirBoleto(boletoVO.getValor())).append("</VALOR> \n");
        arquivoXML.append("<TIPO_ESPECIE>99</TIPO_ESPECIE> \n");
        arquivoXML.append("<FLAG_ACEITE>S</FLAG_ACEITE> \n");
        arquivoXML.append("<DATA_EMISSAO>").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyy-MM-dd")).append("</DATA_EMISSAO> \n");
        arquivoXML.append("<JUROS_MORA> \n");

        String tipoJurosIncluirBoleto = obterTipoJurosIncluirBoleto(boletoVO);
        arquivoXML.append("<TIPO>").append(tipoJurosIncluirBoleto).append("</TIPO> \n");
        if (tipoJurosIncluirBoleto.equals(TipoJurosCaixaEnum.ISENTO.getDescricao())) {
            arquivoXML.append("<VALOR>0.00</VALOR> \n");
        } else if (tipoJurosIncluirBoleto.equals(TipoJurosCaixaEnum.VALOR_POR_DIA.getDescricao()) || tipoJurosIncluirBoleto.equals(TipoJurosCaixaEnum.TAXA_MENSAL.getDescricao())) {
            arquivoXML.append("<DATA>").append(Uteis.getDataAplicandoFormatacao(Calendario.somarDias(boletoVO.getDataVencimento(), 1), "yyyy-MM-dd")).append("</DATA> \n");
            if (tipoJurosIncluirBoleto.equals(TipoJurosCaixaEnum.VALOR_POR_DIA.getDescricao())) {
                arquivoXML.append("<VALOR>").append(obterValorIncluirBoleto(boletoVO.getEmpresaVO().getJurosCobrancaAutomatica())).append("</VALOR> \n");
            } else if (tipoJurosIncluirBoleto.equals(TipoJurosCaixaEnum.TAXA_MENSAL.getDescricao())) {
                arquivoXML.append("<PERCENTUAL>").append(obterValorIncluirBoleto(boletoVO.getEmpresaVO().getJurosCobrancaAutomatica())).append("</PERCENTUAL> \n");
            }
        }

        arquivoXML.append("</JUROS_MORA> \n");
        arquivoXML.append("<POS_VENCIMENTO> \n");
        arquivoXML.append("<ACAO>DEVOLVER</ACAO> \n");
        arquivoXML.append("<NUMERO_DIAS>999</NUMERO_DIAS> \n");
        arquivoXML.append("</POS_VENCIMENTO> \n");
        arquivoXML.append("<CODIGO_MOEDA>9</CODIGO_MOEDA> \n");
        arquivoXML.append("<PAGADOR> \n");

        if (boletoVO.getPessoaVO().getCategoriaPessoa().equals(TipoPessoa.FISICA)) {
            arquivoXML.append("<CPF>").append(obterCNPJOuCPFApenasNumeros(pessoaCPFTO.getCpfResponsavel())).append("</CPF> \n");
            //Nome da pessoa é no máx. 40 caracteres, então pega os 40 primeiros caracteres.
            arquivoXML.append("<NOME>").append(Uteis.retirarAcentuacao(pessoaCPFTO.getNomeResponsavel()).
                    substring(0, Math.min(40, Uteis.retirarAcentuacao(pessoaCPFTO.getNomeResponsavel()).length()))) .append("</NOME> \n");
        } else if (boletoVO.getPessoaVO().getCategoriaPessoa().equals(TipoPessoa.JURIDICA)) {
            arquivoXML.append("<CNPJ>").append(obterCNPJOuCPFApenasNumeros(boletoVO.getPessoaVO().getCnpj())).append("</CNPJ> \n");
            arquivoXML.append("<RAZAO_SOCIAL>").append(Uteis.retirarAcentuacao(pessoaCPFTO.getNomeResponsavel())).append("</RAZAO_SOCIAL> \n");
        }

        String logradouro = Uteis.retirarAcentuacao(enderecoVO.getEndereco());
        String bairro = Uteis.retirarAcentuacao(enderecoVO.getBairro());
        String cidade = Uteis.retirarAcentuacao(cidadeVO.getNomeSemAcento());

        arquivoXML.append("<ENDERECO> \n");
        arquivoXML.append("<LOGRADOURO>").append(logradouro.length() > 39 ? logradouro.substring(0, 39) : logradouro).append("</LOGRADOURO> \n");
        arquivoXML.append("<BAIRRO>").append(bairro.length() > 14 ? bairro.substring(0, 14) : bairro).append("</BAIRRO> \n");
        arquivoXML.append("<CIDADE>").append(cidade.length() > 14 ? cidade.substring(0, 14) : cidade).append("</CIDADE> \n");
        arquivoXML.append("<UF>").append(Uteis.retirarAcentuacao(cidadeVO.getEstado().getSigla())).append("</UF> \n");
        arquivoXML.append("<CEP>").append(Uteis.retirarAcentuacao(obterCNPJOuCPFApenasNumeros(enderecoVO.getCep()))).append("</CEP> \n");
        arquivoXML.append("</ENDERECO> \n");
        arquivoXML.append("</PAGADOR> \n");

        if (boletoVO.getEmpresaVO().getMultaCobrancaAutomatica() > 0) {
            arquivoXML.append("<MULTA> \n");
            arquivoXML.append("<DATA>").append(Uteis.getDataAplicandoFormatacao(Calendario.somarDias(boletoVO.getDataVencimento(), 1), "yyyy-MM-dd")).append("</DATA> \n");
            if (boletoVO.getEmpresaVO().getMultaCobrancaAutomatica() > 0 && boletoVO.getEmpresaVO().isUtilizarMultaValorAbsoluto()) {
                arquivoXML.append("<VALOR>").append(obterValorIncluirBoleto(boletoVO.getEmpresaVO().getMultaCobrancaAutomatica())).append("</VALOR> \n");
            } else if (boletoVO.getEmpresaVO().getMultaCobrancaAutomatica() > 0 && !boletoVO.getEmpresaVO().isUtilizarMultaValorAbsoluto()) {
                arquivoXML.append("<PERCENTUAL>").append(obterValorIncluirBoleto(boletoVO.getEmpresaVO().getMultaCobrancaAutomatica())).append("</PERCENTUAL> \n");
            }
            arquivoXML.append("</MULTA> \n");
        }

        arquivoXML.append("<DESCONTOS> \n");
        arquivoXML.append("<DESCONTO> \n");
        arquivoXML.append("<VALOR>0.00</VALOR> \n");
        arquivoXML.append("<TIPO>ISENTO</TIPO> \n");
        arquivoXML.append("</DESCONTO> \n");
        arquivoXML.append("</DESCONTOS> \n");
        arquivoXML.append("<FICHA_COMPENSACAO> \n");

        String instrucaoBoleto = Uteis.retirarAcentuacao(montarIntrucaoBoleto(pessoaVO, empresaVO)).replaceAll("%", " por cento");
        arquivoXML.append("<MENSAGENS> \n");
        arquivoXML.append("<MENSAGEM>").append(instrucaoBoleto.substring(0, Math.min(instrucaoBoleto.length(), 40))).append("</MENSAGEM>\n");
        arquivoXML.append("<MENSAGEM>").append(instrucaoBoleto.length() > 40 ? instrucaoBoleto.substring(40, Math.min(instrucaoBoleto.length(), 80)) : "").append("</MENSAGEM>\n");
        arquivoXML.append("</MENSAGENS> \n");

        arquivoXML.append("</FICHA_COMPENSACAO> \n");
        arquivoXML.append("<RECIBO_PAGADOR> \n");
        arquivoXML.append("<MENSAGENS> \n");
        arquivoXML.append("<MENSAGEM></MENSAGEM> \n");
        arquivoXML.append("</MENSAGENS> \n");
        arquivoXML.append("</RECIBO_PAGADOR> \n");
        arquivoXML.append("<PAGAMENTO> \n");
        arquivoXML.append("<QUANTIDADE_PERMITIDA>1</QUANTIDADE_PERMITIDA> \n");
        arquivoXML.append("<TIPO>NAO_ACEITA_VALOR_DIVERGENTE</TIPO> \n");
        arquivoXML.append("<PERCENTUAL_MINIMO>0.00</PERCENTUAL_MINIMO> \n");
        arquivoXML.append("<PERCENTUAL_MAXIMO>0.00</PERCENTUAL_MAXIMO> \n");
        arquivoXML.append("</PAGAMENTO> \n");
        arquivoXML.append("</TITULO> \n");
        arquivoXML.append("</INCLUI_BOLETO> \n");
        arquivoXML.append("</DADOS> \n");
        arquivoXML.append("</ext:SERVICO_ENTRADA> \n");
        arquivoXML.append("</soapenv:Body> \n");
        arquivoXML.append("</soapenv:Envelope> \n");

        return arquivoXML.toString();
    }

    private String montarIntrucaoBoleto(PessoaVO pessoaVO, EmpresaVO empresaVO) {
        String instrucoesBoleto = this.convenioCobrancaVO.getInstrucoesBoleto();
        try {
            if (instrucoesBoleto.toUpperCase().contains("TAG_MATRICULA")) {
                ClienteVO clienteVO = this.clienteDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                    instrucoesBoleto = processarInstrucaoMatricula(instrucoesBoleto, clienteVO.getMatricula());
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return instrucoesBoleto;
    }

    private String processarInstrucaoMatricula(String instrucao, String matriculaCliente) throws Exception {
        if (instrucao.contains("<matricula>")) {
            int inicioMatricula = instrucao.indexOf("<matricula>");
            int finalMatricula = instrucao.indexOf("</matricula>");
            String aux = instrucao.substring(0, inicioMatricula);
            aux = aux + instrucao.substring((inicioMatricula + 11), finalMatricula);
            aux = aux.replaceAll("TAG_MATRICULA", matriculaCliente != null ? matriculaCliente : "");
            aux += instrucao.substring(finalMatricula + 12);
            instrucao = aux;
        }
        return instrucao;
    }

    private String obterValorIncluirBoleto(Double valor) {
        // Multiplicando o valor por 100, para pegar as duas últimas casas decimais.
        double valorMultiplicado = valor * 100;

        // Arredondando o valor para o inteiro mais próximo, para evitar problemas de faltar 0,01 centavo do valor do boleto para o sistema.
        int valorArredondado = (int) Math.round(valorMultiplicado); // Isso resulta em 1060

        // Para apresentar como 10.60 (duas casas decimais)
        String valorComoString = String.format("%.2f", valorArredondado / 100.0);
        valorComoString = valorComoString.replace(",", ".");
        return valorComoString;
    }

    private String obterTipoJurosIncluirBoleto(BoletoVO boletoVO) {
        String valorTipoJuros = TipoJurosCaixaEnum.ISENTO.getDescricao();
        if (boletoVO.getEmpresaVO().getJurosCobrancaAutomatica() > 0 && boletoVO.getEmpresaVO().isUtilizarJurosValorAbsoluto()) {
            valorTipoJuros = TipoJurosCaixaEnum.VALOR_POR_DIA.getDescricao();
        } else if (boletoVO.getEmpresaVO().getJurosCobrancaAutomatica() > 0 && !boletoVO.getEmpresaVO().isUtilizarJurosValorAbsoluto()) {
            valorTipoJuros = TipoJurosCaixaEnum.TAXA_MENSAL.getDescricao();
        }
        return valorTipoJuros;
    }

    private String obterAgencia(String agencia) {
        String agenciaString = agencia;
        // Verifica se a string tem menos de 4 caracteres
        if (agenciaString.length() < 4) {
            // Adiciona "0" à esquerda até que a string tenha 7 caracteres
            agenciaString = String.format("%4s", agenciaString).replace(' ', '0');
        }
        return agenciaString;
    }

    private void validarDadosObrigatorioPagador(PessoaCPFTO pessoaCPFTO, CidadeVO cidadeVO, EnderecoVO enderecoVO) throws Exception {
        if (UteisValidacao.emptyString(pessoaCPFTO.getCpfResponsavel())) {
            throw new ConsistirException("O CPF/CNPJ do cliente é obrigatório para registrar o boleto no site da Caixa.");
        }
        if (UteisValidacao.emptyString(pessoaCPFTO.getNomeResponsavel())) {
            throw new ConsistirException("O nome do cliente é obrigatório para registrar o boleto no site da Caixa.");
        }
        if (enderecoVO == null) {
            throw new ConsistirException("O endereço do cliente é obrigatório para registrar o boleto no site da Caixa.");
        }
        if (UteisValidacao.emptyString(enderecoVO.getEndereco())) {
            throw new ConsistirException("O endereço do cliente é obrigatório para registrar o boleto no site da Caixa.");
        }
        if (cidadeVO == null) {
            throw new ConsistirException("A cidade do cliente é obrigatório para registrar o boleto no site da Caixa.");
        }
        if (UteisValidacao.emptyNumber(cidadeVO.getCodigo()) || UteisValidacao.emptyString(cidadeVO.getNome())) {
            throw new ConsistirException("A cidade do cliente é obrigatório para registrar o boleto no site da Caixa.");
        }
        if (UteisValidacao.emptyString(cidadeVO.getEstado().getSigla())) {
            throw new ConsistirException("O estado(UF) do cliente é obrigatório para registrar o boleto no site da Caixa.");
        }
        if (UteisValidacao.emptyString(enderecoVO.getCep())) {
            throw new ConsistirException("O CEP do cliente é obrigatório para registrar o boleto no site da Caixa.");
        }
    }

    private void validarDados(BoletoVO boletoVO) throws Exception {
        if (UteisValidacao.emptyString(this.chaveBanco)) {
            throw new ConsistirException("Operação não permitida, não foi possível identificar a chaveZW.");
        }
        if (UteisValidacao.emptyString(boletoVO.getNossoNumero())) {
            throw new ConsistirException("O parâmetro nossoNumero é obrigatório para registrar o boleto no site da Caixa.");
        }
        if (boletoVO.getDataVencimento() == null) {
            throw new ConsistirException("O parâmetro dataVencimento é obrigatório para registrar o boleto no site da Caixa.");
        }
        if (UteisValidacao.emptyNumber(boletoVO.getValor())) {
            throw new ConsistirException("O parâmetro valorCobrado é obrigatório para registrar o boleto no site da Caixa.");
        }
        if (UteisValidacao.emptyString(this.convenioCobrancaVO.getCnpj())) {
            throw new ConsistirException("O parâmetro cnpjBeneficiario é obrigatório para registrar o boleto no site da Caixa.");
        }
        if (UteisValidacao.emptyString(this.convenioCobrancaVO.getContaEmpresa().getAgencia())) {
            throw new ConsistirException("O parâmetro agenciaBeneficiario é obrigatório para registrar o boleto no site da Caixa.");
        }
        if (UteisValidacao.emptyString(this.convenioCobrancaVO.getContaEmpresa().getContaCorrente())) {
            throw new ConsistirException("O parâmetro contaCorrenteBeneficiario é obrigatório para registrar o boleto no site da Caixa.");
        }
        if (UteisValidacao.emptyString(this.convenioCobrancaVO.getContaEmpresa().getContaCorrenteDV())) {
            throw new ConsistirException("O parâmetro digitoVerificadorContaBeneficiario é obrigatório para registrar o boleto no site da Caixa.");
        }
    }

    @Override
    public void cancelar(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao, boolean origemProcessoManutencao) throws Exception {
        Boleto boletoDAO;

        //Se for na Classe Boleto, no metodo cancelarBoleto, ele é chamado em 13 locais diferentes e não sei com certeza quais são manuais e quais são automáticos.
        //Os que acho que são manuais e automático, adicionei na condição abaixo.
        //Os que não tenho certeza, estão fora da condição e vai lançar a exceção.
        //Na medida que for descobrindo quem falta, precisa adicionar na condição abaixo.
        if (!horarioPermitidoPelaAPICaixa() && (operacao.equals("CancelarBoleto - Tela Cliente") || operacao.equals("CancelarBoletosSelecionados - Tela Cliente")
                || operacao.equals("CancelarBoleto - Tela Cliente - PactoPay") || operacao.equals("Caixa Em Aberto - renegociar")
                || operacao.equals("Caixa Em Aberto - receber") || operacao.equals("Caixa Em Aberto - cancelar"))) {
            throw new Exception("A API da Caixa só permite Cancelar entre entre 08:05 e 19:55.");
        } else if (!horarioPermitidoPelaAPICaixa() && (operacao.equals("Cancelamento pelo ajuste do processo geral") ||
                    operacao.equals("Boletos Pendentes de Cancelamento - RemessaService")) || operacao.equals("PactoPay - Cancelar")) {
            return;
        }

        try {
            boletoDAO = new Boleto(this.getCon());

            //Se o boleto não estiver registrado, só muda o Status, desvincula as parcelas e já retorna.
            if (!this.tentarCancelarBoletosCaixaSemRegistro && !boletoVO.getSituacao().equals(SituacaoBoletoEnum.ERRO) && UteisValidacao.emptyString(boletoVO.getLinkBoleto())) {
                boletoDAO.alterarSituacao(boletoVO, SituacaoBoletoEnum.CANCELADO);

                boletoDAO.incluirBoletoHistorico(boletoVO, "cancelar", "Boleto não Registrado");
                boletoVO.setJsonEstorno("Boleto não Registrado");
                boletoDAO.alterarJsonEstorno(boletoVO);

                return;
            }

            String xmlBaixa = obterXMLBaixa(boletoVO);
            boletoDAO.incluirBoletoHistorico(boletoVO, "cancelar", xmlBaixa);

            URL url = new URL(PropsService.getPropertyValue(PropsService.urlApiCaixaManutencao));
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            OutputStream outputStream = null;

            try {
                connection.setRequestMethod("POST");
                connection.setDoOutput(true);
                connection.setRequestProperty("SOAPAction", "BaixaBoleto");
                connection.setRequestProperty("Content-Type", "text/xml;charset=UTF-8");
                connection.setRequestProperty("Accept-Encoding","gzip,deflate");

                outputStream = connection.getOutputStream();
                byte[] b = xmlBaixa.getBytes("UTF-8");
                outputStream.write(b);
                outputStream.flush();

                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    InputStream inputStream = connection.getInputStream();

                    // Verifica se a resposta está compactada
                    String contentEncoding = connection.getContentEncoding();
                    if ("gzip".equalsIgnoreCase(contentEncoding)) {
                        inputStream = new GZIPInputStream(inputStream);
                    } else if ("deflate".equalsIgnoreCase(contentEncoding)) {
                        inputStream = new InflaterInputStream(inputStream);
                    }

                    // Agora lê o conteúdo descomprimido
                    BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();

                    boletoVO.setJsonEstorno(response.toString());
                    boletoDAO.alterarJsonEstorno(boletoVO);

                    // Quanto tem erro interno na Caixa, continua retornando 200, mas com essa tag <MENSAGENS>, então precisa dessa parte para pegar a Mensagem de Erro para exibir ao usuário.
                    if (response.toString().contains("<MENSAGENS>")) {
                        System.out.println("BOLETO REGISTRADO COM ERRO.");
                        // Encontra o índice inicial do conteúdo dentro de <MENSAGENS>
                        int inicio = response.toString().indexOf("<MENSAGENS>") + "<MENSAGENS>".length();
                        // Encontra o índice final do conteúdo dentro de </MENSAGENS>
                        int fim = response.toString().indexOf("</MENSAGENS>");

                        String msg = response.toString().substring(inicio, fim);
                        if (UteisValidacao.emptyString(msg) || !msg.contains("<RETORNO>(0) OPERACAO EFETUADA</RETORNO>")) { //Tanto o retorndo do Inclui como Baixa tem o mesmo retorno.
                            throw new Exception(msg);
                        }
                    }

                    // Se não tem erro, nessa parte vai pegar os dados de sucesso.
                    boletoDAO.incluirBoletoHistorico(boletoVO, "cancelar | resposta", response.toString());
                    boletoDAO.alterarSituacao(boletoVO, SituacaoBoletoEnum.CANCELADO);

                    if (!origemProcessoManutencao) {
                        estornarRecibo(boletoVO, usuarioVO);
                    }

                } else {
                    System.out.println("Erro na requisição: HTTP code " + responseCode);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.msg_erro, ex.getMessage());
                boletoVO.setSituacao(SituacaoBoletoEnum.ERRO);
            } finally {
                outputStream.close();
                connection.getInputStream().close();
            }
        } finally {
            boletoDAO = null;
        }
    }

    public String obterXMLBaixa(BoletoVO boletoVO) throws Exception {
        StringBuilder arquivoXML = new StringBuilder();
        arquivoXML.append("<?xml version=\"1.0\" encoding=\"utf-8\"?> \n");
        arquivoXML.append("<soapenv:Envelope \n" +
                "xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" \n" +
                "xmlns:ext=\"http://caixa.gov.br/sibar/manutencao_cobranca_bancaria/boleto/externo\" \n" +
                "xmlns:sib=\"http://caixa.gov.br/sibar\">");
        arquivoXML.append("<soapenv:Header/> \n");
        arquivoXML.append("<soapenv:Body> \n");
        arquivoXML.append("<ext:SERVICO_ENTRADA> \n");
        arquivoXML.append("<sib:HEADER> \n");
        arquivoXML.append("<VERSAO>3.0</VERSAO> \n"); //3.0 Boleto Comum, 3.2 Boleto Hibrido com Pix.
        arquivoXML.append("<AUTENTICACAO>").append(obterAutenticacao(boletoVO, TipoOperacaoCaixaEnum.BAIXA_BOLETO)).append("</AUTENTICACAO> \n");
        arquivoXML.append("<USUARIO_SERVICO>SGCBS02P</USUARIO_SERVICO> \n");
        arquivoXML.append("<OPERACAO>BAIXA_BOLETO</OPERACAO> \n");
        arquivoXML.append("<SISTEMA_ORIGEM>SIGCB</SISTEMA_ORIGEM> \n");
        arquivoXML.append("<UNIDADE>").append(obterAgencia(this.convenioCobrancaVO.getContaEmpresa().getAgencia())).append("</UNIDADE> \n");
        arquivoXML.append("<DATA_HORA>").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyyMMddHHmmss")).append("</DATA_HORA> \n");
        arquivoXML.append("</sib:HEADER> \n");
        arquivoXML.append("<DADOS> \n");
        arquivoXML.append("<BAIXA_BOLETO> \n");
        arquivoXML.append("<CODIGO_BENEFICIARIO>").append(obterCodigoBeneficiario(boletoVO.getConvenioCobrancaVO().getNumeroContrato())).append("</CODIGO_BENEFICIARIO> \n");
        arquivoXML.append("<NOSSO_NUMERO>").append(obterNossoNumeroPadraoCaixa(boletoVO.getNossoNumero())).append("</NOSSO_NUMERO> \n");
        arquivoXML.append("</BAIXA_BOLETO> \n");
        arquivoXML.append("</DADOS> \n");
        arquivoXML.append("</ext:SERVICO_ENTRADA> \n");
        arquivoXML.append("</soapenv:Body> \n");
        arquivoXML.append("</soapenv:Envelope> \n");

        return arquivoXML.toString();
    }

    @Override
    public void processarWebhook(BoletoVO boletoVO, String json) throws Exception {
//        throw new Exception("Recurso não disponível para CAIXA.");
//        processarPagamentoBoletoCaixa(boletoVO, json, this.usuarioDAO.getUsuarioRecorrencia(), "processarWebhook");
    }

    @Override
    public void sincronizar(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao) throws Exception {
        Boleto boletoDAO;
        ConvenioCobranca convenioCobrancaDAO;

        if (!horarioPermitidoPelaAPICaixa() && operacao.equals("SincronizarBoleto - Tela Cliente")) {
            throw new Exception("A API da Caixa só permite consulta entre entre 08:05 e 19:55.");
        } else if (!horarioPermitidoPelaAPICaixa() && operacao.equals("processoAutomaticoPagamentosBoletoCaixa")) {
            return;
        }

        try {
            boletoDAO = new Boleto(this.getCon());
            convenioCobrancaDAO = new ConvenioCobranca(this.getCon());

            if (UteisValidacao.emptyString(boletoVO.getIdExterno())) {
                throw new Exception("Boleto não tem idExterno (id_unico)");
            }

            if (UteisValidacao.emptyString(boletoVO.getConvenioCobrancaVO().getCnpj())) {
                boletoVO.setConvenioCobrancaVO(convenioCobrancaDAO.consultarPorChavePrimaria(boletoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS));
            }

            String xmlBaixa = obterXMLConsulta(boletoVO);
            boletoDAO.incluirBoletoHistorico(boletoVO, "sincronizar", xmlBaixa);

            URL url = new URL(PropsService.getPropertyValue(PropsService.urlApiCaixaConsulta));
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            OutputStream outputStream = null;

            try {
                connection.setRequestMethod("POST");
                connection.setDoOutput(true);
                connection.setRequestProperty("SOAPAction", "http://caixa.gov.br/sibar/consulta_cobranca_bancaria/boleto/NewOperation");
                connection.setRequestProperty("Content-Type", "text/xml;charset=UTF-8");
                connection.setRequestProperty("Accept-Encoding","gzip,deflate");

                outputStream = connection.getOutputStream();
                byte[] b = xmlBaixa.getBytes("UTF-8");
                outputStream.write(b);
                outputStream.flush();

                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    InputStream inputStream = connection.getInputStream();

                    // Verifica se a resposta está compactada
                    String contentEncoding = connection.getContentEncoding();
                    if ("gzip".equalsIgnoreCase(contentEncoding)) {
                        inputStream = new GZIPInputStream(inputStream);
                    } else if ("deflate".equalsIgnoreCase(contentEncoding)) {
                        inputStream = new InflaterInputStream(inputStream);
                    }

                    // Agora lê o conteúdo descomprimido
                    BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();

                    // Quanto tem erro interno na Caixa, continua retornando 200, mas com essa tag <MENSAGENS>, então precisa dessa parte para pegar a Mensagem de Erro para exibir ao usuário.
                    TipoRetornoConsultaCaixaEnum retornoConsulta = null;
                    if (response.toString().contains("<MENSAGENS>")) {
                        // Encontra o índice inicial do conteúdo dentro de <MENSAGENS>
                        int inicio = response.toString().indexOf("<MENSAGENS>") + "<MENSAGENS>".length();
                        // Encontra o índice final do conteúdo dentro de </MENSAGENS>
                        int fim = response.toString().indexOf("</MENSAGENS>");

                        String msg = response.toString().substring(inicio, fim);
                        //Tem retorno não tratados por não conhecer o caso concreto e não saber como atuar.
                        //A medida que forem aparecendo, ir adicionando o tratamento do retorno aqui.
                        retornoConsulta = TipoRetornoConsultaCaixaEnum.obterPorDescricaoContains(msg);
                        if (UteisValidacao.emptyString(msg) || (!retornoConsulta.equals(TipoRetornoConsultaCaixaEnum.EM_ABERTO) &&
                                !retornoConsulta.equals(TipoRetornoConsultaCaixaEnum.LIQUIDADO) &&
                                !retornoConsulta.equals(TipoRetornoConsultaCaixaEnum.LIQUIDADO_NO_CARTORIO) &&
                                !retornoConsulta.equals(TipoRetornoConsultaCaixaEnum.TITULO_JA_PAGO_NO_DIA) &&
                                !retornoConsulta.equals(TipoRetornoConsultaCaixaEnum.BAIXA_POR_DEVOLUCAO) &&
                                !retornoConsulta.equals(TipoRetornoConsultaCaixaEnum.BAIXA_POR_ESTORNO) &&
                                !retornoConsulta.equals(TipoRetornoConsultaCaixaEnum.BAIXA_POR_PROTESTO))) {
                            //Se a ação de sincronizar for pela Tela Cliente, então lança a exceção para exibir a mensagem ao usuário.
                            //Se não, apenas retorna para serguir para próximo item do for de boletos para consultar.
                            if (operacao.equals("SincronizarBoleto - Tela Cliente")) {
                                if (msg.contains("HASH DIVERGENTE")) {
                                    msg = "Boleto não pode ser gerado. Validar dados do convênio.";
                                }
                                throw new Exception(msg);
                            } else {
                                Uteis.logarDebug("Processo Automatico Pagamento Boleto Caixa Online - Sincronizar - Erro: " + msg);
                                return;
                            }
                        } else if (retornoConsulta.equals(TipoRetornoConsultaCaixaEnum.EM_ABERTO)) {
                            boletoDAO.incluirBoletoHistorico(boletoVO, "sincronizar | resposta", response.toString());
                            if (operacao.equals("SincronizarBoleto - Tela Cliente")) {
                                return;
                            } else {
                                Uteis.logarDebug("Processo Automatico Pagamento Boleto Caixa Online - Sincronizar - Erro: " + msg);
                                return;
                            }
                        } else if (retornoConsulta.equals(TipoRetornoConsultaCaixaEnum.BAIXA_POR_DEVOLUCAO) ||
                                   retornoConsulta.equals(TipoRetornoConsultaCaixaEnum.BAIXA_POR_ESTORNO) ||
                                   retornoConsulta.equals(TipoRetornoConsultaCaixaEnum.BAIXA_POR_PROTESTO)) {

                            boletoDAO.alterarSituacao(boletoVO, SituacaoBoletoEnum.CANCELADO);
                            boletoDAO.incluirBoletoHistorico(boletoVO, "cancelar | resposta", response.toString());

                            if (operacao.equals("SincronizarBoleto - Tela Cliente")) {
                                throw new Exception("Boleto está cancelado.");
                            } else {
                                Uteis.logarDebug("Processo Automatico Pagamento Boleto Caixa Online - Sincronizar - Erro: " + msg);
                                return;
                            }
                        }
                    }

                    // Se não tem erro, nessa parte vai para o fluxo de coletar os dados para baixa da parcela.
                    boletoDAO.incluirBoletoHistorico(boletoVO, "sincronizar | resposta", response.toString());
                    processarPagamentoBoletoCaixa(boletoVO, response.toString(), usuarioVO, operacao, retornoConsulta);
                } else {
                    Uteis.logarDebug("Processo Automatico Pagamento Boleto Caixa Online - Sincronizar - Erro na requisição: HTTP code " + responseCode);
                }
            } catch (Exception ex) {
                String mensage = ex.getMessage();
                if (!UteisValidacao.emptyString(mensage) && (mensage.equals("Boleto está cancelado.") || mensage.equals("Boleto não pode ser gerado. Validar dados do convênio."))) {
                    // Para exibir Grow para usuário
                    throw ex;
                } else {
                    boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.msg_erro, ex.getMessage());
                    boletoVO.setSituacao(SituacaoBoletoEnum.ERRO);
                }
            } finally {
                outputStream.close();
                connection.getInputStream().close();
            }
        } catch (Exception ex) {
            throw ex;
        } finally {
            boletoDAO = null;
            convenioCobrancaDAO = null;
        }
    }

    public String obterXMLConsulta(BoletoVO boletoVO) throws Exception {
        StringBuilder arquivoXML = new StringBuilder();
        arquivoXML.append("<?xml version=\"1.0\" encoding=\"utf-8\"?> \n");

        arquivoXML.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" \n" +
                "xmlns:consultacobrancabancaria=\"http://caixa.gov.br/sibar/consulta_cobranca_bancaria/boleto\" \n" +
                "xmlns:sibar_base=\"http://caixa.gov.br/sibar\"> ");
        arquivoXML.append("<soapenv:Header/> \n");
        arquivoXML.append("<soapenv:Body> \n");

        arquivoXML.append("<consultacobrancabancaria:SERVICO_ENTRADA> ");
        arquivoXML.append("<sibar_base:HEADER> \n");
        arquivoXML.append("<VERSAO>5.2</VERSAO> \n");
        arquivoXML.append("<AUTENTICACAO>").append(obterAutenticacao(boletoVO, TipoOperacaoCaixaEnum.CONSULTA_BOLETO)).append("</AUTENTICACAO> \n");
        arquivoXML.append("<USUARIO_SERVICO>SGCBS02P</USUARIO_SERVICO> \n");
        arquivoXML.append("<OPERACAO>CONSULTA_BOLETO</OPERACAO> \n");
        arquivoXML.append("<SISTEMA_ORIGEM>SIGCB</SISTEMA_ORIGEM> \n");
        arquivoXML.append("<UNIDADE>").append(obterAgencia(this.convenioCobrancaVO.getContaEmpresa().getAgencia())).append("</UNIDADE> \n");
        arquivoXML.append("<DATA_HORA>").append(Uteis.getDataAplicandoFormatacao(Calendario.hoje(), "yyyyMMddHHmmss")).append("</DATA_HORA> \n");
        arquivoXML.append("</sibar_base:HEADER> \n");
        arquivoXML.append("<DADOS> \n");
        arquivoXML.append("<CONSULTA_BOLETO> \n");
        arquivoXML.append("<CODIGO_BENEFICIARIO>").append(obterCodigoBeneficiario(boletoVO.getConvenioCobrancaVO().getNumeroContrato())).append("</CODIGO_BENEFICIARIO> \n");
        arquivoXML.append("<NOSSO_NUMERO>").append(obterNossoNumeroPadraoCaixa(boletoVO.getNossoNumero())).append("</NOSSO_NUMERO> \n");
        arquivoXML.append("</CONSULTA_BOLETO> \n");
        arquivoXML.append("</DADOS> \n");
        arquivoXML.append("</consultacobrancabancaria:SERVICO_ENTRADA> \n");

        arquivoXML.append("</soapenv:Body> \n");
        arquivoXML.append("</soapenv:Envelope> \n");

        return arquivoXML.toString();
    }

    private String extractValue(String xml, String regex) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(xml);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    private void processarPagamentoBoletoCaixa(BoletoVO boletoVO, String xml, UsuarioVO usuarioVO, String operacao, TipoRetornoConsultaCaixaEnum retornoConsulta) throws Exception {
        Boleto boletoDAO = null;
        BoletoVO boletoVOAnterior = null;
        try {
            boletoDAO = new Boleto(this.getCon());
            boletoDAO.incluirBoletoHistorico(boletoVO, "processarPagamentoBoletoCaixa | " + operacao, xml);

            boletoVOAnterior = (BoletoVO) boletoVO.getClone(true);

            Date dataVencimento = null;
            Double valorBoleto = null;
            Double valorPago = calcularValorPago(xml, dataVencimento, valorBoleto, boletoVO, operacao);

            //obter situacao conforme a os dados recebidos
            SituacaoBoletoEnum situacaoBoletoEnum = obterSituacaoBoleto(retornoConsulta);
            boletoVO.setSituacao(situacaoBoletoEnum);
            boletoVO.setValorPago(valorPago);
            boletoVO.setValorLiquido(valorPago);
            boletoVO.setValorTarifa(0.0);

            //API da Caixa não vem Data Pagamento, então vamos considerar a data de hoje se TITULO JA PAGO NO DIA.
            //Se não, vamos considerar a data de ontem, pois pode ter sido pago em qualquer dia anterior a hoje.
            String situacaoBoleto = extractValue(xml, "<CONTROLE_NEGOCIAL>.*?<MENSAGENS>.*?<RETORNO>(.*?)</RETORNO>");
            if (situacaoBoleto.contains("TITULO JA PAGO NO DIA")) {
                boletoVO.setDataPagamento(Calendario.hoje());
                boletoVO.setDataCredito(Calendario.hoje());
            } else {
                boletoVO.setDataPagamento(Calendario.somarDias(Calendario.hoje(), -1));
                boletoVO.setDataCredito(Calendario.somarDias(Calendario.hoje(), -1));
            }

            boletoVO.setValorPossivelDesconto(0.0);
            boletoVO.setSituacao(SituacaoBoletoEnum.PAGO);

            if (boletoVO.getValorPago() > 0.0) {
                if (!UteisValidacao.emptyNumber(boletoVO.getReciboPagamentoVO().getCodigo()) ||
                        !UteisValidacao.emptyNumber(boletoVO.getMovPagamentoVO().getCodigo())) {
                    boletoDAO.incluirBoletoHistorico(boletoVO, operacao, "BOLETO JÁ ESTÁ PAGO");
                } else {
                    //gerar recibo pagamento
                    gerarPagamentoBoleto(boletoVO, usuarioVO, true, false);
                }
            }

            //atualizar dados do boleto
            boletoDAO.alterar(boletoVO);

            Uteis.logarDebug("Processo Automatico Pagamento Boleto Caixa Online - Sincronizar - processarPagamentoBoletoCaixa Sucesso: " + boletoVO.getNossoNumero() + " - " +
                    boletoVO.getSituacao().getDescricao() + " - " + boletoVO.getValorPago() + " - " + boletoVO.getDataPagamento());
        } catch (Exception ex) {
            ex.printStackTrace();
            if (boletoDAO != null) {
                boletoDAO.incluirBoletoHistorico(boletoVO, operacao + " | processarBoletoCaixa | ERRO", ex.getMessage());
            }
            throw ex;
        } finally {
            if (boletoVOAnterior != null && boletoVO != null) {
                if (!boletoVOAnterior.getSituacao().equals(boletoVO.getSituacao())) {
                    JSONObject jsonSituacao = new JSONObject();
                    jsonSituacao.put("situacao_anterior", boletoVOAnterior.getSituacao().getDescricao());
                    jsonSituacao.put("situacao_atual", boletoVO.getSituacao().getDescricao());
                    boletoDAO.incluirBoletoHistorico(boletoVO, "processarBoletoCaixa - ALTERAR SITUAÇÃO", jsonSituacao.toString());
                }
            }
            boletoDAO = null;
        }
    }

    private Double calcularValorPago(String xml, Date dataVencimento, Double valorBoleto, BoletoVO boletoVO, String operacao) throws Exception {
        //API da Caixa não envia para nós o Valor Pago, então temos de calcular do nosso lado.
        Double percentualJuros = null;
        Double percentualMulta = null;
        Double valorAbsolutoJuros = null;
        Double valorAbsolutoMulta = null;

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        String dataVencimentoStr = extractValue(xml, "<DATA_VENCIMENTO>(.*?)</DATA_VENCIMENTO>");
        String valorBoletoStr = extractValue(xml, "<VALOR>(.*?)</VALOR>");
        String percentualJurosStr = extractValue(xml, "<JUROS_MORA>.*?<PERCENTUAL>(.*?)</PERCENTUAL>");
        String percentualMultaStr = extractValue(xml, "<MULTA>.*?<PERCENTUAL>(.*?)</PERCENTUAL>");
        String valorAbsolutoJurosStr = extractValue(xml, "<JUROS_MORA>.*?<VALOR>(.*?)</VALOR>");
        String valorAbsolutoMultaStr = extractValue(xml, "<MULTA>.*?<VALOR>(.*?)</VALOR>");

        try {
            if (dataVencimentoStr != null) {
                dataVencimento = dateFormat.parse(dataVencimentoStr);
            }
        } catch (ParseException e) {
            e.printStackTrace();
            if (boletoDAO != null) {
                boletoDAO.incluirBoletoHistorico(boletoVO, operacao + " | processarBoletoPJBank | ERRO", e.getMessage());
            }
            throw e;
        }

        int quantidadeDiasVencido = 0;
        if (dataVencimento != null) {
            Date dataAtual = new Date();
            long diffInMillies = dataAtual.getTime() - dataVencimento.getTime();
            int diferencaDias = (int) TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);
            quantidadeDiasVencido = diferencaDias > 0 ? diferencaDias : 0;
        }

        try {
            if (valorBoletoStr != null) {
                valorBoleto = Double.parseDouble(valorBoletoStr);
            }
            if (percentualJurosStr != null) {
                percentualJuros = Double.parseDouble(percentualJurosStr);
            }
            if (percentualMultaStr != null) {
                percentualMulta = Double.parseDouble(percentualMultaStr);
            }
            if (valorAbsolutoJurosStr != null) {
                valorAbsolutoJuros = Double.parseDouble(valorAbsolutoJurosStr);
            }
            if (valorAbsolutoMultaStr != null) {
                valorAbsolutoMulta = Double.parseDouble(valorAbsolutoMultaStr);
            }
        } catch (NumberFormatException e) {
            e.printStackTrace();
            if (boletoDAO != null) {
                boletoDAO.incluirBoletoHistorico(boletoVO, operacao + " | processarBoletoPJBank | ERRO", e.getMessage());
            }
            throw e;
        }

        Double valorMulta = 0.0;
        Double valorJuros = 0.0;
        if (!UteisValidacao.emptyString(percentualJurosStr) && !UteisValidacao.emptyString(percentualMultaStr)) {
            valorMulta = quantidadeDiasVencido > 0 ? (percentualMulta * valorBoleto) / 100 : 0.0;
            valorJuros = quantidadeDiasVencido > 0 ? ((valorBoleto * percentualJuros) / 100) * quantidadeDiasVencido : 0.0;
        } else if (!UteisValidacao.emptyString(valorAbsolutoJurosStr) && !UteisValidacao.emptyString(valorAbsolutoMultaStr)) {
            valorMulta = quantidadeDiasVencido > 0 ? valorAbsolutoMulta : 0.0;
            valorJuros = quantidadeDiasVencido > 0 ? (valorAbsolutoJuros * quantidadeDiasVencido) : 0.0;
        }

        return valorBoleto + valorMulta + valorJuros;
    }

    private SituacaoBoletoEnum obterSituacaoBoleto(TipoRetornoConsultaCaixaEnum situacao) {
        if (situacao.equals(TipoRetornoConsultaCaixaEnum.EM_ABERTO)) {
            return SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO;
        } else if (situacao.equals(TipoRetornoConsultaCaixaEnum.TITULO_JA_PAGO_NO_DIA)) {
            return SituacaoBoletoEnum.PAGO;
        } else if (situacao.equals(TipoRetornoConsultaCaixaEnum.BAIXA_POR_DEVOLUCAO) ||
                   situacao.equals(TipoRetornoConsultaCaixaEnum.BAIXA_POR_ESTORNO) ||
                   situacao.equals(TipoRetornoConsultaCaixaEnum.BAIXA_POR_PROTESTO)) {
            return SituacaoBoletoEnum.CANCELADO;
        }
        return null;
    }

    public void processoAutomaticoPagamentosBoletoCaixa(EmpresaVO empresaVO) {
        Boleto boletoDAO = null;
        ConvenioCobranca convenioCobrancaDAO = null;
        Usuario usuarioDAO = null;

        Uteis.logarDebug("Inicio Processo Automatico Pagamento Boleto Caixa Online");

        try {
            boletoDAO = new Boleto(this.getCon());
            convenioCobrancaDAO = new ConvenioCobranca(this.getCon());
            usuarioDAO = new Usuario(this.getCon());

            Integer[] tiposConvenio = {TipoConvenioCobrancaEnum.BOLETO_CAIXA_ONLINE.getCodigo()};
            UsuarioVO usuarioVO = usuarioDAO.consultarPorChavePrimaria(1, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (empresaVO != null) {
                List<ConvenioCobrancaVO> convenioCobrancaVOList = convenioCobrancaDAO.consultarPorEmpresaESituacao(empresaVO.getCodigo(), false,
                        Uteis.NIVELMONTARDADOS_DADOSBASICOS, tiposConvenio, SituacaoConvenioCobranca.ATIVO);

                Uteis.logarDebug("Processo Automatico Pagamento Boleto Caixa Online - Empresa " + empresaVO.getNome());
                for (ConvenioCobrancaVO convenioCobrancaVO: convenioCobrancaVOList) {
                    Uteis.logarDebug("Processo Automatico Pagamento Boleto Caixa Online - Convênios Empresa " + convenioCobrancaVO.getDescricao());
                    //A API da Caixa não tem a opção de buscar boleto pagos, então precisa buscar um a um pelo NossoNumero.
                    //Aqui está buscando os boletos no banco que estão vencidos a 15 dias atrás e 35 dias para frente, com situação em aberto.
                    //Ai consulta um a um na API da Caixa para verificar se foi pago, usando no NossoNumero.
                    Date dataInicio = Calendario.somarDias(Calendario.hoje(), -15);
                    Date dataFim = Calendario.somarDias(Calendario.hoje(), 35);
                    List<BoletoVO> boletos = boletoDAO.consultarPendentes(convenioCobrancaVO, empresaVO, true, null,
                            Uteis.NIVELMONTARDADOS_DADOSBASICOS, dataInicio, dataFim);

                    for (BoletoVO boletoVO : boletos) {
                        if (boletoVO.getDataVencimento() != null) {
                            Calendar dataVencimento = Calendar.getInstance();
                            dataVencimento.setTime(boletoVO.getDataVencimento());
                            try {
                                this.convenioCobrancaVO = convenioCobrancaVO;
                                sincronizar(boletoVO, usuarioVO, "processoAutomaticoPagamentosBoletoCaixa");
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                        }
                    }
                }
            }
            Uteis.logarDebug("Fim Processo Automatico Pagamento Boleto Caixa Online");
        } catch (Exception ex) {
            Uteis.logarDebug("Erro Processo Automatico Pagamento Boleto Caixa Online - " + ex.getMessage());
            ex.printStackTrace();
        } finally {
            boletoDAO = null;
            convenioCobrancaDAO = null;
            usuarioDAO = null;
        }
    }

    private boolean horarioPermitidoPelaAPICaixa() {
        // A API da Caixa só permite consultar e cancelar/(baixa) boletos entre 08:00 e 20:00.
        // Por isso tem essa validação, que se for fora desse horário, retorna uma mensagem de erro caso a cosulta seja feita na tela do aluno.
        // Se for pelo automático, só retorna para seguir para o próximo boleto.
        LocalTime horaInicial = LocalTime.of(8, 5);
        LocalTime horaFinal = LocalTime.of(19, 55);
        LocalTime horaAtual = LocalTime.now();
        // Verificando se a hora corrente está entre os horários definidos
        return (horaAtual.isAfter(horaInicial) && horaAtual.isBefore(horaFinal));
    }

    public void cancelarBoletoViaProcesso(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao, boolean origemProcessoManutencao,
                                          boolean enviarApiTentarCancelarBoletoNaoRegistrado) throws Exception {
        setTentarCancelarBoletosCaixaSemRegistro(enviarApiTentarCancelarBoletoNaoRegistrado);
        cancelar(boletoVO, usuarioVO, operacao, origemProcessoManutencao);
    }

    public boolean isTentarCancelarBoletosCaixaSemRegistro() {
        return tentarCancelarBoletosCaixaSemRegistro;
    }

    public void setTentarCancelarBoletosCaixaSemRegistro(boolean tentarCancelarBoletosCaixaSemRegistro) {
        this.tentarCancelarBoletosCaixaSemRegistro = tentarCancelarBoletosCaixaSemRegistro;
    }

}
