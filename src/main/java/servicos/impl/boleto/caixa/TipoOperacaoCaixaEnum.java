package servicos.impl.boleto.caixa;

/**
 * Created by <PERSON><PERSON><PERSON> on 09/04/2024
 */
public enum TipoOperacaoCaixaEnum {

    INCLUI_BOLETO(1, "INCLUI_BOLETO", "Inclui/Gera um novo boleto na Caixa."),
    CONSULTA_BOLETO(2, "CONSULTA_BOLETO", "Consultar um boleto já incluido na Caixa."),
    BAIXA_BOLETO(3, "BAIXA_BOLETO", "Baixa/Cancela um boleto na Caixa e não consegue pagar."),
    ALTERA_BOLETO(4, "ALTERA_BOLETO", "Altera um boleto incuido na Caixa, para atualizar valores, vencimentos e etc.")
    ;

    private int codigo;
    private String descricao;
    private String explicacao;

    TipoOperacaoCaixaEnum(int codigo, String descricao, String explicacao) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.explicacao = explicacao;
    }

    public static String consultarDescricaoPorCodigo(int codigo){
        for (TipoOperacaoCaixaEnum o : TipoOperacaoCaixaEnum.values() ) {
            if(o.codigo == codigo){
                return o.descricao;
            }
        }
        return "";
    }

    public static TipoOperacaoCaixaEnum consultarPorCodigo(int codigo){
        for (TipoOperacaoCaixaEnum bancoEnum: TipoOperacaoCaixaEnum.values()){
            if (bancoEnum.getCodigo() == codigo){
                return bancoEnum;
            }
        }
        return null;
    }

    public static String consultarExplicacaoPorCodigo(int codigo){
        for (TipoOperacaoCaixaEnum o : TipoOperacaoCaixaEnum.values() ) {
            if(o.codigo == codigo){
                return o.explicacao;
            }
        }
        return "";
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
