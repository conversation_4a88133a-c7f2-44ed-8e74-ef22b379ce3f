package servicos.impl.boleto.caixa;

/**
 * Created by <PERSON><PERSON><PERSON> on 09/04/2024
 */
public enum TipoJurosCaixaEnum {

    ISENTO(0, "ISENTO", "Não cobrar Juros"),
    VALOR_POR_DIA(1, "VALOR_POR_DIA", "Cobrar Juros por dia de atraso"),
    TAXA_MENSAL(2, "TAXA_MENSAL", "Cobrar Juros por porcentagem mensal")
    ;

    private int codigo;
    private String descricao;
    private String explicacao;

    TipoJurosCaixaEnum(int codigo, String descricao, String explicacao) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.explicacao = explicacao;
    }

    public static String consultarDescricaoPorCodigo(int codigo){
        for (TipoJurosCaixaEnum o : TipoJurosCaixaEnum.values() ) {
            if(o.codigo == codigo){
                return o.descricao;
            }
        }
        return "";
    }

    public static TipoJurosCaixaEnum consultarPorCodigo(int codigo){
        for (TipoJurosCaixaEnum bancoEnum: TipoJurosCaixaEnum.values()){
            if (bancoEnum.getCodigo() == codigo){
                return bancoEnum;
            }
        }
        return null;
    }

    public static String consultarExplicacaoPorCodigo(int codigo){
        for (TipoJurosCaixaEnum o : TipoJurosCaixaEnum.values() ) {
            if(o.codigo == codigo){
                return o.explicacao;
            }
        }
        return "";
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
