package servicos.impl.boleto;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 14/12/2021
 */
public enum AtributoBoletoEnum {

    msg_erro,
    outras_informacoes_anterior,
    credencial_pjbank,
    chave_pjbank,
    ambiente_boleto,
    rejeicao_motivo,
    chave_itau,
    client_id,
    client_secret,
    StatusVenda,
    CodigoErro,
    ValorTarifa,
    ValorMora,
    data_atualizacao,
    pagador_nome,
    pagador_documento,
    pagador_end_numero,
    pagador_end_complemento,
    pagador_end_bairro,
    pagador_end_logradouro,
    pagador_end_cep,
    pagador_end_estado_uf,
    pagador_end_cidade,
    empresa_nome,
    empresa_razao_social,
    empresa_cnpj,
    ;
}
