package servicos.impl.boleto;

import br.com.pactosolucoes.comuns.to.BoletoOnlineTO;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.financeiro.enumerador.TipoBoletoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Endereco;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import relatorio.negocio.comuns.financeiro.DescontoBoletoTO;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 07/12/2021
 */
public abstract class AbstractBoletoOnlineServiceComum extends BoletoOnlineService {

    public AbstractBoletoOnlineServiceComum(Connection con) throws Exception {
        super(con);
    }

    public static BoletoVO criarBoletoVO(BoletoOnlineTO boletoOnlineTO, ConvenioCobrancaVO convenioCobrancaVO, Connection con) throws Exception {
        Cliente clienteDAO;
        ConvenioCobranca convenioCobrancaDAO;
        try {
            clienteDAO = new Cliente(con);
            convenioCobrancaDAO = new ConvenioCobranca(con);

            if (convenioCobrancaVO.getTipo().getTipoBoleto() == null) {
                throw new Exception("Tipo boleto não identificado");
            }

            BoletoOnlineTO.validarDados(boletoOnlineTO);
            boletoOnlineTO.preencherValor();

            BoletoVO obj = new BoletoVO();
            obj.setDataRegistro(Calendario.hoje());
            obj.setSituacao(SituacaoBoletoEnum.NENHUMA);
            obj.setTipo(convenioCobrancaVO.getTipo().getTipoBoleto());
            obj.setValor(boletoOnlineTO.getValor());
            obj.setDataVencimento(boletoOnlineTO.getDataVencimento());
            obj.setEmpresaVO(boletoOnlineTO.getEmpresaVO());
            obj.setPessoaVO(boletoOnlineTO.getPessoaVO());
            obj.setConvenioCobrancaVO(boletoOnlineTO.getConvenioCobrancaVO());
            obj.setUsuarioVO(boletoOnlineTO.getUsuarioVO());
            obj.setOrigem(boletoOnlineTO.getOrigemCobrancaEnum());
            obj.setListaParcelas(boletoOnlineTO.getListaParcelas());
            obj.setIdentificador(convenioCobrancaDAO.incrementarSequencialItem(convenioCobrancaVO.getCodigo()));

            //se não for cobrar multa e juros então vamos limpar
            if (!boletoOnlineTO.isCobrarMultaJuros()) {
                for (MovParcelaVO movParcelaVO : obj.getListaParcelas()) {
                    movParcelaVO.setValorJuros(0.0);
                    movParcelaVO.setValorMulta(0.0);
                    movParcelaVO.setValorMultaJuros(0.0);
                }
            }

            //consultar desconto
            DescontoBoletoTO descontoBoletoTO = null;
            if (!UteisValidacao.emptyNumber(boletoOnlineTO.getDescontoValorFixo()) ||
                    !UteisValidacao.emptyNumber(boletoOnlineTO.getDescontoPercentual())) {
                    descontoBoletoTO = new DescontoBoletoTO();
                    descontoBoletoTO.setDiaMaximoPagamentoDesconto(Uteis.obterDiaData(boletoOnlineTO.getDataVencimento()));
                if (!UteisValidacao.emptyNumber(boletoOnlineTO.getDescontoValorFixo())) {
                    Double porcentagemDesconto = ((boletoOnlineTO.getDescontoValorFixo() * 100) / obj.getValor());
                    descontoBoletoTO.setPorcentagemDesconto(porcentagemDesconto);
                } else {
                    descontoBoletoTO.setPorcentagemDesconto(boletoOnlineTO.getDescontoPercentual());
                }
            } else {
                descontoBoletoTO = clienteDAO.descobrirDescontoBoletoParaCliente(obj.getListaParcelas());
            }
            if (descontoBoletoTO != null) {
                obj.setDiaMesDescontoPagAntecipado(descontoBoletoTO.getDiaMaximoPagamentoDesconto());
                obj.setPorcentagemDescontoPagAntecipado(descontoBoletoTO.getPorcentagemDesconto());
                obj.setValorDescontoPagAntecipado(descontoBoletoTO.getValorDesconto());
            }

            obj.adicionarOutrasInformacoes(AtributoBoletoEnum.ambiente_boleto, convenioCobrancaVO.getAmbiente().getCodigo().toString());
            if (obj.getTipo().equals(TipoBoletoEnum.PJ_BANK)) {
                obj.adicionarOutrasInformacoes(AtributoBoletoEnum.credencial_pjbank, convenioCobrancaVO.getCredencialPJBank());
                obj.adicionarOutrasInformacoes(AtributoBoletoEnum.chave_pjbank, convenioCobrancaVO.getChavePJBank());
            } else if (obj.getTipo().equals(TipoBoletoEnum.ITAU)) {
                obj.adicionarOutrasInformacoes(AtributoBoletoEnum.chave_itau, convenioCobrancaVO.getCodigoAutenticacao01());
                obj.adicionarOutrasInformacoes(AtributoBoletoEnum.client_id, convenioCobrancaVO.getCodigoAutenticacao02());
                obj.adicionarOutrasInformacoes(AtributoBoletoEnum.client_secret, convenioCobrancaVO.getCodigoAutenticacao03());
            }
            return obj;
        } finally {
            clienteDAO = null;
            convenioCobrancaDAO = null;
        }
    }

    public EnderecoVO obterEnderecoVO(PessoaVO pessoaVO) throws Exception {
        Endereco enderecoDAO;
        try {
            enderecoDAO = new Endereco(this.getCon());

            List<EnderecoVO> listaEnderecos = enderecoDAO.consultarEnderecos(pessoaVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (UteisValidacao.emptyList(listaEnderecos)) {
                return new EnderecoVO();
            }

            Ordenacao.ordenarListaReverse(listaEnderecos, "codigo");

            for (EnderecoVO enderecoVO : listaEnderecos) {
                if (enderecoVO.getEnderecoCorrespondencia()) {
                   return enderecoVO;
                }
            }

            for (EnderecoVO enderecoVO : listaEnderecos) {
                if (enderecoVO.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo())) {
                    return enderecoVO;
                }
            }

            return listaEnderecos.get(0);
        } finally {
            enderecoDAO = null;
        }
    }

    public String obterURLLogoEmpresa(String chaveBanco, EmpresaVO empresaVO) {
        try {
            final String genKey = MidiaService.getInstance().genKey(chaveBanco, MidiaEntidadeEnum.FOTO_EMPRESA, empresaVO.getCodigo().toString());
            return Uteis.getPaintFotoDaNuvem(genKey);
        } catch (Exception ex) {
//            ex.printStackTrace();
            return "";
        }
    }

    public String obterUrlWebhookPacto(String chaveBanco, TipoBoletoEnum tipoBoletoEnum,
                                       ConvenioCobrancaVO convenioCobrancaVO, BoletoVO boletoVO) {
        StringBuilder url = new StringBuilder((Uteis.getUrlAPI() + "/prest/boleto/" + chaveBanco + "/" + tipoBoletoEnum.getCodigo()));
        Map<String, String> mapaParametros = new HashMap<>();
        if (boletoVO != null && !UteisValidacao.emptyNumber(boletoVO.getCodigo())) {
            url.append("/").append(boletoVO.getCodigo());
        }
        if (convenioCobrancaVO != null && !UteisValidacao.emptyNumber(convenioCobrancaVO.getCodigo())) {
            mapaParametros.put("convenio", convenioCobrancaVO.getCodigo().toString());
        }

        if (mapaParametros.size() > 0) {
            boolean primeiro = true;
            for (String key : mapaParametros.keySet()) {
                if (primeiro) {
                    url.append("?");
                    primeiro = false;
                } else {
                    url.append("&");
                }
                url.append(key).append("=").append(mapaParametros.get(key));
            }
        }
        return url.toString();
    }

    public BoletoVO verificarBoletoExistente(BoletoVO boletoVO) throws SQLException {
        Boleto boletoDAO;
        try {
            boletoDAO = new Boleto(this.getCon());

            StringBuilder sql = new StringBuilder();
            sql.append("select  \n");
            sql.append("max(b.codigo) as boleto \n");
            sql.append("from boleto b \n");
            sql.append("inner join boletomovparcela bm on bm.boleto = b.codigo \n");
            sql.append("where b.conveniocobranca = ").append(boletoVO.getConvenioCobrancaVO().getCodigo()).append(" \n");
            sql.append("and b.empresa = ").append(boletoVO.getEmpresaVO().getCodigo()).append(" \n");

            if (!boletoVO.getTipo().equals(TipoBoletoEnum.CAIXA)) {
                sql.append("and b.datavencimento = '").append(Uteis.getDataFormatoBD(boletoVO.getDataVencimento())).append("' \n");
                sql.append("and b.valor = ").append(boletoVO.getValor()).append(" \n");
            }

            sql.append("and b.situacao in (");
            sql.append(SituacaoBoletoEnum.GERADO.getCodigo()).append(",");
            sql.append(SituacaoBoletoEnum.AGUARDANDO_REGISTRO.getCodigo()).append(",");
            sql.append(SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO.getCodigo());
            sql.append(") \n");

            String parcelas = "";
            for (MovParcelaVO movParcelaVO : boletoVO.getListaParcelas()) {
                parcelas += ("," + movParcelaVO.getCodigo());
            }
            sql.append("and bm.movparcela in (").append(parcelas.replaceFirst(",", "")).append(") \n");
            try (Statement stm = this.getCon().createStatement()) {
                try (ResultSet rs = stm.executeQuery(sql.toString())) {
                    if (rs.next()) {
                        return boletoDAO.consultarPorChavePrimaria(rs.getInt(1), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                    }
                    return null;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        } finally {
            boletoDAO = null;
        }
    }
}
