package servicos.impl.boleto;

import br.com.pactosolucoes.comuns.util.Formatador;
import br.com.pactosolucoes.comuns.util.StringUtilities;
import br.com.pactosolucoes.enumeradores.TipoEnderecoEnum;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.contrato.MovProdutoModalidadeVO;
import negocio.comuns.contrato.MovProdutoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.MovParcelaVO;
import negocio.comuns.financeiro.MovProdutoParcelaVO;
import negocio.comuns.financeiro.RemessaItemMovParcelaVO;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.financeiro.enumerador.ArquivoLayoutRemessaEnum;
import negocio.comuns.financeiro.enumerador.TipoConvenioCobrancaEnum;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.oamd.EmpresaFinanceiroVO;
import org.jboleto.JBoleto;
import org.jboleto.JBoletoBean;
import servicos.impl.boleto.brb.LayoutRemessaBRBCNAB400;
import servicos.impl.boleto.daycoval.LayoutRemessaDaycovalBoletoCNAB400;
import servicos.impl.boleto.sicredi.LayoutRemessaSicrediBoleto;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.RemessaService;
import servicos.impl.dcc.base.TipoRegistroEnum;
import servicos.impl.dcc.bb.LayoutRemessaBBCNAB240;
import servicos.impl.dcc.bnb.LayoutRemessaBNBCNAB400;
import servicos.impl.dcc.bradesco.LayoutRemessaBradescoCNAB240;
import servicos.impl.dcc.bradesco.LayoutRemessaBradescoCNAB400;
import servicos.impl.dcc.caixa.LayoutRemessaCaixaCNAB240;
import servicos.impl.dcc.itau.LayoutRemessaItauBoletoCNAB400;
import servicos.impl.dcc.itau.LayoutRemessaItauCNAB400BoletoAtualizado;
import servicos.impl.dcc.safra.LayoutRemessaSafraCNAB400;
import servicos.impl.dcc.santander.LayoutRemessaSantanderCNAB400;
import servicos.impl.dcc.sicoob.LayoutRemessaSicoobCNAB240;
import servicos.impl.dcc.sicoob.LayoutRemessaSicoobCNAB400;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by GlaucoT on 22/04/2015
 */
public class LayoutBoletoPadrao extends SuperTO {

    private RemessaVO remessa = null;

    private static void lerRetornoBoleto(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            String banco = "";
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("02RETORNO")) {
                    if (linha.contains("BRADESCO")) {
                        banco = "BRADESCO";
                    }
                    lerAtributosBanco(linha, h, banco);
                } else if (linha.startsWith("1")) {
                    RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                    lerAtributosBanco(linha, detail, banco);
                    listaDetalheRetorno.add(detail);
                } else if (linha.startsWith("9")) {
                    lerAtributosBanco(linha, t, banco);
                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);

//            validarArquivoRemessaRetorno(remessa);

            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SANTANDER.getCodigo())) {
            LayoutRemessaSantanderCNAB400.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BRB.getCodigo())) {
            LayoutRemessaBRBCNAB400.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BANCOOB.getCodigo())
                && (remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) ||
                remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE))) {
            LayoutRemessaSicoobCNAB240.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BANCOOB.getCodigo())
                && (!remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) &&
                !remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE))) {
            LayoutRemessaSicoobCNAB400.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BNB.getCodigo())) {
            LayoutRemessaBNBCNAB400.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.CAIXA.getCodigo())) {
            LayoutRemessaCaixaCNAB240.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BANCODOBRASIL.getCodigo())) {
            LayoutRemessaBBCNAB240.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.ITAU)) {
            LayoutRemessaItauCNAB400BoletoAtualizado.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.ITAU.getCodigo())
                && !remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_DAYCOVAL)) {
            LayoutRemessaItauBoletoCNAB400.lerRetorno(remessa, false);
        } else if (remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_DAYCOVAL)) {
            LayoutRemessaDaycovalBoletoCNAB400.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().getTipo() != null &&
                remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_BRADESCO_240)) {
            LayoutRemessaBradescoCNAB240.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SAFRA.getCodigo())) {
            LayoutRemessaSafraCNAB400.lerRetorno(remessa);
        } else if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SICREDI.getCodigo())) {
            LayoutRemessaSicrediBoleto.lerRetorno(remessa);
        } else {
            lerRetornoBoleto(remessa);
        }
    }

    public static String obterCodigosMovParcelas(final StringBuilder retorno) throws IOException {
       return obterValorCamposLinhas(retorno,70, 81);
    }
    public static String obterValorCamposLinhas(final StringBuilder dados,int pos1, int pos2) throws IOException {
        List<Long> codigos = new ArrayList<Long>();
        if (dados.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(dados.toString()));
            String linha;
            while ((linha = br.readLine()) != null) {
                Long tipo = Long.valueOf(StringUtilities.readString(0, 1, linha));
                if (tipo.equals(1L)) {
                    codigos.add(Long.valueOf(StringUtilities.readString(pos1, pos2, linha)));
                }
            }
        }
        Collections.sort(codigos);
        String cods = "";
        for (Long cod : codigos) {
            cods = cods + "," + cod;
        }
        return cods.replaceFirst(",", "");
    }

    public static String obterValorCamposLinhas(final StringBuilder dados,int pos1, int pos2, int pos1Opt, int pos2Opt) throws IOException {
        List<Long> codigos = new ArrayList<Long>();
        if (dados.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(dados.toString()));
            String linha;
            while ((linha = br.readLine()) != null) {
                Long tipo = Long.valueOf(StringUtilities.readString(0, 1, linha));
                if (tipo.equals(1L)) {
                    String valor = StringUtilities.readString(pos1, pos2, linha);
                    if(valor.isEmpty() || valor.replaceAll(" ", "").isEmpty()){
                        valor = StringUtilities.readString(pos1Opt, pos2Opt, linha);
                    }
                    codigos.add(Long.valueOf(valor));
                }
            }
        }
        Collections.sort(codigos);
        String cods = "";
        for (Long cod : codigos) {
            cods = cods + "," + cod;
        }
        return cods.replaceFirst(",", "");
    }


    public static String obterCodigosRemessaItem(final StringBuilder retorno, ConvenioCobrancaVO convenio) throws IOException {
        if (convenio.getBanco().getCodigoBanco().equals(BancoEnum.SANTANDER.getCodigo())) {
            return obterValorCamposLinhas(retorno, 116, 126, 126, 133);
        } else if (convenio.getBanco().getCodigoBanco().equals(BancoEnum.BANCODOBRASIL.getCodigo())) {
            return LayoutRemessaBBCNAB240.obterCodigosRemessaItem(retorno);
        } else if (convenio.getBanco().getCodigoBanco().equals(BancoEnum.BANCOOB.getCodigo()) && !convenio.getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) && !convenio.getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE)) {
            return obterValorCamposLinhas(retorno, 62, 73);
        } else if (convenio.getBanco().getCodigoBanco().equals(BancoEnum.BANCOOB.getCodigo())
                && (convenio.getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) || convenio.getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE))) {
            return LayoutRemessaSicoobCNAB240.obterCodigosRemessaItem(retorno);
        } else if (convenio.getBanco().getCodigoBanco().equals(BancoEnum.BRB.getCodigo())) {
            //NossoNúmero é da 70 até 82, porém tem Dígitos Verificadores, Carteiras e etc
            return obterValorCamposLinhas(retorno, 71, 77);
        } else if (convenio.getBanco().getCodigoBanco().equals(BancoEnum.BNB.getCodigo())) {
            return obterValorCamposLinhas(retorno, 62, 69);
        } else if (convenio.getBanco().getCodigoBanco().equals(BancoEnum.CAIXA.getCodigo())) {
            return LayoutRemessaCaixaCNAB240.obterCodigosRemessaItem(retorno);
        } else if (convenio.getBanco().getCodigoBanco().equals(BancoEnum.ITAU.getCodigo())
                && !convenio.getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_DAYCOVAL)) {
            return LayoutRemessaItauBoletoCNAB400.obterCodigosRemessaItem(retorno);
        } else if (convenio.getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_DAYCOVAL)) {
            return LayoutRemessaDaycovalBoletoCNAB400.obterCodigosRemessaItem(retorno);
        } else if (convenio.getTipo() != null &&
                convenio.getTipo().equals(TipoConvenioCobrancaEnum.DCO_BRADESCO_240)) {
            return LayoutRemessaBradescoCNAB240.obterCodigosRemessaItem(retorno);
        } else if (convenio.getBanco().getCodigoBanco().equals(BancoEnum.SAFRA.getCodigo())) {
            return obterValorCamposLinhas(retorno, 62, 71);
        } else if (convenio.getBanco().getCodigoBanco().equals(BancoEnum.SICREDI.getCodigo())) {
            return obterValorCamposLinhas(retorno, 50, 55);
        } else {
            return obterCodigosMovParcelas(retorno);
        }
    }

    private static void lerAtributosBanco(String linha, RegistroRemessa h, String banco) {
        if (banco.equals("BRADESCO")) {
            lerAtributosBradesco(linha, h);
        } else {
            lerAtributos(linha, h);
        }
    }

    private static void lerAtributosBradesco(final String linha, RegistroRemessa r) {
        LayoutRemessaBradescoCNAB400.lerAtributos(linha, r);
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {
        //TODO criar um padrão...
    }

    public static StringBuilder prepareFile(RemessaVO remessa,String codigoEmpresaFinanceiro) throws Exception {
        RemessaService service = new RemessaService();
        StringBuilder sb = new StringBuilder();

        //TODO definir formato em relação aos Layouts
        if (remessa.getConvenioCobranca().getBanco().getCodigoBanco() == BancoEnum.ITAU.getCodigo() ||
                remessa.getConvenioCobranca().getBanco().getCodigoBanco() == BancoEnum.BRADESCO.getCodigo() ||
                remessa.getConvenioCobranca().getBanco().getCodigoBanco() == BancoEnum.SANTANDER.getCodigo() ||
                (remessa.getConvenioCobranca().getBanco().getCodigoBanco() == BancoEnum.BANCOOB.getCodigo() && (!remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) &&
                !remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE))) ||
                remessa.getConvenioCobranca().getBanco().getCodigoBanco() == BancoEnum.BNB.getCodigo() ||
                remessa.getConvenioCobranca().getBanco().getCodigoBanco() == BancoEnum.SICREDI.getCodigo() ||
                remessa.getConvenioCobranca().getBanco().getCodigoBanco() == BancoEnum.SAFRA.getCodigo()) {
            //HEADER
            sb.append(remessa.getHeaderRemessa().toStringBuffer()).append("\r\n");
            //DETAIL
            List<RegistroRemessa> lista = remessa.getDetailsRemessa();
            StringBuilder sbDetail = new StringBuilder();
            for (RegistroRemessa regD : lista) {
                sbDetail.append(regD.toStringBuffer()).append("\r\n");
            }
            sb.append(sbDetail);
            //TRAILER
            sb.append(remessa.getTrailerRemessa().toStringBuffer());
            sb.append("\r\n");
        } else if (remessa.getConvenioCobranca().getBanco().getCodigoBanco() == BancoEnum.BRB.getCodigo()) {
            //HEADER
            sb.append(remessa.getHeaderRemessa().toStringBuffer()).append("\r\n");
            //DETAIL
            List<RegistroRemessa> lista = remessa.getDetailsRemessa();
            StringBuilder sbDetail = new StringBuilder();
            for (RegistroRemessa regD : lista) {
                sbDetail.append(regD.toStringBuffer()).append("\r\n");
            }
            sb.append(sbDetail);
            //TRAILER
        } else if (remessa.getConvenioCobranca().getBanco().getCodigoBanco() == BancoEnum.BANCODOBRASIL.getCodigo() ||
                remessa.getConvenioCobranca().getBanco().getCodigoBanco() == BancoEnum.CAIXA.getCodigo()
                || (remessa.getConvenioCobranca().getBanco().getCodigoBanco() == BancoEnum.BANCOOB.getCodigo() && (remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240)
                || remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE)))) {

            //HEADER
            sb.append(remessa.getHeaderRemessa().toStringBuffer()).append("\r\n");
            sb.append(remessa.getHeaderRemessaArquivo().toStringBuffer()).append("\r\n");
            //DETAIL
            List<RegistroRemessa> lista = remessa.getDetailsRemessa();
            StringBuffer sbDetail = new StringBuffer();
            for (RegistroRemessa regD : lista) {
                sbDetail.append(regD.toStringBuffer()).append("\r\n");
            }
            sb.append(sbDetail);
            //TRAILER
            sb.append(remessa.getTrailerRemessaArquivo().toStringBuffer()).append("\r\n");
            sb.append(remessa.getTrailerRemessa().toStringBuffer()).append("\r\n");
        }else {
            for (int i = 0; i < remessa.getListaItens().size(); i++) {
                remessa.getListaItens().get(i).setIdentificadorEmpresaFinanceiro(codigoEmpresaFinanceiro);
                String boleto = adicionarBoletoBiro(i, remessa, remessa.getListaItens().get(i), service);
                sb.append(boleto);
            }
        }

        return sb;
    }

    public static void preencherArquivoRemessa(RemessaVO remessa) {
        if (remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICREDI) ||
                remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.CARNE_BANCO_SICREDI)) {
            LayoutRemessaSicrediBoleto.gerarArquivoRemessa(remessa);
            return;
        }
        if (remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_DAYCOVAL)) {
            LayoutRemessaDaycovalBoletoCNAB400.preencherArquivoRemessa(remessa);
            return;
        }
        if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.ITAU.getCodigo())) {
            LayoutRemessaItauBoletoCNAB400.preencherArquivoRemessa(remessa, true);
            return;
        }
        if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BRADESCO.getCodigo())) {
            if (remessa.getConvenioCobranca().getTipo().equals(TipoConvenioCobrancaEnum.DCO_BRADESCO_240)) {
                LayoutRemessaBradescoCNAB240.preencherArquivoRemessa(remessa);
            } else {
                LayoutRemessaBradescoCNAB400.preencherArquivoRemessa(remessa);
            }
            return;
        }
        if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SANTANDER.getCodigo())) {
            LayoutRemessaSantanderCNAB400.preencherArquivoRemessa(remessa);
            return;
        }
        if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BRB.getCodigo())) {
            LayoutRemessaBRBCNAB400.preencherArquivoRemessa(remessa);
            return;
        }
        if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BANCODOBRASIL.getCodigo())) {
            LayoutRemessaBBCNAB240.preencherArquivoRemessa(remessa);
            return;
        }
        if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BANCOOB.getCodigo())
                && (!remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) &&
                !remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE))) {
            LayoutRemessaSicoobCNAB400.preencherArquivoRemessa(remessa);
            return;
        }
        if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BANCOOB.getCodigo())
                && (remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) ||
                remessa.getConvenioCobranca().getTipoRemessa().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE))) {
            LayoutRemessaSicoobCNAB240.preencherArquivoRemessa(remessa);
            return;
        }

        if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.CAIXA.getCodigo())) {
            LayoutRemessaCaixaCNAB240.preencherArquivoRemessa(remessa);
            return;
        }

        if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BNB.getCodigo())) {
            LayoutRemessaBNBCNAB400.preencherArquivoRemessa(remessa);
            return;
        }

        if (remessa.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.SAFRA.getCodigo())) {
            LayoutRemessaSafraCNAB400.preencherArquivoRemessa(remessa);
            return;
        }

        RegistroRemessa header = new RegistroRemessa(TipoRegistroEnum.HEADER);
        header.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(remessa.getDataRegistro()));


        double valorTotalBruto = 0.0;
        for (RemessaItemVO boleto : remessa.getListaItens()) {
            for (RemessaItemMovParcelaVO parcela : boleto.getMovParcelas()) {
                valorTotalBruto += parcela.getMovParcelaVO().getValorParcela();
            }
        }

        RegistroRemessa trailer = new RegistroRemessa(TipoRegistroEnum.TRAILER);
        trailer.put(DCCAttEnum.QuantidadeRegistros, (remessa.getListaItens().size()));
        trailer.put(DCCAttEnum.ValorTotalBruto, StringUtilities.formatarCampoMonetario(valorTotalBruto, 10));


        remessa.setHeaderRemessa(header);
        remessa.setHeaderArquivo(header.toStringBuffer());
        remessa.setHead(new StringBuilder(header.toString()));

        remessa.setTrailer(new StringBuilder(trailer.toString()));
    }

    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            h = new RegistroRemessa(TipoRegistroEnum.HEADER);

            String banco = "";
            while ((linha = br.readLine()) != null) {
                if (linha.startsWith("02RETORNO")) {
                    if (linha.contains("BRADESCO")) {
                        banco = "BRADESCO";
                    }
                    lerAtributosBanco(linha, h, banco);
                }
                break;
            }
        }
        return h;
    }

    private static String adicionarBoletoBiro(int sequencial, RemessaVO remessa, RemessaItemVO remessaItemVO, RemessaService service) {
        EnderecoVO enderecoVO = new EnderecoVO();
        for (EnderecoVO endereco : remessaItemVO.getPessoa().getEnderecoVOs()) {
            if (endereco.getEnderecoCorrespondencia() || endereco.getTipoEndereco().equals(TipoEnderecoEnum.RESIDENCIAL.getCodigo())) {
                enderecoVO = endereco;
            }
        }

        JBoletoBean boletoBean = service.gerarBoletoCobranca(remessaItemVO);
        boolean layout240 = (remessa.getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240) ||
                remessa.getConvenioCobranca().getArquivoLayoutRemessa().equals(ArquivoLayoutRemessaEnum.BOLETO_SICOOB_240_CARNE));
        JBoleto jboleto = new JBoleto(null, boletoBean, remessa.getConvenioCobranca().getBanco().getCodigoBanco(), layout240);

        StringBuilder boleto = new StringBuilder();
        //Sequêncial
        boleto.append(StringUtilities.formatarCampoForcandoZerosAEsquerda(String.valueOf(sequencial + 1), 6)).append("\r\n");
        //Agência/Conta
        boleto.append(remessa.getConvenioCobranca().getContaCorrenteBoleto()).append("\r\n");
        //Matrícula do Cliente
        boleto.append(StringUtilities.formatarCampoForcandoZerosAEsquerda(String.valueOf(remessaItemVO.getClienteVO().getCodigoMatricula()), 6)).append("\r\n");
        //Nome do Sacado
        boleto.append(remessaItemVO.getPessoa().getNome()).append("\r\n");
        //Endereço do sacado;
        boleto.append(enderecoVO.getEndereco());
        if (!UteisValidacao.emptyString(enderecoVO.getNumero())) {
            boleto.append(", ").append(enderecoVO.getNumero());
        }
        if (!UteisValidacao.emptyString(enderecoVO.getComplemento())) {
            boleto.append(", ").append(enderecoVO.getComplemento());
        }
        boleto.append("\r\n");
        //Cidade - UF - UF do Sacado;
        boleto.append(enderecoVO.getBairro()).append(" - ").append(remessaItemVO.getPessoa().getCidade().getNome()).append(" - ").append(remessaItemVO.getPessoa().getEstadoVO().getSigla()).append("\r\n");
        //CEP do Sacado
        boleto.append("CEP: ").append(enderecoVO.getCep()).append("\r\n");
        //Mês de Referência da parcela
        boleto.append(Uteis.getMesNomeReferencia(remessaItemVO.getDataVencimentoBoleto()).toUpperCase()).append("/").append(Uteis.getAnoData(remessaItemVO.getDataVencimentoBoleto())).append("\r\n");
        //Data da Geração
        boleto.append(Uteis.getDataAplicandoFormatacao(remessa.getDataRegistro(), "dd/MM/yyyy")).append("\r\n");
        //Data do Vencimento;
        boleto.append(Uteis.getDataAplicandoFormatacao(remessaItemVO.getDataVencimentoBoleto(), "dd/MM/yyyy")).append("\r\n");
        boleto.append(jboleto.getBanco().getNossoNumeroFormatted()).append("\r\n");
        List<String> produtos = montarListaProdutos(remessaItemVO, true);
        int qtdVazio = 41 - produtos.size();
        for (String produto : produtos) {
            boleto.append(produto).append("\r\n");
        }
        for (int i = 0; i < qtdVazio; i++) {
            boleto.append("\r\n");
        }
        //Uma das 3 mensagens...
        boleto.append(boletoBean.getInstrucao5()).append("\r\n");
        boleto.append(boletoBean.getInstrucao4()).append("\r\n");
        boleto.append("Total: ").append(Formatador.formatarValorMonetario(remessaItemVO.getValorBoleto())).append("\r\n");

        String[] instrucoesBoleto = boletoBean.getInstrucao1().split("\r\n");
        int qtdVazioInstrucoes = 6 - instrucoesBoleto.length;
        for (String instr : instrucoesBoleto) {
            boleto.append(instr).append("\r\n");
        }
        for (int i = 0; i < qtdVazioInstrucoes; i++) {
            boleto.append("\r\n");
        }

        boleto.append(Formatador.formatarValorMonetario(remessaItemVO.getValorBoleto())).append("\r\n");
        boleto.append(jboleto.getBoleto().getCodigoBarras()).append("\r\n");
        boleto.append(jboleto.getBoleto().getLinhaDigitavel()).append("\r\n");
        for (int i = 0; i < 33; i++) {
            boleto.append("\r\n");
        }

        return boleto.toString();
    }

    public static List<String> montarListaProdutos(RemessaItemVO itemVO, boolean valorNoInicio) {
        Map<PessoaVO, List<MovParcelaVO>> arvore = new HashMap<PessoaVO, List<MovParcelaVO>>();

        for (RemessaItemMovParcelaVO itemParcela : itemVO.getMovParcelas()) {
            List<MovParcelaVO> parcelasPessoa = arvore.get(itemParcela.getMovParcelaVO().getPessoa());
            if (parcelasPessoa == null || parcelasPessoa.isEmpty()) {
                parcelasPessoa = new ArrayList<MovParcelaVO>();
                parcelasPessoa.add(itemParcela.getMovParcelaVO());
                arvore.put(itemParcela.getMovParcelaVO().getPessoa(), parcelasPessoa);
            } else {
                parcelasPessoa.add(itemParcela.getMovParcelaVO());
            }
        }


        Map<PessoaVO, List<MovProdutoParcelaVO>> arvoreProdutos = new HashMap<PessoaVO, List<MovProdutoParcelaVO>>();
        for (PessoaVO pessoaVO : arvore.keySet()) {
            List<MovProdutoParcelaVO> produtosPessoa = arvoreProdutos.get(pessoaVO);
            if (produtosPessoa == null || produtosPessoa.isEmpty()) {
                produtosPessoa = new ArrayList<MovProdutoParcelaVO>();
            }
            List<MovParcelaVO> parcelas = arvore.get(pessoaVO);
            for (MovParcelaVO parcela : parcelas) {
                produtosPessoa.addAll(parcela.getMovProdutoParcelaVOs());
            }
            arvoreProdutos.put(pessoaVO, produtosPessoa);
        }

        Map<Integer, List<MovProdutoVO>> movProdutosPessoas = new HashMap<Integer, List<MovProdutoVO>>();
        for (PessoaVO pessoaVO : arvoreProdutos.keySet()) {
            List<MovProdutoVO> movProdutosPessoa = movProdutosPessoas.get(pessoaVO.getCodigo());
            if (movProdutosPessoa == null) {
                movProdutosPessoa = new ArrayList<MovProdutoVO>();
                movProdutosPessoas.put(pessoaVO.getCodigo(), movProdutosPessoa);
            }
            List<MovProdutoParcelaVO> mpps = arvoreProdutos.get(pessoaVO);
            for (MovProdutoParcelaVO mppPessoa : mpps) {
                MovProdutoVO movProd = mppPessoa.getMovProdutoVO();
                if (!movProdutosPessoa.contains(movProd)) {
                    movProd.setTotalFinal(mppPessoa.getValorPago());
                    movProdutosPessoa.add(movProd);
                } else {
                    int pos = movProdutosPessoa.indexOf(movProd);
                    MovProdutoVO mpVO = movProdutosPessoa.get(pos);
                    mpVO.setTotalFinal(mpVO.getTotalFinal() + mppPessoa.getValorPago());
                }
            }
        }

        Map<Integer, ClienteVO> mapPessoaCliente = new HashMap<Integer, ClienteVO>();
        for (RemessaItemMovParcelaVO remessaItemMovParcelaVO : itemVO.getMovParcelas()) {
            mapPessoaCliente.put(remessaItemMovParcelaVO.getMovParcelaVO().getPessoa().getCodigo(), remessaItemMovParcelaVO.getClienteVO());
        }

        ArrayList<String> descricao = new ArrayList<String>();

        List<Integer> pessoasOrdenadas = new ArrayList<Integer>();
        pessoasOrdenadas.add(itemVO.getClienteVO().getPessoa().getCodigo());
        for (Integer codPessoa : movProdutosPessoas.keySet()) {
            if (!pessoasOrdenadas.contains(codPessoa)) {
                pessoasOrdenadas.add(codPessoa);
            }
        }
        for (Integer pessoaVO : pessoasOrdenadas) {
            ClienteVO clienteVO = mapPessoaCliente.get(pessoaVO);
            if (clienteVO == null) {
                continue;
            }
            descricao.add(clienteVO.getMatricula() + " - " + clienteVO.getPessoa().getNome());
            List<String> itens = new ArrayList<String>();
            List<MovProdutoVO> produtosPessoa = movProdutosPessoas.get(pessoaVO);
            Ordenacao.ordenarLista(produtosPessoa, "ordenacaoBoleto");
            for (MovProdutoVO movProdutoVO : produtosPessoa) {
                if (!movProdutoVO.getMovProdutoModalidades().isEmpty()) {
                    for (MovProdutoModalidadeVO mpMod : movProdutoVO.getMovProdutoModalidades()) {
                        String complemento = "";
                        if (movProdutoVO.getMulta() > 0 || movProdutoVO.getJuros() > 0) {
                            complemento = " - MULTA E JUROS";
                        }
                        if (valorNoInicio) {
                            itens.add("    " + Formatador.formatarValorMonetario(mpMod.getValor()) + ": " + mpMod.getModalidadeVO().getNome() + complemento);
                        } else {
                            itens.add("    " + mpMod.getModalidadeVO().getNome() + complemento + ": " + Formatador.formatarValorMonetario(mpMod.getValor()));
                        }
                    }
                } else {
                    if (valorNoInicio) {
                        itens.add("    " + Formatador.formatarValorMonetario(movProdutoVO.getTotalFinal()) + ": " + movProdutoVO.getDescricao());
                    } else {
                        itens.add("    " + movProdutoVO.getDescricao() + ": " + Formatador.formatarValorMonetario(movProdutoVO.getTotalFinal()));
                    }
                }
            }
            descricao.addAll(itens);
        }
        if (itemVO.getValorCredito() > 0) {
            descricao.add("    ");
            if (valorNoInicio) {
                descricao.add("    -" + Formatador.formatarValorMonetario(itemVO.getValorCredito()) + ": Crédito Referente a Pagamentos Passados");
            } else {
                descricao.add("    " + "Crédito Referente a Pagamentos Passados: -" + Formatador.formatarValorMonetario(itemVO.getValorCredito()));
            }
        }
        return descricao;
    }


    public RemessaVO getRemessa() {
        return remessa;
    }

    public void setRemessa(RemessaVO remessa) {
        this.remessa = remessa;
    }
    
    public static Map<EmpresaFinanceiroVO, String> obterCodigosItemPorEmpresas(final StringBuilder retorno, List<EmpresaFinanceiroVO> empresasFinanceiro) throws IOException {
        Map<String, List<Long>> identificadorCodigos = new HashMap<String, List<Long>>();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            while ((linha = br.readLine()) != null) {
                Long tipo = Long.valueOf(StringUtilities.readString(0, 1, linha));
                if (tipo.equals(1L)) {
                    if(!identificadorCodigos.containsKey(StringUtilities.readString(70, 73, linha))){
                         identificadorCodigos.put(StringUtilities.readString(70, 73, linha), new ArrayList<Long>());
                    }
                    identificadorCodigos.get(StringUtilities.readString(70, 73, linha)).add(Long.valueOf(StringUtilities.readString(73, 81, linha)));
                }
            }
        }
        Map<EmpresaFinanceiroVO, String> empresasCodigos = new HashMap<EmpresaFinanceiroVO, String>();
        if(!empresasFinanceiro.isEmpty() && empresasFinanceiro.get(empresasFinanceiro.size() - 1).getCodigo() != 0){
            EmpresaFinanceiroVO empresaVazia = new EmpresaFinanceiroVO();
            empresaVazia.setNomeFantasia("Não identificada");
            empresaVazia.setCodigo(0);
            empresaVazia.setIdentificadorRemessa(0);
            empresasFinanceiro.add(empresaVazia);
        }
        String identificador = "";
        for(EmpresaFinanceiroVO empresa: empresasFinanceiro){
            identificador =StringUtilities.formatarCampo(new BigDecimal(empresa.getIdentificadorRemessa()), 3);
            if(identificadorCodigos.containsKey(identificador)){
                Collections.sort(identificadorCodigos.get(identificador));
                String cods = "";
                for (Long cod : identificadorCodigos.get(identificador)) {
                    cods = cods + "," + cod;
                }
                empresasCodigos.put(empresa, cods.replaceFirst(",", ""));
            }
        }
        return empresasCodigos;
    }

    /**
     * Realiza a descoberta do encode que será utilizado para a criação do arquivo.
     * @param remessaVO
     * @return
     */
    public static String obterEncodeArquivo(RemessaVO remessaVO) {
        return remessaVO.getConvenioCobranca().getBanco().getCodigoBanco().equals(BancoEnum.BANCOOB.getCodigo()) ? "ISO-8859-1" : "UTF-8";
    }
}
