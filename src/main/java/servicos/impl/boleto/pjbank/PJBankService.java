package servicos.impl.boleto.pjbank;

import br.com.pactosolucoes.comuns.to.BoletoOnlineTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import br.com.pactosolucoes.pjbank.WebhookPJBankJSON;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.CidadeVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.BoletoVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PessoaCPFTO;
import negocio.comuns.financeiro.enumerador.SituacaoBoletoEnum;
import negocio.comuns.financeiro.enumerador.TipoBoletoEnum;
import negocio.comuns.financeiro.enumerador.TipoBoletoPJBankEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Ordenacao;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.basico.Cidade;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Endereco;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.Boleto;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.impl.boleto.AbstractBoletoOnlineServiceComum;
import servicos.impl.boleto.AtributoBoletoEnum;
import servicos.integracao.pjbank.api.PJBankClient;
import servicos.integracao.pjbank.beanRecebimento.BoletoRecebimento;
import servicos.integracao.pjbank.recebimento.BoletosManager;
import servicos.interfaces.BoletoOnlineServiceInterface;

import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: Luiz Felipe
 * Date: 07/12/2021
 */
public class PJBankService extends AbstractBoletoOnlineServiceComum implements BoletoOnlineServiceInterface {

    private String chaveBanco;
    private Boleto boletoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private Pessoa pessoaDAO;
    private Cliente clienteDAO;
    private Endereco enderecoDAO;
    private Cidade cidadeDAO;
    private Empresa empresaDAO;
    private Usuario usuarioDAO;
    private ConvenioCobrancaVO convenioCobrancaVO;

    public PJBankService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        inicializarDAO(con);
        this.convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
    }

    private void inicializarDAO(Connection con) throws Exception {
        this.chaveBanco = DAO.resolveKeyFromConnection(con);
        this.boletoDAO = new Boleto(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.enderecoDAO = new Endereco(con);
        this.cidadeDAO = new Cidade(con);
        this.usuarioDAO = new Usuario(con);
        this.empresaDAO = new Empresa(con);
    }

    @Override
    public BoletoVO criar(BoletoOnlineTO boletoOnlineTO) throws Exception {
        BoletoVO boletoVO = null;
        try {
            boletoVO = criarBoletoVO(boletoOnlineTO, this.convenioCobrancaVO, this.getCon());
            if (boletoOnlineTO.isVerificarBoletoExistente()) {
                BoletoVO boletoExistenteVO = verificarBoletoExistente(boletoVO);
                if (boletoExistenteVO != null &&
                        !UteisValidacao.emptyNumber(boletoExistenteVO.getCodigo())) {
                    return boletoExistenteVO;
                }
            }

            this.boletoDAO.incluir(boletoVO);

            EmpresaVO empresaVO = this.empresaDAO.consultarPorChavePrimaria(boletoVO.getEmpresaVO().getCodigo(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            PessoaCPFTO pessoaCPFTO = this.boletoDAO.obterDadosPessoaPagador(empresaVO.getCodigo(), boletoVO.getPessoaVO(), true, true);

            servicos.integracao.pjbank.bean.Cliente clientePJBank = new servicos.integracao.pjbank.bean.Cliente();
            clientePJBank.setNome(pessoaCPFTO.getNomeResponsavel());
            clientePJBank.setCpfCnpj(pessoaCPFTO.getCpfResponsavel().replaceAll("[^0-9]", ""));

            PessoaVO pessoaVO = this.pessoaDAO.consultarPorChavePrimaria(pessoaCPFTO.getPessoa(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);

            CidadeVO cidadeVO = new CidadeVO();
            if (!UteisValidacao.emptyNumber(pessoaVO.getCidade().getCodigo())) {
                cidadeVO = this.cidadeDAO.consultarPorChavePrimaria(pessoaVO.getCidade().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            servicos.integracao.pjbank.bean.Endereco enderecoPJBank = new servicos.integracao.pjbank.bean.Endereco();
            List<EnderecoVO> listaEnderecos = this.enderecoDAO.consultarEnderecos(pessoaVO.getCodigo(), false, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            if (!UteisValidacao.emptyList(listaEnderecos)) {
                Ordenacao.ordenarListaReverse(listaEnderecos, "codigo");
                EnderecoVO enderecoVO = listaEnderecos.get(0);
                enderecoPJBank.setLogradouro(enderecoVO.getEndereco());
                enderecoPJBank.setNumero(enderecoVO.getNumero());
                enderecoPJBank.setComplemento(enderecoVO.getComplemento());
                enderecoPJBank.setBairro(enderecoVO.getBairro());
                enderecoPJBank.setCep(enderecoVO.getCep().replaceAll("[^0-9]", ""));
                enderecoPJBank.setCidade(cidadeVO.getNome());
                enderecoPJBank.setEstado(cidadeVO.getEstado().getSigla());
            }

            clientePJBank.setEndereco(enderecoPJBank);

            BoletoRecebimento pJBankEnvio = new BoletoRecebimento();
            pJBankEnvio.setCliente(clientePJBank);
            pJBankEnvio.setValor(boletoOnlineTO.getValor());
            pJBankEnvio.setVencimento(boletoOnlineTO.getDataVencimento());

            if (empresaVO.getCobrarAutomaticamenteMultaJuros()) {
                pJBankEnvio.setJurosFixo(empresaVO.isUtilizarJurosValorAbsoluto() ? 1 : 0); //define se vai ser em reais ou porcentagem --> 1 = reais | 0 = porcentagem
                pJBankEnvio.setMultaFixo(empresaVO.isUtilizarMultaValorAbsoluto() ? 1 : 0); //define se vai ser em reais ou porcentagem --> 1 = reais | 0 = porcentagem
                pJBankEnvio.setJuros(Uteis.arredondarForcando2CasasDecimais(empresaVO.getJurosCobrancaAutomatica() * 30));
                pJBankEnvio.setMulta(Uteis.arredondarForcando2CasasDecimais(empresaVO.getMultaCobrancaAutomatica()));
            } else {
                pJBankEnvio.setJuros(0);
                pJBankEnvio.setMulta(0);
            }

            String instrucoesBoleto = this.convenioCobrancaVO.getInstrucoesBoleto();
            try {
                if (instrucoesBoleto.toUpperCase().contains("TAG_MATRICULA")) {
                    ClienteVO clienteVO = this.clienteDAO.consultarPorCodigoPessoa(pessoaVO.getCodigo(), empresaVO.getCodigo(), Uteis.NIVELMONTARDADOS_MINIMOS);
                    if (!UteisValidacao.emptyNumber(clienteVO.getCodigo())) {
                        instrucoesBoleto = processarInstrucaoMatricula(instrucoesBoleto, clienteVO.getMatricula());
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            pJBankEnvio.setInstrucao_adicional(instrucoesBoleto);
            pJBankEnvio.setPedidoNumero(gerarNumeroAleatorio());
            pJBankEnvio.setLogoUrl(obterURLLogoEmpresa(this.chaveBanco, empresaVO));
            pJBankEnvio.setWebhook(obterUrlWebhookPacto(this.chaveBanco, TipoBoletoEnum.PJ_BANK, this.convenioCobrancaVO, boletoVO));

            //desconto boleto
            if (
                    !UteisValidacao.emptyNumber(boletoVO.getDiaMesDescontoPagAntecipado())
                    && (
                            !UteisValidacao.emptyNumber(boletoVO.getPorcentagemDescontoPagAntecipado())
                            || !UteisValidacao.emptyNumber(boletoVO.getValorDescontoPagAntecipado())
                    )
            ) {
                Integer diaDesconto = 0;
                Integer diaVencimentoBoleto = Uteis.obterDiaData(pJBankEnvio.getVencimento());
                if (diaVencimentoBoleto >= boletoVO.getDiaMesDescontoPagAntecipado()) {
                    diaDesconto = new Long(Uteis.nrDiasEntreDatas(Calendario.setDiaMes(pJBankEnvio.getVencimento(), boletoVO.getDiaMesDescontoPagAntecipado()), pJBankEnvio.getVencimento())).intValue();
                } else {
                    //pegar o dia do mês anterior
                    diaDesconto = new Long(Uteis.nrDiasEntreDatas(Calendario.setDiaMes(Uteis.somarMeses(pJBankEnvio.getVencimento(), -1), boletoVO.getDiaMesDescontoPagAntecipado()), pJBankEnvio.getVencimento())).intValue();
                }
                pJBankEnvio.setDiaDesconto(diaDesconto);

                //calcular desconto
                if (boletoVO.getPorcentagemDescontoPagAntecipado() > 0) {
                    pJBankEnvio.setDesconto(Uteis.arredondarForcando2CasasDecimais((pJBankEnvio.getValor() * boletoVO.getPorcentagemDescontoPagAntecipado()) / 100));
                } else {
                    pJBankEnvio.setDesconto(boletoVO.getValorDescontoPagAntecipado());
                }
            } else {
                pJBankEnvio.setDesconto(0.0);
            }

            pJBankEnvio.setChavePJBank(this.convenioCobrancaVO.getChavePJBank());
            pJBankEnvio.setCredencialPJBank(this.convenioCobrancaVO.getCredencialPJBank());

            try {
                BoletoRecebimento pjBankResposta = enviarPJBank(boletoVO, pJBankEnvio, this.convenioCobrancaVO.getTipoBoletoPJBank());
                boletoVO.setIdExterno(pjBankResposta.getIdUnico());
                boletoVO.setLinkBoleto(pjBankResposta.getLinkBoleto());
                boletoVO.setNossoNumero(pjBankResposta.getNossoNumero());
                boletoVO.setNumeroInterno(pjBankResposta.getPedidoNumero().toString());
                boletoVO.setNumeroExterno(pjBankResposta.getPedidoNumeroPJBank());
                boletoVO.setLinhaDigitavel(pjBankResposta.getLinhaDigitavel());
                boletoVO.setSituacao(SituacaoBoletoEnum.AGUARDANDO_REGISTRO);
            } catch (Exception ex) {
                ex.printStackTrace();
                boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.msg_erro, ex.getMessage());
                boletoVO.setSituacao(SituacaoBoletoEnum.ERRO);
            }

            this.boletoDAO.alterar(boletoVO);
            return boletoVO;
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarBoletoComErro(boletoVO, ex);
            throw ex;
        }
    }

    private BoletoRecebimento enviarPJBank(BoletoVO boletoVO, BoletoRecebimento boletoRecebimento, TipoBoletoPJBankEnum tipoBoletoPJBankEnum) throws Exception {
        Boleto boletoDAO = null;
        try {
            boletoDAO = new Boleto(this.getCon());

            PJBankClient client = new PJBankClient("recebimentos/" + boletoRecebimento.getCredencialPJBank() + "/transacoes", this.convenioCobrancaVO.getAmbiente());
            HttpPost httpPost = client.getHttpPostClient();
            DateFormat dateFormat = new SimpleDateFormat("MM/dd/yyyy");

            JSONObject params = new JSONObject();

            params.put("vencimento", dateFormat.format(boletoRecebimento.getVencimento()));
            params.put("valor", boletoRecebimento.getValor());
            params.put("juros", boletoRecebimento.getJuros());
            params.put("juros_fixo", boletoRecebimento.getJurosFixo());
            params.put("multa", boletoRecebimento.getMulta());
            params.put("multa_fixo", boletoRecebimento.getMultaFixo());
            params.put("desconto", boletoRecebimento.getDesconto());
            params.put("diasdesconto1", boletoRecebimento.getDiaDesconto());
            params.put("nome_cliente", boletoRecebimento.getCliente().getNome());
            params.put("cpf_cliente", boletoRecebimento.getCliente().getCpfCnpj());
            params.put("endereco_cliente", boletoRecebimento.getCliente().getEndereco().getLogradouro());
            params.put("numero_cliente", boletoRecebimento.getCliente().getEndereco().getNumero());
            params.put("complemento_cliente", boletoRecebimento.getCliente().getEndereco().getComplemento());
            params.put("bairro_cliente", boletoRecebimento.getCliente().getEndereco().getBairro());
            params.put("cidade_cliente", boletoRecebimento.getCliente().getEndereco().getCidade());
            params.put("estado_cliente", boletoRecebimento.getCliente().getEndereco().getEstado());
            params.put("cep_cliente", boletoRecebimento.getCliente().getEndereco().getCep());
            params.put("logo_url", boletoRecebimento.getLogoUrl());
            params.put("texto", boletoRecebimento.getTexto());
            params.put("instrucoes", boletoRecebimento.getInstrucoes());
            params.put("instrucao_adicional", boletoRecebimento.getInstrucao_adicional());
            params.put("grupo", boletoRecebimento.getGrupo());
            params.put("pedido_numero", boletoRecebimento.getPedidoNumero());
            params.put("webhook", boletoRecebimento.getWebhook());
            if (tipoBoletoPJBankEnum.equals(TipoBoletoPJBankEnum.BOLETO_PIX) ||
                    tipoBoletoPJBankEnum.equals(TipoBoletoPJBankEnum.PIX)) {
                params.put("pix", tipoBoletoPJBankEnum.getParametro());
            }


            //Opcional. Caso não queira a atualização do vencimento do boleto de forma automática, utilizar este parâmetro, informando o valor 1. length (0-1).
            params.put("nunca_atualizar_boleto", 1);

            boletoVO.setParamsEnvio(params.toString());

            httpPost.setEntity(new StringEntity(params.toString(), StandardCharsets.UTF_8));

            String response = EntityUtils.toString(client.doRequest(httpPost).getEntity());
            boletoDAO.incluirBoletoHistorico(boletoVO, "enviarPJBank | resposta", response);
            boletoVO.setParamsResposta(response);

            JSONObject responseObject = new JSONObject(response);
            boletoRecebimento.setIdUnico(responseObject.getString("id_unico"));
            boletoRecebimento.setNossoNumero(responseObject.optString("nossonumero"));
            boletoRecebimento.setLinkBoleto(responseObject.getString("linkBoleto"));
            boletoRecebimento.setLinkGrupo(responseObject.getString("linkGrupo"));
            boletoRecebimento.setLinhaDigitavel(responseObject.getString("linhaDigitavel"));
            boletoRecebimento.setPedidoNumeroPJBank(responseObject.optString("pedido_numero"));
            boletoRecebimento.setBanco(responseObject.optString("banco_numero"));

            boletoRecebimento.setChavePJBank(this.convenioCobrancaVO.getChavePJBank());
            boletoRecebimento.setCredencialPJBank(responseObject.optString("credencial"));
            if (UteisValidacao.emptyString(boletoRecebimento.getCredencialPJBank())) {
                boletoRecebimento.setCredencialPJBank(this.convenioCobrancaVO.getCredencialPJBank());
            }
            return boletoRecebimento;
        } catch (Exception ex) {
            ex.printStackTrace();
            if (boletoDAO != null) {
                boletoDAO.incluirBoletoHistorico(boletoVO, "enviarPJBank | erro", ex.getMessage());
            }
            throw ex;
        } finally {
            boletoDAO = null;
        }
    }

    private Integer gerarNumeroAleatorio() {
        SimpleDateFormat sdf = new SimpleDateFormat("ddHHmmss");
        Double vlrRandom = 0.0;
        vlrRandom = Math.random() * Double.valueOf(sdf.format(Calendario.hoje()));
        return vlrRandom.intValue();
    }

    private String processarInstrucaoMatricula(String instrucao, String matriculaCliente) throws Exception {
        if (instrucao.contains("<matricula>")) {
            int inicioMatricula = instrucao.indexOf("<matricula>");
            int finalMatricula = instrucao.indexOf("</matricula>");
            String aux = instrucao.substring(0, inicioMatricula);
            aux = aux + instrucao.substring((inicioMatricula + 11), finalMatricula);
            aux = aux.replaceAll("TAG_MATRICULA", matriculaCliente != null ? matriculaCliente : "");
            aux += instrucao.substring(finalMatricula + 12);
            instrucao = aux;
        }
        return instrucao;
    }

    @Override
    public void cancelar(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao, boolean origemProcessoManutencao) throws Exception {
        Boleto boletoDAO;
        try {
            boletoDAO = new Boleto(this.getCon());
            JSONObject jsonBase = boletoDAO.gerarBaseJSON(usuarioVO, operacao);
            boletoDAO.incluirBoletoHistorico(boletoVO, "cancelar", jsonBase.toString());

            BoletosManager boletosManager = new BoletosManager(this.convenioCobrancaVO.getCredencialPJBank(), this.convenioCobrancaVO.getChavePJBank(), this.convenioCobrancaVO);

            if (!origemProcessoManutencao) {
                //validar se o boleto já está cancelado
                if (validarSeBoletoEstaCancelado(boletoVO, boletosManager)) {
                    //se já estiver como cancelado, realizar a operação de atualizar o boleto para CANCELADO
                    boletoDAO.incluirBoletoHistorico(boletoVO, "cancelar | resposta", "Foi solicitado o cancelamento do boleto porém ele já estava cancelado na PjBank, " +
                            "portanto foi atualizado a situação do boleto no sistema para CANCELADO");
                    String operacaoGravar = ("Cancelamento de Boleto - " + boletoVO.getCodigo());
                    JSONObject jsonEstorno = boletoDAO.gerarBaseJSON(usuarioVO, operacaoGravar);
                    boletoVO.setJsonEstorno(jsonEstorno.toString());
                    boletoDAO.alterarJsonEstorno(boletoVO);
                    boletoDAO.alterarSituacao(boletoVO, SituacaoBoletoEnum.CANCELADO);
                    estornarRecibo(boletoVO, usuarioVO);
                    return;

                } else {
                    //verificar se o boleto não foi pago
                    validarSeBoletoEstaPago(boletoVO, boletosManager, boletoDAO);
                }
            }

            String response = boletosManager.cancelar(boletoVO);
            boletoDAO.incluirBoletoHistorico(boletoVO, "cancelar | resposta", response);

            JSONObject json = null;
            try {
                json = new JSONObject(response);
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            if (json != null && json.optString("status").equalsIgnoreCase("200")) {
                String operacaoGravar = ("Cancelamento de Boleto - " + boletoVO.getCodigo());
                JSONObject jsonEstorno;
                if (origemProcessoManutencao) {
                    jsonEstorno = boletoDAO.gerarBaseJSONProcesso(usuarioVO, operacaoGravar);
                } else {
                    jsonEstorno = boletoDAO.gerarBaseJSON(usuarioVO, operacaoGravar);
                }
                boletoVO.setJsonEstorno(jsonEstorno.toString());
                boletoDAO.alterarJsonEstorno(boletoVO);
                if (!origemProcessoManutencao) {
                    boletoDAO.alterarSituacao(boletoVO, SituacaoBoletoEnum.CANCELADO);
                    estornarRecibo(boletoVO, usuarioVO);
                }
            } else {
                throw new Exception("Não foi possível cancelar, tente novamente mais tarde. " + response);
            }

        } finally {
            boletoDAO = null;
        }
    }

    private void validarSeBoletoEstaPago(BoletoVO obj, BoletosManager boletosManager, Boleto boletoDAO) throws Exception {
        //consultar dados do boleto
        String response = boletosManager.get(obj);
        tratarErroRequisicao(response);

        JSONObject json = new JSONArray(response).getJSONObject(0);
        if (!UteisValidacao.emptyString(json.optString("data_pagamento"))) {
            boletoDAO.alterarSituacao(obj, SituacaoBoletoEnum.PAGO);
            throw new Exception("Boleto não pode ser cancelado pois o seu status está como PAGO");
        }
    }

    private boolean validarSeBoletoEstaCancelado(BoletoVO obj, BoletosManager boletosManager) throws Exception {
        //consultar dados do boleto
        String response = boletosManager.get(obj);

        //verifica se o boleto já está cancelado. Se já estiver, o retorno vem vazio pois o boleto é totalmente apagado na pjbank.
        return response != null && response.equals("[]");
    }

    @Override
    public void processarWebhook(BoletoVO boletoVO, String json) throws Exception {
        processarBoletoPJBank(boletoVO, json, this.usuarioDAO.getUsuarioRecorrencia(), "processarWebhook");
    }

    private SituacaoBoletoEnum obterSituacaoBoleto(WebhookPJBankJSON webhookPJBankJSON) {
        SituacaoBoletoEnum situacaoBoletoEnum = null;
        String statusPJBank = webhookPJBankJSON.getRegistro_sistema_bancario();
        if (statusPJBank.equalsIgnoreCase("enviado") || statusPJBank.equalsIgnoreCase("pendente") || statusPJBank.equalsIgnoreCase("rejeitado") ||
                (statusPJBank.equalsIgnoreCase("baixado") && !UteisValidacao.emptyString(webhookPJBankJSON.getData_pagamento()))) {
            if (webhookPJBankJSON.getValor_pago_Float() > 0 && !UteisValidacao.emptyString(webhookPJBankJSON.getData_pagamento())){
                situacaoBoletoEnum = SituacaoBoletoEnum.PAGO;
            } else {
                situacaoBoletoEnum = SituacaoBoletoEnum.AGUARDANDO_REGISTRO;
            }
        } else if (statusPJBank.equalsIgnoreCase("Estornado")) {
            situacaoBoletoEnum = SituacaoBoletoEnum.ESTORNADO;
        } else if (statusPJBank.equalsIgnoreCase("Cancelado") || statusPJBank.equalsIgnoreCase("baixado")) {
            situacaoBoletoEnum = SituacaoBoletoEnum.CANCELADO;
        } else if (statusPJBank.equalsIgnoreCase("confirmado")) {
            if (webhookPJBankJSON.getValor_pago_Float() > 0) {
                situacaoBoletoEnum = SituacaoBoletoEnum.PAGO;
            } else {
                situacaoBoletoEnum = SituacaoBoletoEnum.AGUARDANDO_PAGAMENTO;
            }
        } else if (statusPJBank.equalsIgnoreCase("rejeitado")) {
            situacaoBoletoEnum = SituacaoBoletoEnum.REJEITADO;
        }
        return situacaoBoletoEnum;
    }

    @Override
    public void sincronizar(BoletoVO boletoVO, UsuarioVO usuarioVO, String operacao) throws Exception {
        Boleto boletoDAO;
        try {
            boletoDAO = new Boleto(this.getCon());

            if (UteisValidacao.emptyString(boletoVO.getIdExterno())) {
                throw new Exception("Boleto não tem idExterno (id_unico)");
            }

            JSONObject jsonBase = boletoDAO.gerarBaseJSON(usuarioVO, operacao);
            jsonBase.put("idExterno", boletoVO.getIdExterno());
            boletoDAO.incluirBoletoHistorico(boletoVO, "sincronizar", jsonBase.toString());


            PJBankClient client = new PJBankClient("recebimentos/" + this.convenioCobrancaVO.getCredencialPJBank() + "/transacoes/" + boletoVO.getIdExterno(), this.convenioCobrancaVO.getAmbiente());
            HttpGet httpGet = client.getHttpGetClient();
            httpGet.addHeader("x-chave", this.convenioCobrancaVO.getChavePJBank());
            String resposta = EntityUtils.toString(client.doRequest(httpGet).getEntity());

            if (resposta != null && resposta.equals("[]")) {
                resposta = "Foi solicitado a sincronização do boleto que já estava cancelado na PjBank, portanto foi atualizado a situação do boleto no sistema para CANCELADO";
                boletoDAO.incluirBoletoHistorico(boletoVO, "sincronizar | resposta", resposta);
                processarBoletoPJBank(boletoVO, "já cancelado", usuarioVO, operacao);
            } else {
                boletoDAO.incluirBoletoHistorico(boletoVO, "sincronizar | resposta", resposta);
                JSONArray array = null;
                try {
                    array = new JSONArray(resposta);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                if (array == null) {
                    throw new Exception("Não foi possível sincronizar, tente novamente mais tarde. " + resposta);
                } else if (array.length() == 0) {
                    throw new Exception("Não foi encontrado boleto com o Id Único: " + boletoVO.getIdExterno());
                } else if (array.length() == 1) {
                    processarBoletoPJBank(boletoVO, array.getJSONObject(0).toString(), usuarioVO, operacao);
                } else {
                    throw new Exception("Erro ao sincronizar boleto: " + resposta);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        } finally {
            boletoDAO = null;
        }
    }

    private void processarBoletoPJBank(BoletoVO boletoVO, String json, UsuarioVO usuarioVO, String operacao) throws Exception {
        Boleto boletoDAO = null;
        BoletoVO boletoVOAnterior = null;
        try {
            boletoDAO = new Boleto(this.getCon());
            boletoDAO.incluirBoletoHistorico(boletoVO, "processarBoletoPJBank | " + operacao, json);

            //em caso do boleto já estiver cancelado
            if (json.equals("já cancelado")){
                boletoDAO.alterarSituacao(boletoVO, SituacaoBoletoEnum.CANCELADO);
                return;
            }

            WebhookPJBankJSON webhookPJBankJSON = new WebhookPJBankJSON(new JSONObject(json));

            if (boletoVO == null) {
                if (UteisValidacao.emptyString(webhookPJBankJSON.getId_unico())) {
                    throw new Exception("JSON Boleto PJBank não tem IdUnico informado");
                }
                boletoVO = boletoDAO.consultarPorIdexternoTipo(webhookPJBankJSON.getId_unico(), TipoBoletoEnum.PJ_BANK, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            }

            if (boletoVO == null) {
                throw new Exception("Boleto não encontrado");
            }

            boletoVOAnterior = (BoletoVO) boletoVO.getClone(true);

            //em caso de webhook de cancelamento
            if (webhookPJBankJSON.getTipo().equalsIgnoreCase("cancelamento_boleto")) {
                if (!boletoVO.getSituacao().equals(SituacaoBoletoEnum.CANCELADO)) {
                    //boleto já está cancelado
                    return;
                }
                if (!UteisValidacao.emptyNumber(boletoVO.getMovPagamentoVO().getCodigo()) ||
                        !UteisValidacao.emptyNumber(boletoVO.getReciboPagamentoVO().getCodigo())) {
                    throw new Exception("Boleto está pago por isso não é possível alterar para cancelado. Boleto " + boletoVO.getCodigo());
                }
                boletoDAO.alterarSituacao(boletoVO, SituacaoBoletoEnum.CANCELADO);
                return;
            }


            //obter situacao conforme a os dados recebidos
            SituacaoBoletoEnum situacaoBoletoEnum = obterSituacaoBoleto(webhookPJBankJSON);
            boletoVO.setSituacao(situacaoBoletoEnum);
            boletoVO.setValorPago(Double.valueOf(webhookPJBankJSON.getValor_pago_Float()));
            boletoVO.setValorLiquido(Double.valueOf(webhookPJBankJSON.getValor_liquido_Float()));
            boletoVO.setValorTarifa(Double.valueOf(webhookPJBankJSON.getValor_tarifa_Float()));
            boletoVO.setDataPagamento(webhookPJBankJSON.getData_pagamento_Date());
            boletoVO.setDataCredito(webhookPJBankJSON.getData_credito_Date());
            boletoVO.setValorPossivelDesconto(Double.valueOf(webhookPJBankJSON.getValor_Float()));

            if (UteisValidacao.emptyString(boletoVO.getLinkBoleto())) {
                boletoVO.setLinkBoleto(webhookPJBankJSON.getLink());
            }

            if (!UteisValidacao.emptyString(webhookPJBankJSON.getRegistro_rejeicao_motivo())) {
                boletoVO.adicionarOutrasInformacoes(AtributoBoletoEnum.rejeicao_motivo, webhookPJBankJSON.getRegistro_rejeicao_motivo());
            }

            if (webhookPJBankJSON.getValor_pago_Float() > 0.0) {
                if (!UteisValidacao.emptyNumber(boletoVO.getReciboPagamentoVO().getCodigo()) ||
                        !UteisValidacao.emptyNumber(boletoVO.getMovPagamentoVO().getCodigo())) {
                    boletoDAO.incluirBoletoHistorico(boletoVO, operacao, "BOLETO JÁ ESTÁ PAGO");
                } else {
                    //gerar recibo pagamento
                    gerarPagamentoBoleto(boletoVO, usuarioVO, true, false);
                }
            }

            //atualizar dados do boleto
            boletoDAO.alterar(boletoVO);
        } catch (Exception ex) {
            ex.printStackTrace();
            if (boletoDAO != null) {
                boletoDAO.incluirBoletoHistorico(boletoVO, operacao + " | processarBoletoPJBank | ERRO", ex.getMessage());
            }
            throw ex;
        } finally {
            if (boletoVOAnterior != null && boletoVO != null) {
                if (!boletoVOAnterior.getSituacao().equals(boletoVO.getSituacao())) {
                    JSONObject jsonSituacao = new JSONObject();
                    jsonSituacao.put("situacao_anterior", boletoVOAnterior.getSituacao().getDescricao());
                    jsonSituacao.put("situacao_atual", boletoVO.getSituacao().getDescricao());
                    boletoDAO.incluirBoletoHistorico(boletoVO, "processarBoletoPJBank - ALTERAR SITUAÇÃO", jsonSituacao.toString());
                }
            }

            boletoDAO = null;
        }
    }

    private void tratarErroRequisicao(String response) throws ConsistirException {

        boolean erro = false;
        String msgErro = "";

        try {
            JSONArray jsonArray = new JSONArray(response);
            if (jsonArray.length() > 0) {
                return;
            }

        } catch (Exception e) {
            //ignora
        }

        try {
            JSONObject jsonErro = new JSONObject(response);
            if (jsonErro.optString("status").equals("400") && !UteisValidacao.emptyString(jsonErro.optString("msg"))) {
                erro = true;
                msgErro = jsonErro.optString("msg");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug(this.getClass().getName() + " | tratarErroRequisicao | " + response);
            throw new ConsistirException("Não foi possível cancelar o boleto. Talvez ela já esteja cancelado. Entre em contato com a Pacto.");
        }
        if (erro) {
            throw new ConsistirException(msgErro);
        }
    }
}
