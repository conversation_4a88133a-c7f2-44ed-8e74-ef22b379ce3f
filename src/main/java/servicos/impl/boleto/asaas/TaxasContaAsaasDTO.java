package servicos.impl.boleto.asaas;

import negocio.comuns.utilitarias.Uteis;
import org.json.JSONObject;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 11/05/2023
 */

public class TaxasContaAsaasDTO {

    private TaxasContaAsaasPaymentDTO payment;

    private TaxasContaAsaasNotificacoesDTO notification;

    public TaxasContaAsaasDTO() {
    }

    public TaxasContaAsaasDTO(JSONObject json) {
        this.payment = new TaxasContaAsaasPaymentDTO(json.optJSONObject("payment"));
        this.notification = new TaxasContaAsaasNotificacoesDTO(json.optJSONObject("notification"));
        ;
    }

    public String getTaxasNotificacoesApresentar() throws Exception {
        if (getNotification() != null) {
            return "Email e SMS: R$" + Uteis.formatarValorEmReal(getNotification().getMessagingFeeValue());
        }
        return "";
    }

    public TaxasContaAsaasPaymentDTO getPayment() {
        return payment;
    }

    public void setPayment(TaxasContaAsaasPaymentDTO payment) {
        this.payment = payment;
    }

    public TaxasContaAsaasNotificacoesDTO getNotification() {
        return notification;
    }

    public void setNotification(TaxasContaAsaasNotificacoesDTO notification) {
        this.notification = notification;
    }
}
