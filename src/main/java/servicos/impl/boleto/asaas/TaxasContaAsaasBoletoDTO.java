package servicos.impl.boleto.asaas;

import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 11/05/2023
 */

public class TaxasContaAsaasBoletoDTO {

    private Double defaultValue;
    private Double discountValue;
    private String expirationDate;


    public TaxasContaAsaasBoletoDTO() {
    }

    public TaxasContaAsaasBoletoDTO(JSONObject json) {
        this.defaultValue = json.optDouble("defaultValue");
        if (!Double.isNaN(json.optDouble("discountValue"))) {
            this.discountValue = json.optDouble("discountValue");
        } else {
            this.discountValue = 0.0;
        }
        if (!UteisValidacao.emptyNumber(json.optDouble("expirationDate"))) {
            this.expirationDate = json.optString("expirationDate");
        }
    }

    public double getDefaultValue() {
        if (defaultValue == null) {
            return 0.0;
        }
        return defaultValue;
    }

    public void setDefaultValue(double defaultValue) {
        this.defaultValue = defaultValue;
    }

    public double getDiscountValue() {
        if (discountValue == null) {
            return 0.0;
        }
        return discountValue;
    }

    public void setDiscountValue(double discountValue) {
        this.discountValue = discountValue;
    }

    public String getExpirationDate() {
        if (UteisValidacao.emptyString(expirationDate)) {
            return "";
        }
        return expirationDate;
    }

    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }
}
