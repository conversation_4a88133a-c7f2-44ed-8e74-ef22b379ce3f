package servicos.impl.boleto.asaas;

import negocio.comuns.financeiro.enumerador.ReasonAsaasEnum;
import negocio.comuns.financeiro.enumerador.StatusAsaasEnum;
import negocio.comuns.financeiro.enumerador.TipoCobrancaAsaasEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import servicos.integracao.pjbank.bean.Boleto;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 05/05/2023
 */

//DTO usado tanto na criação de boletos quando na criação de pix

public class CobrancaAsaasRetornoDTO extends Boleto {
    private String object;
    private String id;
    private String dateCreated;
    private String dueDate;
    private String customer;
    private Double value = 0.0;
    private Double netValue = 0.0;
    private TipoCobrancaAsaasEnum billingType;
    private String creditDate;
    private String estimatedCreditDate;
    private boolean canBePaidAfterDueDate;
    private StatusAsaasEnum status;
    private String description;
    private String externalReference;
    private double originalValue;
    private DescontoAsaasDTO discount;
    private JurosAsaasDTO interest;
    private MultaAsaasDTO fine;
    private String pixTransaction;
    private String pixQrCodeId;
    private String originalDueDate;
    private Double interestValue = 0.0;
    private String confirmedDate;
    private String paymentDate;
    private String clientPaymentDate;
    private String invoiceUrl;
    private String transactionReceiptUrl;
    private boolean deleted;
    private Boolean anticipated;
    private Boolean anticipable;
    private ReasonAsaasEnum reason;
    private String canDelete;
    private String cannotBeDeletedReason;
    private String invoiceNumber;
    private String nossoNumero;
    private String paymentLink;
    private String installmentNumber;
    private String lastInvoiceViewedDate;

    public CobrancaAsaasRetornoDTO() {
    }

    public CobrancaAsaasRetornoDTO(JSONObject json) throws Exception {
        setObject(json.optString("object"));
        setId(json.optString("id"));
        setCustomer(json.optString("customer"));
        setPaymentLink(json.optString("paymentLink"));
        setValue(json.optDouble("value"));
        setNetValue(json.optDouble("netValue"));
        setBillingType(TipoCobrancaAsaasEnum.valueOf(json.optString("billingType")));
        setCanBePaidAfterDueDate(json.optBoolean("canBePaidAfterDueDate"));
        setPixTransaction(json.optString("pixTransaction"));
        setStatus(StatusAsaasEnum.valueOf(json.optString("status")));
        setDescription(json.optString("description"));
        setExternalReference(json.optString("externalReference"));
        setOriginalValue(Double.isNaN(json.optDouble("originalValue")) ? 0.0 : json.optDouble("originalValue"));
        setInterestValue(Double.isNaN(json.optDouble("interestValue")) ? 0.0 : json.optDouble("interestValue"));
        setInstallmentNumber(json.optString("installmentNumber"));
        setTransactionReceiptUrl(json.optString("transactionReceiptUrl"));
        setNossoNumero(json.optString("nossoNumero"));
        setInvoiceUrl(json.optString("invoiceUrl"));
        setInvoiceNumber(json.optString("invoiceNumber"));
        setDeleted(json.optBoolean("deleted"));
        setAnticipated(json.optBoolean("anticipated"));
        setAnticipable(json.optBoolean("anticipable"));
        setPixQrCodeId(json.optString("pixQrCodeId"));

        if (!UteisValidacao.emptyString(json.optString("creditDate"))) {
            setCreditDate(Uteis.getData(Uteis.getDate(json.optString("creditDate"), "yyyy-MM-dd")));
        }
        if (!UteisValidacao.emptyString(json.optString("estimatedCreditDate"))) {
            setEstimatedCreditDate(Uteis.getData(Uteis.getDate(json.optString("estimatedCreditDate"), "yyyy-MM-dd")));
        }
        if (!UteisValidacao.emptyString(json.optString("confirmedDate"))) {
            setConfirmedDate(Uteis.getData(Uteis.getDate(json.optString("confirmedDate"), "yyyy-MM-dd")));
        }
        if (!UteisValidacao.emptyString(json.optString("lastInvoiceViewedDate"))) {
            setPaymentDate(Uteis.getData(Uteis.getDate(json.optString("lastInvoiceViewedDate"), "yyyy-MM-dd")));
        }
        if (!UteisValidacao.emptyString(json.optString("paymentDate"))) {
            setPaymentDate(Uteis.getData(Uteis.getDate(json.optString("paymentDate"), "yyyy-MM-dd")));
        }
        if (!UteisValidacao.emptyString(json.optString("clientPaymentDate"))) {
            setClientPaymentDate(Uteis.getData(Uteis.getDate(json.optString("clientPaymentDate"), "yyyy-MM-dd")));
        }
        if (!UteisValidacao.emptyString(json.optString("originalDueDate"))) {
            setOriginalDueDate(Uteis.getData(Uteis.getDate(json.optString("originalDueDate"), "yyyy-MM-dd")));
        }
        if (!UteisValidacao.emptyString(json.optString("dateCreated"))) {
            setDateCreated(Uteis.getData(Uteis.getDate(json.optString("dateCreated"), "yyyy-MM-dd")));
        }
        if (!UteisValidacao.emptyString(json.optString("dueDate"))) {
            setDueDate(Uteis.getData(Uteis.getDate(json.optString("dueDate"), "yyyy-MM-dd")));
        }
        if (!UteisValidacao.emptyString(json.optString("lastInvoiceViewedDate"))) {
            setLastInvoiceViewedDate(Uteis.getData(Uteis.getDate(json.optString("lastInvoiceViewedDate"), "yyyy-MM-dd")));
        }

        //desconto
        if (json.has("discount") && json.optJSONObject("discount") != null && !UteisValidacao.emptyNumber(json.optJSONObject("discount").optDouble("value"))) {
            DescontoAsaasDTO descontoAsaasDTO = new DescontoAsaasDTO(json.optJSONObject("discount"));
            setDiscount(descontoAsaasDTO);
        }

        //multa
        if (json.has("fine") && json.optJSONObject("fine") != null && !UteisValidacao.emptyNumber(json.optJSONObject("fine").optDouble("value"))) {
            MultaAsaasDTO multaAsaasDTO = new MultaAsaasDTO(json.optJSONObject("fine"));
            setFine(multaAsaasDTO);
        }

        //juros
        if (json.has("interest") && json.optJSONObject("interest") != null && !UteisValidacao.emptyNumber(json.optJSONObject("interest").optDouble("value"))) {
            JurosAsaasDTO jurosAsaasDTO = new JurosAsaasDTO(json.optJSONObject("interest"));
            setInterest(jurosAsaasDTO);
        }

    }

    public String getObject() {
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public String getId() {
        if (id == null) {
            return "";
        }
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDateCreated() {
        if (dateCreated == null) {
            return "";
        }
        return dateCreated;
    }

    public void setDateCreated(String dateCreated) {
        this.dateCreated = dateCreated;
    }

    public String getDueDate() {
        if (dueDate == null) {
            return "";
        }
        return dueDate;
    }

    public void setDueDate(String dueDate) {
        this.dueDate = dueDate;
    }

    public String getCustomer() {
        if (customer == null) {
            return "";
        }
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public Double getValue() {
        if (UteisValidacao.emptyNumber(value)) {
            return 0.0;
        }
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public Double getNetValue() {
        if (UteisValidacao.emptyNumber(netValue)) {
            return 0.0;
        }
        return netValue;
    }

    public void setNetValue(Double netValue) {
        this.netValue = netValue;
    }

    public TipoCobrancaAsaasEnum getBillingType() {
        if (billingType == null) {
            return TipoCobrancaAsaasEnum.NENHUMA;
        }
        return billingType;
    }

    public void setBillingType(TipoCobrancaAsaasEnum billingType) {
        this.billingType = billingType;
    }

    public String getCreditDate() {
        if (creditDate == null) {
            return "";
        }
        return creditDate;
    }

    public void setCreditDate(String creditDate) {
        this.creditDate = creditDate;
    }

    public String getEstimatedCreditDate() {
        if (estimatedCreditDate == null) {
            return "";
        }
        return estimatedCreditDate;
    }

    public void setEstimatedCreditDate(String estimatedCreditDate) {
        this.estimatedCreditDate = estimatedCreditDate;
    }

    public boolean isCanBePaidAfterDueDate() {
        return canBePaidAfterDueDate;
    }

    public void setCanBePaidAfterDueDate(boolean canBePaidAfterDueDate) {
        this.canBePaidAfterDueDate = canBePaidAfterDueDate;
    }

    public StatusAsaasEnum getStatus() {
        return status;
    }

    public void setStatus(StatusAsaasEnum status) {
        this.status = status;
    }

    public String getDescription() {
        if (description == null) {
            return "";
        }
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getExternalReference() {
        if (externalReference == null) {
            return "";
        }
        return externalReference;
    }

    public void setExternalReference(String externalReference) {
        this.externalReference = externalReference;
    }

    public double getOriginalValue() {
        if (UteisValidacao.emptyNumber(originalValue)) {
            return 0.0;
        }
        return originalValue;
    }

    public void setOriginalValue(double originalValue) {
        this.originalValue = originalValue;
    }

    public DescontoAsaasDTO getDiscount() {
        if (discount == null) {
            return new DescontoAsaasDTO();
        }
        return discount;
    }

    public void setDiscount(DescontoAsaasDTO discount) {
        this.discount = discount;
    }

    public JurosAsaasDTO getInterest() {
        if (interest == null) {
            return new JurosAsaasDTO();
        }
        return interest;
    }

    public void setInterest(JurosAsaasDTO interest) {
        this.interest = interest;
    }

    public MultaAsaasDTO getFine() {
        if (fine == null) {
            return new MultaAsaasDTO();
        }
        return fine;
    }

    public void setFine(MultaAsaasDTO fine) {
        this.fine = fine;
    }

    public String getPixTransaction() {
        if (pixTransaction == null) {
            return "";
        }
        return pixTransaction;
    }

    public void setPixTransaction(String pixTransaction) {
        this.pixTransaction = pixTransaction;
    }

    public String getPixQrCodeId() {
        if (pixQrCodeId == null) {
            return "";
        }
        return pixQrCodeId;
    }

    public void setPixQrCodeId(String pixQrCodeId) {
        this.pixQrCodeId = pixQrCodeId;
    }

    public String getOriginalDueDate() {
        if (originalDueDate == null) {
            return "";
        }
        return originalDueDate;
    }

    public void setOriginalDueDate(String originalDueDate) {
        this.originalDueDate = originalDueDate;
    }

    public Double getInterestValue() {
        return interestValue;
    }

    public void setInterestValue(Double interestValue) {
        this.interestValue = interestValue;
    }

    public String getConfirmedDate() {
        if (confirmedDate == null) {
            return "";
        }
        return confirmedDate;
    }

    public void setConfirmedDate(String confirmedDate) {
        this.confirmedDate = confirmedDate;
    }

    public String getPaymentDate() {
        if (paymentDate == null) {
            return "";
        }
        return paymentDate;
    }

    public void setPaymentDate(String paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getClientPaymentDate() {
        if (clientPaymentDate == null) {
            return "";
        }
        return clientPaymentDate;
    }

    public void setClientPaymentDate(String clientPaymentDate) {
        this.clientPaymentDate = clientPaymentDate;
    }

    public String getInvoiceUrl() {
        if (invoiceUrl == null) {
            return "";
        }
        return invoiceUrl;
    }

    public void setInvoiceUrl(String invoiceUrl) {
        this.invoiceUrl = invoiceUrl;
    }

    public String getTransactionReceiptUrl() {
        if (transactionReceiptUrl == null) {
            return "";
        }
        return transactionReceiptUrl;
    }

    public void setTransactionReceiptUrl(String transactionReceiptUrl) {
        this.transactionReceiptUrl = transactionReceiptUrl;
    }

    public Boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Boolean getAnticipated() {
        return anticipated;
    }

    public void setAnticipated(Boolean anticipated) {
        this.anticipated = anticipated;
    }

    public Boolean getAnticipable() {
        return anticipable;
    }

    public void setAnticipable(Boolean anticipable) {
        this.anticipable = anticipable;
    }

    public ReasonAsaasEnum getReason() {
        if (reason == null) {
            return ReasonAsaasEnum.NENHUMA;
        }
        return reason;
    }

    public void setReason(ReasonAsaasEnum reason) {
        this.reason = reason;
    }

    public String getCanDelete() {
        if (canDelete == null) {
            return "";
        }
        return canDelete;
    }

    public void setCanDelete(String canDelete) {
        this.canDelete = canDelete;
    }

    public String getCannotBeDeletedReason() {
        if (cannotBeDeletedReason == null) {
            return "";
        }
        return cannotBeDeletedReason;
    }

    public void setCannotBeDeletedReason(String cannotBeDeletedReason) {
        this.cannotBeDeletedReason = cannotBeDeletedReason;
    }

    public String getInvoiceNumber() {
        if (invoiceNumber == null) {
            return "";
        }
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public String getNossoNumero() {
        if (nossoNumero == null) {
            return "";
        }
        return nossoNumero;
    }

    public void setNossoNumero(String nossoNumero) {
        this.nossoNumero = nossoNumero;
    }

    public String getPaymentLink() {
        if (paymentLink == null) {
            return "";
        }
        return paymentLink;
    }

    public void setPaymentLink(String paymentLink) {
        this.paymentLink = paymentLink;
    }

    public String getInstallmentNumber() {
        if (installmentNumber == null) {
            return "";
        }
        return installmentNumber;
    }

    public void setInstallmentNumber(String installmentNumber) {
        this.installmentNumber = installmentNumber;
    }

    public String getLastInvoiceViewedDate() {
        if (lastInvoiceViewedDate == null) {
            return "";
        }
        return lastInvoiceViewedDate;
    }

    public void setLastInvoiceViewedDate(String lastInvoiceViewedDate) {
        this.lastInvoiceViewedDate = lastInvoiceViewedDate;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    @Override
    public String toString() {
        return "{" +
                "object='" + object + '\'' +
                ", id='" + id + '\'' +
                ", dateCreated='" + dateCreated + '\'' +
                ", dueDate='" + dueDate + '\'' +
                ", customer='" + customer + '\'' +
                ", value=" + value +
                ", netValue=" + netValue +
                ", billingType=" + billingType +
                ", creditDate='" + creditDate + '\'' +
                ", estimatedCreditDate='" + estimatedCreditDate + '\'' +
                ", canBePaidAfterDueDate=" + canBePaidAfterDueDate +
                ", status=" + status +
                ", description='" + description + '\'' +
                ", externalReference='" + externalReference + '\'' +
                ", originalValue=" + originalValue +
                ", discount=" + discount +
                ", interest=" + interest +
                ", fine=" + fine +
                ", pixTransaction='" + pixTransaction + '\'' +
                ", pixQrCodeId='" + pixQrCodeId + '\'' +
                ", originalDueDate='" + originalDueDate + '\'' +
                ", interestValue=" + interestValue +
                ", confirmedDate='" + confirmedDate + '\'' +
                ", paymentDate='" + paymentDate + '\'' +
                ", clientPaymentDate='" + clientPaymentDate + '\'' +
                ", invoiceUrl='" + invoiceUrl + '\'' +
                ", transactionReceiptUrl='" + transactionReceiptUrl + '\'' +
                ", deleted=" + deleted +
                ", anticipated=" + anticipated +
                ", anticipable=" + anticipable +
                ", reason=" + reason +
                ", canDelete='" + canDelete + '\'' +
                ", cannotBeDeletedReason='" + cannotBeDeletedReason + '\'' +
                ", invoiceNumber='" + invoiceNumber + '\'' +
                ", nossoNumero='" + nossoNumero + '\'' +
                ", paymentLink='" + paymentLink + '\'' +
                ", installmentNumber='" + installmentNumber + '\'' +
                ", lastInvoiceViewedDate='" + lastInvoiceViewedDate + '\'' +
                '}';
    }
}
