package servicos.impl.boleto.asaas;

import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 04/05/2023
 */

public class ClienteAsaasDTO extends SuperVO {
    private String nome;
    private String cpfCnpj;
    private EnderecoVO enderecoVO;
    private String telefone;
    private String email;
    private String id;

    public ClienteAsaasDTO() {
    }

    public ClienteAsaasDTO(String nome, String cpfCnpj, EnderecoVO enderecoVO) {
        this.nome = nome;
        this.cpfCnpj = cpfCnpj;
        this.enderecoVO = enderecoVO;
    }

    public ClienteAsaasDTO(String nome, String cpfCnpj, EnderecoVO enderecoVO, String telefone, String email) {
        this.nome = nome;
        this.cpfCnpj = cpfCnpj;
        this.enderecoVO = enderecoVO;
        this.telefone = telefone;
        this.email = email;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCpfCnpj() {
        return cpfCnpj;
    }

    public void setCpfCnpj(String cpfCnpj) {
        this.cpfCnpj = cpfCnpj;
    }

    public EnderecoVO getEnderecoVO() {
        return enderecoVO;
    }

    public void setEnderecoVO(EnderecoVO enderecoVO) {
        this.enderecoVO = enderecoVO;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getId() {
        if (UteisValidacao.emptyString(id)) {
            return "";
        }
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
