package servicos.impl.boleto.asaas;

import negocio.comuns.financeiro.enumerador.StatusSituacaoCadastralAsaasEnum;
import org.json.JSONObject;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 11/05/2023
 */

public class SituacaoCadastralContaAsaasDTO {

    private String id;
    private String commercialInfo;
    private String bankAccountInfo;
    private String documentation;
    private String general;

    public SituacaoCadastralContaAsaasDTO() {
    }

    public SituacaoCadastralContaAsaasDTO(JSONObject json) {
        this.id = json.optString("id");
        this.commercialInfo = StatusSituacaoCadastralAsaasEnum.obterPorDescricao(json.optString("commercialInfo")).getDescricao();
        this.bankAccountInfo = StatusSituacaoCadastralAsaasEnum.obterPorDescricao(json.optString("bankAccountInfo")).getDescricao();
        this.documentation = StatusSituacaoCadastralAsaasEnum.obterPorDescricao(json.optString("documentation")).getDescricao();
        this.general = StatusSituacaoCadastralAsaasEnum.obterPorDescricao(json.optString("general")).getDescricao();
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCommercialInfo() {
        return commercialInfo;
    }

    public void setCommercialInfo(String commercialInfo) {
        this.commercialInfo = commercialInfo;
    }

    public String getBankAccountInfo() {
        return bankAccountInfo;
    }

    public void setBankAccountInfo(String bankAccountInfo) {
        this.bankAccountInfo = bankAccountInfo;
    }

    public String getDocumentation() {
        return documentation;
    }

    public void setDocumentation(String documentation) {
        this.documentation = documentation;
    }

    public String getGeneral() {
        return general;
    }

    public void setGeneral(String general) {
        this.general = general;
    }
}
