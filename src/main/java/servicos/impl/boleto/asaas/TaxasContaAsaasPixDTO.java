package servicos.impl.boleto.asaas;

import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 11/05/2023
 */

public class TaxasContaAsaasPixDTO {

    private Double fixedFeeValue;
    private Double fixedFeeValueWithDiscount;
    private String discountExpiration;
    private Integer monthlyCreditsWithoutFee;


    public TaxasContaAsaasPixDTO() {
    }

    public TaxasContaAsaasPixDTO(JSONObject json) {
        this.fixedFeeValue = json.optDouble("fixedFeeValue");
        if (!Double.isNaN(json.optDouble("fixedFeeValueWithDiscount"))) {
            this.fixedFeeValueWithDiscount = json.optDouble("fixedFeeValueWithDiscount");
        } else {
            this.fixedFeeValueWithDiscount = 0.0;
        }
        this.discountExpiration = json.optString("discountExpiration");
        this.monthlyCreditsWithoutFee = json.optInt("monthlyCreditsWithoutFee");
    }

    public Double getFixedFeeValue() {
        if (fixedFeeValue == null) {
            return 0.0;
        }
        return fixedFeeValue;
    }

    public void setFixedFeeValue(Double fixedFeeValue) {
        this.fixedFeeValue = fixedFeeValue;
    }

    public Double getFixedFeeValueWithDiscount() {
        if (fixedFeeValueWithDiscount == null) {
            return 0.0;
        }
        return fixedFeeValueWithDiscount;
    }

    public void setFixedFeeValueWithDiscount(Double fixedFeeValueWithDiscount) {
        this.fixedFeeValueWithDiscount = fixedFeeValueWithDiscount;
    }

    public String getDiscountExpiration() {
        if (UteisValidacao.emptyString(discountExpiration)) {
            return "";
        }
        return discountExpiration;
    }

    public void setDiscountExpiration(String discountExpiration) {
        this.discountExpiration = discountExpiration;
    }

    public int getMonthlyCreditsWithoutFee() {
        if (monthlyCreditsWithoutFee == null) {
            return 0;
        }
        return monthlyCreditsWithoutFee;
    }

    public void setMonthlyCreditsWithoutFee(int monthlyCreditsWithoutFee) {
        this.monthlyCreditsWithoutFee = monthlyCreditsWithoutFee;
    }
}
