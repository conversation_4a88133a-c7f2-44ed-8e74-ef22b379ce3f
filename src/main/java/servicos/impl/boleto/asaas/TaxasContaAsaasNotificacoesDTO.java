package servicos.impl.boleto.asaas;

import org.json.JSONObject;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 11/05/2023
 */

public class TaxasContaAsaasNotificacoesDTO {

    private Double phoneCallFeeValue;
    private Double whatsAppFeeValue;
    private Double messagingFeeValue;


    public TaxasContaAsaasNotificacoesDTO() {
    }

    public TaxasContaAsaasNotificacoesDTO(JSONObject json) {
        this.phoneCallFeeValue = json.optDouble("phoneCallFeeValue");
        this.whatsAppFeeValue = json.optDouble("whatsAppFeeValue");
        this.messagingFeeValue = json.optDouble("messagingFeeValue");
    }

    public Double getPhoneCallFeeValue() {
        if (phoneCallFeeValue == null) {
            return 0.0;
        }
        return phoneCallFeeValue;
    }

    public void setPhoneCallFeeValue(Double phoneCallFeeValue) {
        this.phoneCallFeeValue = phoneCallFeeValue;
    }

    public Double getWhatsAppFeeValue() {
        if (whatsAppFeeValue == null) {
            return 0.0;
        }
        return whatsAppFeeValue;
    }

    public void setWhatsAppFeeValue(Double whatsAppFeeValue) {
        this.whatsAppFeeValue = whatsAppFeeValue;
    }

    public Double getMessagingFeeValue() {
        if (messagingFeeValue == null) {
            return 0.0;
        }
        return messagingFeeValue;
    }

    public void setMessagingFeeValue(Double messagingFeeValue) {
        this.messagingFeeValue = messagingFeeValue;
    }
}
