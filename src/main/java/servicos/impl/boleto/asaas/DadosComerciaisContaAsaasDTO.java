package servicos.impl.boleto.asaas;

import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.enumerador.CompanyTypeAsaasEnum;
import negocio.comuns.financeiro.enumerador.StatusDadosComerciaisAsaasEnum;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 11/05/2023
 */

public class DadosComerciaisContaAsaasDTO {

    private String status;
    private String personType;
    private String cpfCnpj; //CPF ou CNPJ do proprietário da conta
    private String name; //Nome do proprietário da conta
    private String companyType;
    private String companyName;
    private String email;
    private String phone;
    private String mobilePhone;
    private String postalCode;
    private String address;
    private String complement;
    private String province;
    private String city;
    private String denialReason;


    public DadosComerciaisContaAsaasDTO() {
    }

    public DadosComerciaisContaAsaasDTO(JSONObject json) {
        this.status = StatusDadosComerciaisAsaasEnum.obterPorDescricao(json.optString("status")).getDescricao();
        if (!UteisValidacao.emptyString(json.optString("personType"))) {
            this.personType = TipoPessoa.obterPorLabel(json.optString("personType")).getLabel();
        }
        this.cpfCnpj = Uteis.formatarCpfCnpj(json.optString("cpfCnpj"), false);
        this.name = json.optString("name");
        this.companyType = CompanyTypeAsaasEnum.obterPorDescricao(json.optString("companyType")).getDescricao();
        this.email = json.optString("email");
        this.phone = json.optString("phone");
        this.mobilePhone = json.optString("mobilePhone");
        this.postalCode = json.optString("postalCode");
        this.address = json.optString("address") + (UteisValidacao.emptyString(json.optString("addressNumber")) ? "" : " - Nº " + json.optString("addressNumber"));
        this.complement = json.optString("complement");
        this.province = json.optString("province");
        if (json.optJSONObject("city") != null && !UteisValidacao.emptyString(json.optJSONObject("city").optString("name"))) {
            this.city = json.optJSONObject("city").optString("name");
        }
        this.denialReason = json.optString("denialReason");
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPersonType() {
        return personType;
    }

    public void setPersonType(String personType) {
        this.personType = personType;
    }

    public String getCpfCnpj() {
        return cpfCnpj;
    }

    public void setCpfCnpj(String cpfCnpj) {
        this.cpfCnpj = cpfCnpj;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCompanyType() {
        return companyType;
    }

    public void setCompanyType(String companyType) {
        this.companyType = companyType;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getComplement() {
        return complement;
    }

    public void setComplement(String complement) {
        this.complement = complement;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDenialReason() {
        return denialReason;
    }

    public void setDenialReason(String denialReason) {
        this.denialReason = denialReason;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
}
