package servicos.impl.boleto.asaas;

import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON>
 * Date: 11/05/2023
 */

public class TaxasContaAsaasPaymentDTO {

    private TaxasContaAsaasBoletoDTO bankSlip;

    private TaxasContaAsaasPixDTO pix;

    public TaxasContaAsaasPaymentDTO() {
    }

    public TaxasContaAsaasPaymentDTO(JSONObject json) {
        this.bankSlip = new TaxasContaAsaasBoletoDTO(json.optJSONObject("bankSlip"));
        this.pix = new TaxasContaAsaasPixDTO(json.optJSONObject("pix"));
    }

    public TaxasContaAsaasBoletoDTO getBankSlip() {
        return bankSlip;
    }

    public void setBankSlip(TaxasContaAsaasBoletoDTO bankSlip) {
        this.bankSlip = bankSlip;
    }

    public TaxasContaAsaasPixDTO getPix() {
        return pix;
    }

    public void setPix(TaxasContaAsaasPixDTO pix) {
        this.pix = pix;
    }

    public String getTaxasBoletoApresentar() throws Exception {
        if (getBankSlip() != null) {
            String texto = "R$" + Uteis.formatarValorEmReal(getBankSlip().getDefaultValue());
            boolean possuiDesconto = !UteisValidacao.emptyNumber(getBankSlip().getDiscountValue());
            if (possuiDesconto) {
                texto += " | Valor promocional de R$" + Uteis.formatarValorEmReal(getBankSlip().getDiscountValue());
                boolean temDataExpiracaoDesconto = !UteisValidacao.emptyString(getBankSlip().getExpirationDate());
                if (temDataExpiracaoDesconto) {
                    texto += " até o dia " + Uteis.getData(Uteis.getDate(getBankSlip().getExpirationDate(), "yyyy-MM-dd"));
                }
            }
            return texto;
        }
        return "";
    }

    public String getTaxasPixApresentar() throws Exception {
        if (getPix() != null) {
            String texto = "R$" + Uteis.formatarValorEmReal(getPix().getFixedFeeValue());
            boolean possuiDesconto = !UteisValidacao.emptyNumber(getPix().getFixedFeeValueWithDiscount());
            if (possuiDesconto) {
                texto += " | Valor promocional de R$" + Uteis.formatarValorEmReal(getPix().getFixedFeeValueWithDiscount());
                boolean temDataExpiracaoDesconto = !UteisValidacao.emptyString(getPix().getDiscountExpiration());
                boolean temQtdGratisMensal = !UteisValidacao.emptyNumber(getPix().getMonthlyCreditsWithoutFee());
                if (temDataExpiracaoDesconto) {
                    texto += " até o dia " + Uteis.getData(Uteis.getDate(getPix().getDiscountExpiration(), "yyyy-MM-dd"));
                }
                if (temQtdGratisMensal) {
                    texto += " | Possui " + getPix().getMonthlyCreditsWithoutFee() + " grátis por mês";
                }
            }
            return texto;
        }
        return "";
    }
}
