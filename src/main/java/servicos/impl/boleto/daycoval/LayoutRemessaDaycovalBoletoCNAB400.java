package servicos.impl.boleto.daycoval;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import negocio.comuns.basico.EnderecoVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.financeiro.ObjetoGenerico;
import negocio.comuns.financeiro.RemessaItemVO;
import negocio.comuns.financeiro.RemessaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.dcc.base.DCCAttEnum;
import servicos.impl.dcc.base.LayoutRemessaBase;
import servicos.impl.dcc.base.RegistroRemessa;
import servicos.impl.dcc.base.TipoRegistroEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LayoutRemessaDaycovalBoletoCNAB400 extends LayoutRemessaBase {

    public static void preencherArquivoRemessa(RemessaVO remessa) {

        RegistroRemessa headerArquivo = preencherHeaderArquivo(remessa);
        remessa.setHeaderRemessaArquivo(headerArquivo);

        Date dataDeposito = remessa.getDataRegistro();

        List<RemessaItemVO> lista = remessa.getListaItens();
        List<RegistroRemessa> listaDetalhe = new ArrayList<RegistroRemessa>();

        int qtdAceito = 0;
        int seq = 1;
        double valor = 0.0;
        for (RemessaItemVO item : lista) {
            RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);

            detail.put(DCCAttEnum.TipoRegistro, "1");//1
            detail.put(DCCAttEnum.CpfOuCnpj, "02");//2
            detail.put(DCCAttEnum.CnpjEmpresa, StringUtilities.formatarCampoEmBranco(Uteis.removerMascara(remessa.getConvenioCobranca().getEmpresa().getCNPJ()), 14));//14
            detail.put(DCCAttEnum.NumeroContrato, StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getNumeroContrato(), 12));//12
            detail.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(4));//4
            detail.put(DCCAttEnum.InstrucaoAlegacao, StringUtilities.formatarCampoEmBranco(4));//4

            StringBuilder usoEmpresa = new StringBuilder();
            usoEmpresa.append(StringUtilities.formatarCampoEmBranco(StringUtilities.formatarCampoData(Calendario.proximoDiaUtil(dataDeposito, 5), "dd/MM/yyyy"), 25));
            detail.put(DCCAttEnum.UsoEmpresa, usoEmpresa);//25

            String nossoNumero = StringUtilities.formatarCampo(new BigDecimal(item.getIdentificador()), 8);
            detail.put(DCCAttEnum.NossoNumero, nossoNumero);//8 (063 - 070)

            detail.put(DCCAttEnum.UsoBanco, StringUtilities.formatarCampoEmBranco(37)); // 37 (71 - 107)
            detail.put(DCCAttEnum.CodCarteira, StringUtilities.formatarCampo(new BigDecimal(remessa.getConvenioCobranca().getCarteira()), 1));
            detail.put(DCCAttEnum.CodOcorrencia, "01");

            detail.put(DCCAttEnum.NumDocumento, StringUtilities.formatarCampoForcandoZerosAEsquerda(item.getCodigo().toString(), 10));
            detail.put(DCCAttEnum.DataVencimento, StringUtilities.formatarCampoData(item.getDataVencimentoBoleto(), "ddMMyy"));
            detail.put(DCCAttEnum.ValorVenda, StringUtilities.formatarCampoMonetario(item.getValorBoleto(), 13));//13

            detail.put(DCCAttEnum.CodigoBanco, "707");
            detail.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.formatarCampoZerado(5)); //Agencia onde o titulo sera cobrado
            detail.put(DCCAttEnum.EspecieTitulo, "99");
            detail.put(DCCAttEnum.Identificacao, "N"); //Aceite
            detail.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(remessa.getDataRegistro(), "ddMMyy"));
            detail.put(DCCAttEnum.Instrucao1, "00");
            detail.put(DCCAttEnum.Instrucao2, "00");
            detail.put(DCCAttEnum.MoraDia, StringUtilities.formatarCampoZerado(13));
            detail.put(DCCAttEnum.DataLimiteDesconto, StringUtilities.formatarCampoZerado(6));
            detail.put(DCCAttEnum.ValorDesconto, StringUtilities.formatarCampoZerado(13));
            detail.put(DCCAttEnum.ValorBaseIOF, StringUtilities.formatarCampoZerado(13));
            detail.put(DCCAttEnum.Abatimento, StringUtilities.formatarCampoZerado(13));
            detail.put(DCCAttEnum.CpfOuCnpj, item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? "01" : "02");
            String cpfCnpj = item.getPessoa().getCategoriaPessoa().equals(TipoPessoa.FISICA) ? getCPF(item) : getCNPJ(item);
            detail.put(DCCAttEnum.CpfCliente, StringUtilities.formatarCpfCnjp(cpfCnpj, 14));

            detail.put(DCCAttEnum.NomePessoa, retirarCaracteresInvalidos(StringUtilities.formatarCampoEmBranco(item.getPessoa().getNome(), 30)));

            detail.put(DCCAttEnum.EmBranco3, StringUtilities.formatarCampoEmBranco(10));

            EnderecoVO enderecoVO = new EnderecoVO();
            for (EnderecoVO endereco : item.getPessoa().getEnderecoVOs()) {
                if (endereco.getTipoEndereco().equals("RE") || endereco.getEnderecoCorrespondencia()) {
                    enderecoVO = endereco;
                    break;
                }
            }

            detail.put(DCCAttEnum.EnderecoPagador, retirarCaracteresInvalidos(StringUtilities.formatarCampoEmBranco(enderecoVO.getEndereco() + ", " + enderecoVO.getNumero() + ", " + enderecoVO.getComplemento(), 40)));
            detail.put(DCCAttEnum.BairroPagador, StringUtilities.formatarCampoEmBranco(enderecoVO.getBairro(), 12));
            detail.put(DCCAttEnum.CEPPagador, StringUtilities.formatarCampoEmBranco(Uteis.removerMascara(enderecoVO.getCep().trim()), 8));
            detail.put(DCCAttEnum.CidadePagador, retirarCaracteresInvalidos(StringUtilities.formatarCampoEmBranco(item.getPessoa().getCidade_Apresentar(), 15)));
            detail.put(DCCAttEnum.EstadoPagador, StringUtilities.formatarCampoEmBranco(item.getPessoa().getEstadoVO().getSigla(), 2));
            detail.put(DCCAttEnum.SacadorAvalista, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getRazaoSocial(), 30));
            detail.put(DCCAttEnum.EmBranco4, StringUtilities.formatarCampoEmBranco(4));
            detail.put(DCCAttEnum.DataMora, StringUtilities.formatarCampoEmBranco(6));
            detail.put(DCCAttEnum.Prazo, StringUtilities.formatarCampoZerado(2));
            detail.put(DCCAttEnum.EmBranco5, StringUtilities.formatarCampoZerado(1));
            seq++;
            detail.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampo(new BigDecimal(seq), 6));

            if (item.getMovPagamento().getCodigo() != 0) {
                qtdAceito += 1;
            }
            valor += item.getValorItemRemessa();

            //
            listaDetalhe.add(detail);
        }
        remessa.setQtdAceito(qtdAceito);

        seq++;


        RegistroRemessa trailerArquivo = preencherTrailerArquivo(remessa, seq, valor);
        remessa.setHeaderRemessa(headerArquivo);
        remessa.setDetailsRemessa(listaDetalhe);
        remessa.setTrailerRemessa(trailerArquivo);
        remessa.setTrailerRemessaArquivo(trailerArquivo);

        remessa.setHeaderArquivo(new StringBuilder(headerArquivo.toString()));
        remessa.setHead(new StringBuilder(headerArquivo.toString()));
        remessa.setDetail(new StringBuilder(listaDetalhe.toString()));
        remessa.setTrailerArquivo(new StringBuilder(trailerArquivo.toString()));
        remessa.setTrailer(new StringBuilder(trailerArquivo.toString()));

    }

    public static String getCPF(RemessaItemVO remessaItem) {
        String cpf = remessaItem.getPessoa().getCfp();
        if (UteisValidacao.emptyString(cpf)) {
            cpf = remessaItem.getClienteVO().getPessoaResponsavel().getCfp();
        }
        cpf = cpf.replaceAll("[.,-]", "");
        cpf = !cpf.isEmpty() && cpf.length() > 11 ? cpf.substring(0, 11) : cpf;
        return cpf;
    }

    public static RegistroRemessa obterHeaderRetorno(final StringBuilder retorno) throws IOException {
        RegistroRemessa h = null;
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            h = new RegistroRemessa(TipoRegistroEnum.HEADER);

            while ((linha = br.readLine()) != null) {
                Integer tipo = Integer.valueOf(StringUtilities.readString(0, 1, linha));
                if (tipo.equals(0)) {
                    lerAtributos(linha, h);
                    break;
                }
            }
        }
        return h;
    }

    public static String obterCodigosMovParcelas(final StringBuilder retorno) throws IOException {
        List<Integer> codigos = new ArrayList<Integer>();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;
            while ((linha = br.readLine()) != null) {
                Integer tipo = Integer.valueOf(StringUtilities.readString(0, 1, linha));
                if (tipo.equals(1)) {
                    codigos.add(Integer.valueOf(StringUtilities.readString(116, 126, linha)));
                }
            }
        }
        Collections.sort(codigos);
        String cods = "";
        for (Integer cod : codigos) {
            cods = cods + "," + cod;
        }
        return cods.replaceFirst(",", "");
    }

    public static void lerRetorno(RemessaVO remessa) throws IOException, ConsistirException {
        StringBuilder retorno = remessa.getRetorno();
        if (retorno.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(retorno.toString()));
            String linha;

            RegistroRemessa h = new RegistroRemessa(TipoRegistroEnum.HEADER);
            RegistroRemessa t = new RegistroRemessa(TipoRegistroEnum.TRAILER);
            List<RegistroRemessa> listaDetalheRetorno = new ArrayList<RegistroRemessa>();
            while ((linha = br.readLine()) != null) {
                Integer tipo = Integer.valueOf(StringUtilities.readString(0, 1, linha));
                switch (tipo) {
                    //header arquivo
                    case 0:
                        lerAtributos(linha, h);
                        break;
                    //detalhe
                    case 1:
                        RegistroRemessa detail = new RegistroRemessa(TipoRegistroEnum.DETALHE);
                        lerAtributos(linha, detail);
                        listaDetalheRetorno.add(detail);
                        break;
                    //trailer
                    case 9:
                        lerAtributos(linha, t);
                        break;

                }
            }
            remessa.setHeaderRetorno(h);
            remessa.setDetailsRetorno(listaDetalheRetorno);
            remessa.setTrailerRetorno(t);

//            validarArquivoRemessaRetorno(remessa);
            //Analisar Trailer
            RegistroRemessa tRem = remessa.getTrailerRemessa();
            RegistroRemessa tRet = remessa.getTrailerRetorno();
            List<ObjetoGenerico> attrTrailer = tRem.getAtributos();
            for (int i = 0; i < attrTrailer.size(); i++) {
                ObjetoGenerico obj = attrTrailer.get(i);
                preencherAtributosTransientes(remessa, tRet.getAtributos().get(i));
            }
            //alguns dados precisam ser atualizados na Remessa depois do processamento do Retorno
            if (t.toString().length() > 2) {
                remessa.setTrailer(new StringBuilder(t.toString()));
            }
        }
    }

    public static void lerAtributos(final String linha, RegistroRemessa r) {
        if (r.getTipo() == TipoRegistroEnum.HEADER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
            r.put(DCCAttEnum.CodigoRetorno, StringUtilities.readString(1, 2, linha));
            r.put(DCCAttEnum.IdentificacaoRetorno, StringUtilities.readString(2, 9, linha));
            r.put(DCCAttEnum.CodigoServico, StringUtilities.readString(9, 11, linha));
            r.put(DCCAttEnum.IdentificacaoServico, StringUtilities.readString(11, 26, linha));

            r.put(DCCAttEnum.NumeroContrato, StringUtilities.readString(26, 38, linha));

            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(38, 46, linha));
            r.put(DCCAttEnum.NomeEmpresa, StringUtilities.readString(46, 76, linha));
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(76, 79, linha));
            r.put(DCCAttEnum.NomeBanco, StringUtilities.readString(79, 94, linha));
            r.put(DCCAttEnum.DataGeracao, StringUtilities.readString(94, 100, linha));
            r.put(DCCAttEnum.DensidadeGravacao, StringUtilities.readString(100, 105, linha));
            r.put(DCCAttEnum.UnidadeDensidade, StringUtilities.readString(105, 108, linha));
            //Nº Seq. Arquivo Ret. (Número Seqüencial do arquivo de retorno)
            r.put(DCCAttEnum.NumAvisoBancario, StringUtilities.readString(108, 113, linha));
            r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(113, 119, linha));
            r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(119, 394, linha));
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));
        } else if (r.getTipo() == TipoRegistroEnum.DETALHE) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
            r.put(DCCAttEnum.CpfOuCnpj, StringUtilities.readString(1, 3, linha));
            r.put(DCCAttEnum.CnpjEmpresa, StringUtilities.readString(3, 17, linha));
            r.put(DCCAttEnum.AgenciaDepositaria, StringUtilities.readString(17, 21, linha));
            r.put(DCCAttEnum.Zeros, StringUtilities.readString(21, 23, linha));
            r.put(DCCAttEnum.ContaCorrente, StringUtilities.readString(23, 28, linha));
            r.put(DCCAttEnum.ContaCorrenteDigito, StringUtilities.readString(28, 29, linha));
            r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(29, 37, linha));
            r.put(DCCAttEnum.UsoEmpresa, StringUtilities.readString(37, 62, linha));
            r.put(DCCAttEnum.NossoNumero, StringUtilities.readString(62, 70, linha));
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(70, 82, linha));
            r.put(DCCAttEnum.Carteira, StringUtilities.readString(82, 85, linha));
            r.put(DCCAttEnum.NossoNumero2, StringUtilities.readString(85, 93, linha));
            r.put(DCCAttEnum.NossoNumero2DV, StringUtilities.readString(93, 94, linha));
            r.put(DCCAttEnum.EmBranco3, StringUtilities.readString(94, 107, linha));
            r.put(DCCAttEnum.CodCarteira, StringUtilities.readString(107, 108, linha));
            r.put(DCCAttEnum.StatusVenda, StringUtilities.readString(108, 110, linha));
            r.put(DCCAttEnum.DataOcorrencia, StringUtilities.readString(110, 116, linha));
            r.put(DCCAttEnum.NumDocumento, StringUtilities.readString(116, 126, linha));
            r.put(DCCAttEnum.NossoNumero3, StringUtilities.readString(126, 134, linha));
            r.put(DCCAttEnum.EmBranco4, StringUtilities.readString(134, 146, linha));
            r.put(DCCAttEnum.DataVencimento, StringUtilities.readString(146, 152, linha));
            r.put(DCCAttEnum.ValorVenda, StringUtilities.readString(152, 165, linha));
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(165, 168, linha));
            r.put(DCCAttEnum.AgenciaDebito, StringUtilities.readString(168, 172, linha));
            r.put(DCCAttEnum.AgenciaDebitoDigito, StringUtilities.readString(172, 173, linha));
            r.put(DCCAttEnum.EspecieTitulo, StringUtilities.readString(173, 175, linha));
            r.put(DCCAttEnum.DespesaCobranca, StringUtilities.readString(175, 188, linha));
            r.put(DCCAttEnum.EmBranco4, StringUtilities.readString(188, 214, linha));
            r.put(DCCAttEnum.ValorBaseIOF, StringUtilities.readString(214, 227, linha));
            r.put(DCCAttEnum.ValorAbatimento, StringUtilities.readString(227, 240, linha));
            r.put(DCCAttEnum.ValorDesconto, StringUtilities.readString(240, 253, linha));
            r.put(DCCAttEnum.ValorPago, StringUtilities.readString(253, 266, linha));//??
            r.put(DCCAttEnum.Juros, StringUtilities.readString(266, 279, linha));
            r.put(DCCAttEnum.OutrosCreditos, StringUtilities.readString(279, 292, linha));
            r.put(DCCAttEnum.BoletoDDA, StringUtilities.readString(292, 293, linha));
            r.put(DCCAttEnum.EmBranco5, StringUtilities.readString(293, 295, linha));
            r.put(DCCAttEnum.DataPrevistaCredito, StringUtilities.readString(295, 301, linha));
            r.put(DCCAttEnum.InstrucaoCancelada, StringUtilities.readString(301, 305, linha));
            r.put(DCCAttEnum.EmBranco6, StringUtilities.readString(305, 311, linha));
            r.put(DCCAttEnum.Zeros2, StringUtilities.readString(311, 324, linha));
            r.put(DCCAttEnum.NomePessoa, StringUtilities.readString(324, 354, linha));
            r.put(DCCAttEnum.EmBranco7, StringUtilities.readString(354, 377, linha));
            r.put(DCCAttEnum.CodigoErro, StringUtilities.readString(377, 385, linha));
            r.put(DCCAttEnum.EmBranco8, StringUtilities.readString(385, 392, linha));
            r.put(DCCAttEnum.CodigoLiquidacao, StringUtilities.readString(392, 394, linha));
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));
        } else if (r.getTipo() == TipoRegistroEnum.TRAILER) {
            r.put(DCCAttEnum.TipoRegistro, StringUtilities.readString(0, 1, linha));
            r.put(DCCAttEnum.CodigoRetorno, StringUtilities.readString(1, 2, linha));
            r.put(DCCAttEnum.CodigoServico, StringUtilities.readString(2, 4, linha));
            r.put(DCCAttEnum.CodigoBanco, StringUtilities.readString(4, 7, linha));
            r.put(DCCAttEnum.EmBranco, StringUtilities.readString(7, 13, linha));
            r.put(DCCAttEnum.QtdTitulosCobrancaSimples, StringUtilities.readString(17, 25, linha));
            r.put(DCCAttEnum.ValorTotalCobrancaSimples, StringUtilities.readString(25, 39, linha));
            r.put(DCCAttEnum.AvisoBancarioCobrancaSimples, StringUtilities.readString(39, 47, linha));
            r.put(DCCAttEnum.EmBranco2, StringUtilities.readString(47, 57, linha));
            r.put(DCCAttEnum.QtdTitulosCobrancaVinculada, StringUtilities.readString(57, 65, linha));
            r.put(DCCAttEnum.ValorTotalCobrancaVinculada, StringUtilities.readString(65, 79, linha));
            r.put(DCCAttEnum.AvisoBancarioCobrancaVinculada, StringUtilities.readString(79, 87, linha));
            r.put(DCCAttEnum.EmBranco3, StringUtilities.readString(87, 177, linha));
            r.put(DCCAttEnum.QtdTitulosCobrancaDireta, StringUtilities.readString(177, 185, linha));
            r.put(DCCAttEnum.ValorTotalCobrancaDireta, StringUtilities.readString(185, 199, linha));
            r.put(DCCAttEnum.AvisoBancarioCobrancaDireta, StringUtilities.readString(199, 207, linha));
            r.put(DCCAttEnum.NumAvisoBancario, StringUtilities.readString(207, 212, linha));
            r.put(DCCAttEnum.QtdTitulosBaixados, StringUtilities.readString(212, 220, linha));
            r.put(DCCAttEnum.ValorTotalBruto, StringUtilities.readString(220, 234, linha));
            r.put(DCCAttEnum.EmBranco4, StringUtilities.readString(234, 394, linha));
            r.put(DCCAttEnum.SequencialRegistro, StringUtilities.readString(394, 400, linha));
        }

    }

    public static RegistroRemessa preencherHeaderArquivo(RemessaVO remessa) {

        Date dataGeracao = remessa.getDataRegistro();

        RegistroRemessa headerArquivo = new RegistroRemessa(TipoRegistroEnum.HEADER_ARQUIVO);
        headerArquivo.put(DCCAttEnum.TipoRegistro, "0");
        headerArquivo.put(DCCAttEnum.CodigoOperacao, "1");
        headerArquivo.put(DCCAttEnum.IdentificacaoRemessa, "REMESSA");
        headerArquivo.put(DCCAttEnum.CodigoServico, "01");
        headerArquivo.put(DCCAttEnum.IdentificacaoServico, StringUtilities.formatarCampoEmBranco("COBRANCA", 15));

        headerArquivo.put(DCCAttEnum.NumeroContrato,
                StringUtilities.formatarCampoForcandoZerosAEsquerda(remessa.getConvenioCobranca().getNumeroContrato(), 12));

        headerArquivo.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(8));
        headerArquivo.put(DCCAttEnum.NomeEmpresa, StringUtilities.formatarCampoEmBranco(remessa.getConvenioCobranca().getEmpresa().getRazaoSocial(), 30));
        headerArquivo.put(DCCAttEnum.CodigoBanco, "707");
        headerArquivo.put(DCCAttEnum.NomeBanco, StringUtilities.formatarCampoEmBranco("BANCO DAYCOVAL", 15));
        headerArquivo.put(DCCAttEnum.DataGeracao, StringUtilities.formatarCampoData(dataGeracao, "ddMMyy"));
        headerArquivo.put(DCCAttEnum.EmBranco2, StringUtilities.formatarCampoEmBranco(294));
        headerArquivo.put(DCCAttEnum.SequencialRegistro, "000001");

        return headerArquivo;
    }

    public static RegistroRemessa preencherTrailerArquivo(RemessaVO remessa, int qtdRegistros, double valorTotalBruto) {
        RegistroRemessa trailerArquivo = new RegistroRemessa(TipoRegistroEnum.TRAILER_ARQUIVO);

        trailerArquivo.put(DCCAttEnum.TipoRegistro, "9");
        trailerArquivo.put(DCCAttEnum.EmBranco, StringUtilities.formatarCampoEmBranco(393));
        trailerArquivo.put(DCCAttEnum.SequencialRegistro, StringUtilities.formatarCampo(new BigDecimal(qtdRegistros), 6));

        Map<String, String> infoAdicional = new HashMap<String, String>();
        infoAdicional.put(DCCAttEnum.QuantidadeRegistros.name(), StringUtilities.formatarCampo(new BigDecimal(qtdRegistros), 10));
        infoAdicional.put(DCCAttEnum.ValorTotalBruto.name(), StringUtilities.formatarCampoMonetario(valorTotalBruto, 13));
        remessa.setProps(infoAdicional);

        return trailerArquivo;
    }

    public static StringBuilder prepareFile(RemessaVO remessa) {
        StringBuilder sb = new StringBuilder();

//        sb.append("<table>");

        //HEADER
        sb.append(remessa.getHeaderRemessaArquivo().toStringBuffer()).append("\r\n");
        sb.append(remessa.getHeaderRemessa().toStringBuffer()).append("\r\n");
        //DETAIL
        List<RegistroRemessa> lista = remessa.getDetailsRemessa();
        StringBuffer sbDetail = new StringBuffer();
        for (RegistroRemessa regD : lista) {
            sbDetail.append(regD.toStringBuffer()).append("\r\n");
        }
        sb.append(sbDetail);
        //TRAILER
        sb.append(remessa.getTrailerRemessa().toStringBuffer()).append("\r\n");
        ;
        sb.append(remessa.getTrailerRemessaArquivo().toStringBuffer());
        sb.append("\r\n");

//        sb.append("</table>");

        return sb;
    }

    public static String obterCodigosRemessaItem(StringBuilder dados) throws IOException {
        List<Long> codigos = new ArrayList<Long>();
        if (dados.length() > 2) {
            BufferedReader br = new BufferedReader(new StringReader(dados.toString()));
            String linha;
            while ((linha = br.readLine()) != null) {
                Long tipo = Long.valueOf(StringUtilities.readString(0, 1, linha));
                if (tipo.equals(1L)) {
                    Long codRetorno = Long.valueOf(StringUtilities.readString(108, 110, linha));
                    if (!ignorarStatusVenda(codRetorno)) {
                        String valor = StringUtilities.readString(116, 126, linha);
                        if (valor.isEmpty() || valor.replaceAll(" ", "").isEmpty()) {
                            valor = StringUtilities.readString(126, 133, linha);
                        }
                        codigos.add(Long.valueOf(valor));
                    }
                }
            }
        }
        Collections.sort(codigos);
        StringBuilder cods = new StringBuilder();
        for (Long cod : codigos) {
            cods.append(",").append(cod);
        }
        return cods.toString().replaceFirst(",", "");
    }

    private static boolean ignorarStatusVenda(Long codRetorno) {
        return codRetorno.equals(54L) //Código 54: Tarifa Mensal;
                || codRetorno.equals(52L); //Código 52: Tarifa Mensal Baixas na Carteira;
    }
}
