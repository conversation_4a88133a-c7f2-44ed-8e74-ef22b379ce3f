package servicos.impl.boleto;

/**
 * Created by GlaucoT on 25/10/2016
 */
public enum BancoEnum {

    ITAU(341, "Itaú"),
    BRADESCO(237, "Bradesco"),
    SANTANDER(33, "Santander"),
    BRB(70, "BRB"),
    BANCODOBRASIL(1, "Banco do Brasil"),
    BANCOOB(756, "Bancoob"),
    BNB(4, "BNB"),
    CAIXA(104, "Caixa"),
    S<PERSON>REDI(748, "<PERSON>credi"),
    SAFRA(422, "Safra")
    ;

    private int codigo;
    private String desc;

    BancoEnum(int codigo, String desc) {
        this.codigo = codigo;
        this.desc = desc;
    }

    public static String consultarNomePorCodigo(int codigo){
        for (BancoEnum o : BancoEnum.values() ) {
            if(o.codigo == codigo){
                return o.desc;
            }
        }
        return "";
    }

    public static BancoEnum consultarPorCodigo(int codigo){
        for (BancoEnum bancoEnum: BancoEnum.values()){
            if (bancoEnum.getCodigo() == codigo){
                return bancoEnum;
            }
        }
        return null;
    }

    public int getCodigo() {
        return codigo;
    }

    public void setCodigo(int codigo) {
        this.codigo = codigo;
    }
}
