package servicos.impl.ceopag;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.gson.Gson;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.arquitetura.SuperVO;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.basico.enumerador.TipoPessoa;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.PessoaCPFTO;
import negocio.comuns.financeiro.TransacaoCeopagVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.basico.Cliente;
import negocio.facade.jdbc.basico.Empresa;
import negocio.facade.jdbc.basico.Pessoa;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.apache.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.interfaces.CeopagServiceInterface;
import servicos.propriedades.PropsService;

import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;


public class CeopagService extends AbstractCobrancaOnlineServiceComum implements CeopagServiceInterface {
    private String urlApiCeopagPortal = "";
    private String urlApiCeopagGateway = "";
    private String urlApiCeopagReconciliation = "";
    private String urlApiCeopagWebhook = "";
    private String urlApiCeopagAuth = "";
    private String accessToken = "";
    private String refreshToken = "";
    private String key = "";
    private ConvenioCobrancaVO convenioCobrancaVO;
    private Pessoa pessoaDAO;
    private Cliente clienteDAO;
    private Transacao transacaoDAO;
    private ConvenioCobranca convenioCobrancaDAO;

    public CeopagService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.pessoaDAO = new Pessoa(con);
        this.clienteDAO = new Cliente(con);
        this.transacaoDAO = new Transacao(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioCobrancaVO = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        this.key = DAO.resolveKeyFromConnection(con);
        popularInformacoes();
    }

    public String body(Object dto) {
        Gson json = new Gson();
        return json.toJson(dto);
    }

    private void popularInformacoes() {
        if (this.convenioCobrancaVO != null) {
            if (this.convenioCobrancaVO.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                this.urlApiCeopagPortal = PropsService.getPropertyValue(PropsService.urlApiCeopagProducaoPortal);
                this.urlApiCeopagGateway = PropsService.getPropertyValue(PropsService.urlApiCeopagProducaoGateway);
                this.urlApiCeopagReconciliation = PropsService.getPropertyValue(PropsService.urlApiCeopagProducaoReconciliation);
                this.urlApiCeopagWebhook = PropsService.getPropertyValue(PropsService.urlApiCeopagProducaoWebhook);
                this.urlApiCeopagAuth = PropsService.getPropertyValue(PropsService.urlApiCeopagProducaoAuth);
            } else {
                this.urlApiCeopagPortal = PropsService.getPropertyValue(PropsService.urlApiCeopagSandboxPortal);
                this.urlApiCeopagGateway = PropsService.getPropertyValue(PropsService.urlApiCeopagSandboxGateway);
                this.urlApiCeopagReconciliation = PropsService.getPropertyValue(PropsService.urlApiCeopagSandboxReconciliation);
                this.urlApiCeopagWebhook = PropsService.getPropertyValue(PropsService.urlApiCeopagSandboxWebhook);
                this.urlApiCeopagAuth = PropsService.getPropertyValue(PropsService.urlApiCeopagSandboxAuth);
            }
        }
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacaoVO = null;
        Transacao transacaoDAO = null;
        Empresa empresaDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());
            empresaDAO = new Empresa(getCon());
            transacaoVO = criarTransacao(dadosCartao, new TransacaoCeopagVO(), TipoTransacaoEnum.CEOPAG, this.convenioCobrancaVO);
            transacaoVO.setCodigo(0);
            transacaoDAO.incluir(transacaoVO);

            gravarOutrasInformacoes(dadosCartao.getNumero(), dadosCartao, transacaoVO);

            EmpresaVO empresaVO = empresaDAO.consultarPorCodigo(transacaoVO.getEmpresa(), Uteis.NIVELMONTARDADOS_GESTAOREMESSA);
            ClienteVO clienteVO = this.clienteDAO.consultarPorCodigoPessoa(transacaoVO.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
            PessoaVO pessoaVO = this.pessoaDAO.consultarPorChavePrimaria(transacaoVO.getPessoaPagador().getCodigo(), Uteis.NIVELMONTARDADOS_PESSOA_PACTO_PAY);
            PessoaCPFTO pessoaCPFTO = new PessoaCPFTO();
            if (UteisValidacao.emptyString(pessoaVO.getCfp()) && clienteVO.isUtilizarResponsavelPagamento()) {
                try {
                    pessoaCPFTO = transacaoDAO.obterDadosPessoaPagador(pessoaVO, false, true);
                } catch (Exception ignore) {
                }
            }

            JSONObject parametrosPagamento = criarTransacaoJSON(transacaoVO, pessoaVO, pessoaCPFTO, dadosCartao, empresaVO);
            transacaoVO.setParamsEnvio(encriptarDadosSigilososEnvio(parametrosPagamento));
            transacaoDAO.alterar(transacaoVO);

            validarDadosTransacao(transacaoVO, dadosCartao);

            RespostaHttpDTO respostaHttpDTO = executarRequestGateway(transacaoVO, "/v2/charge/authorization", parametrosPagamento.toString(), MetodoHttpEnum.POST);
            processarRetorno(transacaoVO, respostaHttpDTO);

            //consultar para verificar se já foi Concluída com sucesso...
//            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
//                realizarConsultaSituacao(3, transacaoVO);
//            }

            preencherOutrasInformacoes(transacaoVO);
            transacaoDAO.alterar(transacaoVO);
            return transacaoVO;
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacaoVO, ex);
            verificarException(transacaoVO, ex);
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacaoVO, ex.getMessage());
            verificarException(transacaoVO, ex);
        } finally {
            gravarTentativaCartao(transacaoVO);
            empresaDAO = null;
            transacaoDAO = null;
        }
        return transacaoVO;
    }

    private String encriptarDadosSigilososEnvio(JSONObject parametrosPagamento) {
        parametrosPagamento = new JSONObject(parametrosPagamento.toString());
        JSONObject charge = parametrosPagamento.getJSONObject("charge");
        JSONArray transactions = charge.getJSONArray("transactions");
        JSONObject card = transactions.getJSONObject(0).getJSONObject("card");
        card.put("cardNumber", APF.getCartaoMascarado(card.getString("cardNumber")));
        if (card.has("cvv")) {
            card.put("cvv", "***");
        }
        return parametrosPagamento.toString();
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return null;
    }

    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return null;
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacaoOriginal, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        return null;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {
        // atualiza situação da transação, caso ela esteja aguardando
//        if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
//            retransmitirTransacao(transacaoVO, null, null);
//        }
        realizarCancelamentoTransacao(transacaoVO, estornarRecibo);
        return transacaoVO;
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    @Override
    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception {
        if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            RespostaHttpDTO respostaHttpDTO = executarRequestGateway(transacaoVO, "/v2/charge/" + transacaoVO.getCodigoExterno(), null, MetodoHttpEnum.GET);
            processarRetorno(transacaoVO, respostaHttpDTO);
        }
    }

    @Override
    public String consultarTransacao(Integer id) throws Exception {
        return null;
    }

    @Override
    public void incluirPessoa(PessoaVO pessoa, TransacaoVO transacaoVO) throws Exception {
    }

    @Override
    public void incluirPessoa(PessoaVO pessoa, TransacaoVO transacaoVO, ClienteVO clienteVO) throws Exception {
    }

    private JSONObject criarTransacaoJSON(TransacaoVO transacaoVO, PessoaVO pessoaVO, PessoaCPFTO pessoaCPFTO,
                                          CartaoCreditoTO cartaoCreditoTO, EmpresaVO empresaVO) throws Exception {

        String identificador = "TRA" + transacaoVO.getCodigo();
        Uteis.logarDebug("IDENTIFICADOR CEOPAG: " + identificador);

        JSONObject charge = new JSONObject();
        //Identificador da transação
        charge.put("merchantChargeId", identificador);
        charge.put("source", 1); //https://ceopag-docs.aditum.com.br/#paymentsource
        charge.put("origin", "SistemaPacto");

        charge.put("customer", gerarCustomer(pessoaVO, pessoaCPFTO));
        charge.put("transactions", gerarTransactions(identificador, transacaoVO, cartaoCreditoTO, pessoaVO, empresaVO, pessoaCPFTO));
        charge.put("metadata", gerarMetadata(transacaoVO));

        JSONObject body = new JSONObject();
        body.put("charge", charge);
        return body;
    }

    private void processarRetorno(TransacaoVO transacao, RespostaHttpDTO respostaHttpDTO) {
        incluirHistoricoRetornoTransacao(transacao, logRequest(respostaHttpDTO), "processarRetorno");
        transacao.setParamsResposta(respostaHttpDTO.getResponse());

        JSONObject retornoJSON = null;
        if (!UteisValidacao.emptyString(respostaHttpDTO.getResponse())) {
            retornoJSON = new JSONObject(respostaHttpDTO.getResponse());
        }

        if (retornoJSON == null || (retornoJSON.has("success") && !retornoJSON.getBoolean("success"))) {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            return;
        }


        JSONObject chargeJSON = retornoJSON.optJSONObject("charge");
        if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
            String chargeID = chargeJSON.optString("id");
            if (!UteisValidacao.emptyString(chargeID)) {
                transacao.setCodigoExterno(chargeID);
            }
        }

        String chargeStatus = chargeJSON.getString("chargeStatus"); //https://ceopag-docs.aditum.com.br/#chargestatus
        if (chargeStatus.equalsIgnoreCase("Authorized")) {
            transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
        } else if (chargeStatus.equalsIgnoreCase("PreAuthorized")) {
            transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
        } else if (chargeStatus.equalsIgnoreCase("PendingCancel") ||
                chargeStatus.equalsIgnoreCase("Canceled")) {
            transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
        } else if (chargeStatus.equalsIgnoreCase("NotAuthorized")) {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
        } else {
            transacao.setSituacao(SituacaoTransacaoEnum.COM_ERRO);
        }
    }

    public void realizarConsultaSituacao(int qtd, TransacaoVO transacaoVO) throws Exception {
        if (!UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            if (qtd > 0) {
//                RespostaHttpDTO respostaHttpDTO = consultarTransacao(transacaoVO.getCodigoExterno());
//                processarRetorno(transacaoVO, respostaHttpDTO);
//                if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.APROVADA)) {
//                    Thread.sleep(1000);
//                    realizarConsultaSituacao(qtd - 1, transacaoVO);
//                }
            }
        }
    }

    private JSONArray gerarTransactions(String identificador, TransacaoVO transacaoVO,
                                        CartaoCreditoTO cartaoCreditoTO, PessoaVO pessoaVO,
                                        EmpresaVO empresaVO, PessoaCPFTO pessoaCPFTO) throws Exception {
        JSONObject transaction = new JSONObject();
        transaction.put("paymentType", 2); //https://ceopag-docs.aditum.com.br/#paymenttype
        int amountInt = (int) (transacaoVO.getValor() * 100);
        transaction.put("amount", amountInt);
        transaction.put("installmentNumber", cartaoCreditoTO.getParcelas());
        if (cartaoCreditoTO.getParcelas() > 1) { //https://ceopag-docs.aditum.com.br/#installmenttype
            transaction.put("installmentType", "Merchant");
        } else {
            transaction.put("installmentType", "None");
        }
        transaction.put("softDescriptor", empresaVO.getRazaoSocialParaSoftDescriptor(false));
        transaction.put("merchantTransactionId", identificador);
        transaction.put("card", gerarCard(cartaoCreditoTO, pessoaVO, pessoaCPFTO));

        JSONArray transactions = new JSONArray();
        transactions.put(transaction);
        return transactions;
    }

    private String obterDocumento(PessoaVO pessoaVO, PessoaCPFTO pessoaCPFTO) throws ConsistirException {
        if (pessoaVO.getCategoriaPessoa().equals(TipoPessoa.FISICA)) {

            String cpf = pessoaVO.getCfp();
            if (UteisValidacao.emptyString(cpf)) {
                cpf = pessoaCPFTO.getCpf();
            }
            if (UteisValidacao.emptyString(cpf)) {
                cpf = pessoaCPFTO.getCpfResponsavel();
            }

            if (UteisValidacao.emptyString(cpf)) {
                throw new ConsistirException("Cliente não possui CPF cadastrado e o mesmo é obrigatório para Ceopag!");
            }
            return cpf.trim();
        } else {

            String cnpj = pessoaVO.getCnpj();
            if (UteisValidacao.emptyString(cnpj)) {
                throw new ConsistirException("Cliente não possui CNPJ cadastrado e o mesmo é obrigatório para Ceopag!");
            }
            return cnpj.trim();
        }
    }

    private JSONObject gerarCustomer(PessoaVO pessoaVO, PessoaCPFTO pessoaCPFTO) throws Exception {
        JSONObject customer = new JSONObject();
        customer.put("name", Uteis.retirarAcentuacao(pessoaVO.getNome()).trim());
        customer.put("email", pessoaVO.getEmail().trim());

        if (pessoaVO.getCategoriaPessoa().equals(TipoPessoa.FISICA)) {

            String cpf = pessoaVO.getCfp();
            if (UteisValidacao.emptyString(cpf)) {
                cpf = pessoaCPFTO.getCpf();
            }
            if (UteisValidacao.emptyString(cpf)) {
                cpf = pessoaCPFTO.getCpfResponsavel();
            }

            if (UteisValidacao.emptyString(cpf)) {
                throw new ConsistirException("Cliente não possui CPF cadastrado!");
            }

            if (!SuperVO.verificaCPF(cpf.trim())) {
                throw new Exception("O CPF " + cpf.trim() + " é inválido!");
            }

            customer.put("entityType", 1);
            customer.put("document", cpf.trim());
            customer.put("documentType", 1);
        } else {

            String cnpj = pessoaVO.getCnpj();
            if (UteisValidacao.emptyString(cnpj)) {
                throw new ConsistirException("Cliente não possui CNPJ cadastrado!");
            }

            customer.put("entityType", 2);
            customer.put("document", cnpj);
            customer.put("documentType", 2);
        }
        return customer;
    }

    private JSONArray gerarMetadata(TransacaoVO transacaoVO) {
        JSONArray metadata = new JSONArray();

        JSONObject meta1 = new JSONObject();
        meta1.put("key", "chave");
        meta1.put("value", this.key);
        metadata.put(meta1);

        return metadata;
    }

    private JSONObject gerarCard(CartaoCreditoTO cartaoCreditoTO, PessoaVO pessoaVO,
                                 PessoaCPFTO pessoaCPFTO) throws Exception {
        String numeroCartao = "";
        if (!UteisValidacao.emptyString(cartaoCreditoTO.getNumero())) {
            numeroCartao = cartaoCreditoTO.getNumero();
        }

        JSONObject card = new JSONObject();
        card.put("cardNumber", numeroCartao);
        card.put("expirationMonth", cartaoCreditoTO.getValidade().substring(0, 2));
        card.put("expirationYear", cartaoCreditoTO.getAnoValidadeYYYY());
        card.put("cardholderName", Uteis.retirarAcentuacao(cartaoCreditoTO.getNomeTitular().trim()));
        String documentoTitular = Uteis.formatarCpfCnpj(cartaoCreditoTO.getCpfCnpjPortador(), true);
        if (UteisValidacao.emptyString(documentoTitular)) {
            //pegar o documento do aluno
            documentoTitular = Uteis.formatarCpfCnpj(obterDocumento(pessoaVO, pessoaCPFTO), true);
        }
        if (UteisValidacao.emptyString(documentoTitular)) {
            throw new Exception("O nº documento (CPF/CNPJ) do titular do cartão é obrigatório.");
        }

        if (documentoTitular.length() == 11) { //CPF
            if (!UteisValidacao.isValidCPF(documentoTitular.trim())) {
                throw new Exception("O CPF do titular do cartão é inválido: " + documentoTitular.trim());
            }
        } else if (documentoTitular.length() == 14) { //CNPJ
            if (!UteisValidacao.validaCNPJ(documentoTitular.trim())) {
                throw new Exception("O CNPJ do titular do cartão é inválido: " + documentoTitular.trim());
            }
        }

        card.put("cardholderDocument", documentoTitular.trim());
        if (cartaoCreditoTO.isTransacaoPresencial() &&
                !UteisValidacao.emptyString(cartaoCreditoTO.getCodigoSeguranca())) {
            card.put("cvv", cartaoCreditoTO.getCodigoSeguranca());
        }
        return card;
    }

    private RespostaHttpDTO executarRequestGateway(TransacaoVO transacaoVO, String endPoint, String body, MetodoHttpEnum metodoHttpEnum) throws Exception {
        String path = this.urlApiCeopagGateway + endPoint;
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", obterAcessToken(transacaoVO));

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, body, metodoHttpEnum);
        incluirHistoricoRetornoTransacao(transacaoVO, logRequest(respostaHttpDTO), "executarRequestGateway");
        return respostaHttpDTO;
    }

    private String obterAcessToken(TransacaoVO transacaoVO) throws Exception {
        if (UteisValidacao.emptyString(this.accessToken)) {
            executarRequestGenerateToken(transacaoVO);
        }
        if (UteisValidacao.emptyString(this.accessToken)) {
            throw new Exception("Não foi possível obter a apiKey");
        }
        return "Bearer " + this.accessToken;
    }

    private void executarRequestGenerateToken(TransacaoVO transacaoVO) throws Exception {
        String path = this.urlApiCeopagPortal + "/v1/Login/GenerateToken";
        Map<String, String> headers = new HashMap<>();
        headers.put("MerchantToken", this.convenioCobrancaVO.getCodigoAutenticacao01());

        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(path, headers, null, null, MetodoHttpEnum.POST);
        incluirHistoricoRetornoTransacao(transacaoVO, logRequest(respostaHttpDTO), "executarRequestGenerateToken");
        if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
            throw new Exception(respostaHttpDTO.getResponse());
        }
        JSONObject jsonResp = new JSONObject(respostaHttpDTO.getResponse());
        this.accessToken = jsonResp.optString("generatedToken");
        this.refreshToken = jsonResp.optString("refreshToken");
    }

    private void realizarCancelamentoTransacao(TransacaoVO transacaoVO, Boolean estornarRecibo) throws Exception {
        RespostaHttpDTO respostaHttpDTO = executarRequestGateway(transacaoVO, "/v2/charge/cancelation/" + transacaoVO.getCodigoExterno(), new JSONObject().toString(), MetodoHttpEnum.PUT);
        processarRetornoCancelamento(transacaoVO, respostaHttpDTO);
        if (estornarRecibo && transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) &&
                !UteisValidacao.emptyNumber(transacaoVO.getReciboPagamento())) {
            estornarRecibo(transacaoVO, estornarRecibo);
            if (transacaoVO.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
            }
        }
        this.transacaoDAO.alterar(transacaoVO);
    }

    private void processarRetornoCancelamento(TransacaoVO transacaoVO, RespostaHttpDTO respostaHttpDTO) throws Exception {
        incluirHistoricoRetornoTransacao(transacaoVO, logRequest(respostaHttpDTO), "processarRetornoCancelamento");
        transacaoVO.setResultadoCancelamento(respostaHttpDTO.getResponse());
        try {
            JSONObject cancelamentoJSON = new JSONObject(respostaHttpDTO.getResponse());
            if (cancelamentoJSON.has("canceled") &&
                    cancelamentoJSON.getBoolean("canceled")) {
                transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                transacaoVO.setDataHoraCancelamento(Calendario.hoje());
            } else if (cancelamentoJSON.has("canceled") &&
                    cancelamentoJSON.has("success")) {
                boolean canceled = cancelamentoJSON.getBoolean("canceled");
                boolean success = cancelamentoJSON.getBoolean("success");
                if (!canceled && !success && cancelamentoJSON.has("errors")) {
                    JSONArray errors = cancelamentoJSON.getJSONArray("errors");
                    if (errors.length() > 0) {
                        JSONObject error = errors.getJSONObject(0);
                        if (error.has("message") && error.optString("message").contains("já foi cancelada")) {
                            transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                            transacaoVO.setDataHoraCancelamento(Calendario.hoje());
                        } else {
                            throw new Exception(error.optString("message"));
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            if (ex.getMessage().contains("foi negada por causa da configuração de cancelamento do estabelecimento")) {
                throw ex;
            }
            consultarSituacaoCobrancaTransacao(transacaoVO);
        }
    }

    private String logRequest(RespostaHttpDTO respostaHttpDTO) {
        try {
            return new JSONObject(respostaHttpDTO).toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return respostaHttpDTO.getResponse();
        }
    }

}
