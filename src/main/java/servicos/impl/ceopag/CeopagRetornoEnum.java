package servicos.impl.ceopag;

import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;

public enum CeopagRetornoEnum {

    RetornoNENHUM("NENHUM", ""),
    RetornoLR_04("LR_04", "Transação não autorizada. Entre em contato com o banco emissor do cartão."),
    RetornoLR_G4("LR_G4", "Transação não autorizada. Cartão inválido.", CodigoRetornoPactoEnum.CARTAO_INVALIDO,OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    RetornoLR_05("LR_05", "Transação não autorizada. Entre em contato com o banco emissor do cartão."),
    RetornoLR_06("LR_06", "Transação não autorizada pela operadora do cartão. Por favor, tente novamente."),
    RetornoLR_14("LR_14", "O número do cartão informado é inválido. Por favor, verifique e tente novamente.", CodigoRetornoPactoEnum.CARTAO_INVALIDO,OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    RetornoLR_41("LR_41", "O número do cartão informado é inválido. Por favor, verifique e tente novamente.", CodigoRetornoPactoEnum.CARTAO_INVALIDO,OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    RetornoLR_51("LR_51", "O número do cartão informado é inválido. Por favor, verifique e tente novamente.", CodigoRetornoPactoEnum.CARTAO_INVALIDO,OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    RetornoLR_54("LR_54", "O número do cartão informado é inválido. Por favor, verifique e tente novamente.", CodigoRetornoPactoEnum.CARTAO_INVALIDO,OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    RetornoLR_57("LR_57", "O número do cartão informado é inválido. Por favor, verifique e tente novamente.", CodigoRetornoPactoEnum.CARTAO_INVALIDO,OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),

    //retorno sandbox
    RetornoMOCK("mock", "Retorno sandbox."),
    ;

    private String codigo;
    private String descricao;
    private CodigoRetornoPactoEnum codigoRetornoPacto;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobranca;

    private CeopagRetornoEnum(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
    }

    private CeopagRetornoEnum(String codigo, String descricao, CodigoRetornoPactoEnum codigoRetornoPacto, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.codigo = codigo;
        this.descricao = descricao;
        this.codigoRetornoPacto = codigoRetornoPacto;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    public String getCodigo() {
        return codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public CodigoRetornoPactoEnum getCodigoRetornoPacto() {
        return codigoRetornoPacto;
    }

    public static CeopagRetornoEnum valueOff(String id) {
        for (CeopagRetornoEnum pagoLivreStatusEnum : CeopagRetornoEnum.values()) {
            if (pagoLivreStatusEnum.getCodigo().equals(id)) {
                return pagoLivreStatusEnum;
            }
        }
        return RetornoNENHUM;
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobranca() {
        return operacaoRetornoCobranca;
    }

}
