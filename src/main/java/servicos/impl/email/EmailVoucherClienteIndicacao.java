package servicos.impl.email;

import acesso.webservice.DaoAuxiliar;
import br.com.pactosolucoes.comuns.util.FileUtilities;
import controle.arquitetura.SuperControle;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.comuns.utilitarias.queue.MsgTO;
import servicos.operacoes.midias.MidiaService;
import servicos.operacoes.midias.commons.MidiaEntidadeEnum;

import java.io.File;

public class EmailVoucherClienteIndicacao {

    public void enviarEmailVoucherIndicacao(String email, ClienteVO clienteVO, String voucherIndicacao, String chave, Integer codigoEmpresa) throws Exception {
        try {
            EmpresaVO empresaVO = DaoAuxiliar.retornarAcessoControle(chave).getEmpresaDao().consultarPorChavePrimaria(codigoEmpresa, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
            MsgTO msg = new MsgTO( new StringBuffer(gerarHTMLEmail(clienteVO, voucherIndicacao, empresaVO, chave).toString()),
                    "Olá, aqui está seu código de indicação, obrigado por aceitar o convite!",
                    empresaVO.getNome(),
                    true, SuperControle.getConfiguracaoSMTPRobo(),false,
                    new String[]{email}
            );
            UteisValidacao.enfileirarEmail(msg);
        } catch (Exception ex) {
            ex.printStackTrace();
            throw ex;
        }
    }

    public String gerarHTMLEmail(ClienteVO clienteVO, String voucherIndicacao, EmpresaVO empresaVO, String chave) throws Exception {
        File arq = new File(getClass().getResource("/br/com/pactosolucoes/comuns/util/resources/emailVoucherClienteindicacaoVendasOnline.txt").toURI());
        StringBuilder texto = FileUtilities.readContentFile(arq.getAbsolutePath(), "UTF-8");

        String empresaNome = empresaVO.getNome().toUpperCase();
        String empresaUrlLogo = getLogoEmpresa(chave, empresaVO);
        String empresaEndereco = toTitledCase(empresaVO.getEndereco());
        String empresaNumero = empresaVO.getNumero();
        String empresaSetor = toTitledCase(empresaVO.getSetor());
        String empresaCidade = toTitledCase(empresaVO.getCidade().getNome());
        String empresaTelefone = empresaVO.getTelComercial1();
        String empresaEmail = empresaVO.getEmail().toLowerCase();
        String nomeAluno = clienteVO.getPessoa().getNome();

        return texto.toString()
                .replaceAll("#ACADEMIA_NOME#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaNome))
                .replaceAll("#VOUCHER_ALUNO#", voucherIndicacao)
                .replaceAll("#ACADEMIA_URL_LOGO#", empresaUrlLogo)
                .replaceAll("#ACADEMIA_ENDERECO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaEndereco))
                .replaceAll("#ACADEMIA_NUMERO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaNumero))
                .replaceAll("#ACADEMIA_SETOR#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaSetor))
                .replaceAll("#ACADEMIA_CIDADE#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaCidade))
                .replaceAll("#ACADEMIA_TELEFONE#", empresaTelefone)
                .replaceAll("#ACADEMIA_EMAIL#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(empresaEmail))
                .replaceAll("#NOME_ALUNO#", Uteis.trocarAcentuacaoPorAcentuacaoHTML(nomeAluno));
    }

    public String getLogoEmpresa(String chave, EmpresaVO empresaVO) {
        try {
            String genKey = MidiaService.getInstance().genKey(chave, MidiaEntidadeEnum.FOTO_EMPRESA_EMAIL, empresaVO.getCodigo().toString());
            return Uteis.getPaintFotoDaNuvem(genKey);
        } catch (Exception ignored) {
            return "";
        }
    }

    public String toTitledCase(String word) {
        String[] words = word.trim().split("\\s");
        StringBuilder sb = new StringBuilder();

        if (!word.equals("")) {
            for (int i = 0; i < words.length; i++) {
                sb.append(words[i].substring(0, 1).toUpperCase() + words[i].substring(1).toLowerCase());
                sb.append(" ");
            }
        }
        return sb.toString();
    }

}
