package servicos.impl.turma;

import negocio.comuns.basico.ColaboradorVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.plano.HistoricoProfessorTurmaVO;
import negocio.comuns.plano.HorarioTurmaVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.SuperEntidade;
import servicos.interfaces.turma.HistoricoProfessorTurmaService;

import java.sql.Connection;
import java.sql.ResultSet;
import java.util.*;

public class HistoricoProfessorTurmaServiceImpl extends SuperEntidade implements HistoricoProfessorTurmaService {


    public HistoricoProfessorTurmaServiceImpl(Connection con) throws Exception {
        super(con);
    }

    public HistoricoProfessorTurmaServiceImpl() throws Exception {
        super();
    }

    public Map<Integer, List<HistoricoProfessorTurmaVO>> montarHistoricoProfessor(Date inicio) throws Exception {
        Map<Integer, List<HistoricoProfessorTurmaVO>> mapa = new HashMap<Integer, List<HistoricoProfessorTurmaVO>>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select fotokey, nome, horarioturma, professor, inicio, fim from historicoprofessorturma h ");
        sql.append(" inner join colaborador c on h.professor = c.codigo ");
        sql.append(" inner join pessoa p on p.codigo = c.pessoa ");
        sql.append(" where inicio::date >= '").append(Uteis.getDataJDBC(inicio)).append("' or fim::date >= '").append(Uteis.getDataJDBC(inicio)).append("' ");
        sql.append(" order by inicio ");

        ResultSet rs = criarConsulta(sql.toString(), con);
        while(rs.next()){
            List<HistoricoProfessorTurmaVO> horarios = mapa.get(rs.getInt("horarioturma"));
            if(horarios == null){
                horarios = new ArrayList<HistoricoProfessorTurmaVO>();
                mapa.put(rs.getInt("horarioturma"), horarios);
            }
            HistoricoProfessorTurmaVO hist = new HistoricoProfessorTurmaVO();
            hist.setInicio(rs.getDate("inicio"));
            hist.setFim(rs.getDate("fim"));
            hist.setFotoKey(rs.getString("fotokey"));
            hist.setProfessor(new ColaboradorVO());
            hist.getProfessor().setPessoa(new PessoaVO());
            hist.getProfessor().getPessoa().setNome(rs.getString("nome"));
            hist.getProfessor().setCodigo(rs.getInt("professor"));
            horarios.add(hist);
        }

        return mapa;
    }

    public void inserirHistoricoMudancaProfessor(HorarioTurmaVO horarioTurma, Integer professorAtual, Date iniciovigencia) throws Exception{
        // caso o professor atual seja nulo, significa que o horario está sendo incluido
        if(professorAtual == null){
            HistoricoProfessorTurmaVO novo = new HistoricoProfessorTurmaVO();
            novo.setHorarioTurma(horarioTurma);
            novo.setProfessor(horarioTurma.getProfessor());
            novo.setLancamento(Calendario.hoje());
            novo.setInicio(iniciovigencia);
            getFacade().getHistoricoProfessor().incluir(novo);
        }else{
            //obter ultimo historico, que é o registro sem data fim
            HistoricoProfessorTurmaVO ultimo = getFacade().getHistoricoProfessor().consultarUltimo(horarioTurma.getCodigo());
            if(ultimo == null){
                //caso não exista, procurar o ultimo registro de toda o historico
                List<HistoricoProfessorTurmaVO> historico = getFacade().getHistoricoProfessor().consultarTodos(horarioTurma.getCodigo());
                if(!UteisValidacao.emptyList(historico)){
                    ultimo = historico.get(0);
                }
            }
            Date agora = Calendario.hoje();
            //caso ainda não exista, é necessário inserir um historico antes de mudar o professor
            if(ultimo == null){
                HistoricoProfessorTurmaVO atual = new HistoricoProfessorTurmaVO();
                atual.setHorarioTurma(horarioTurma);
                atual.setProfessor(new ColaboradorVO());
                atual.getProfessor().setCodigo(professorAtual);
                atual.setLancamento(agora);
                atual.setInicio(iniciovigencia);
                atual.setFim(agora);
                getFacade().getHistoricoProfessor().incluir(atual);
            }else{
                ultimo.setFim(agora);
                getFacade().getHistoricoProfessor().alterar(ultimo);
            }
            HistoricoProfessorTurmaVO novo = new HistoricoProfessorTurmaVO();
            novo.setHorarioTurma(horarioTurma);
            novo.setProfessor(horarioTurma.getProfessor());
            novo.setLancamento(agora);
            novo.setInicio(agora);
            getFacade().getHistoricoProfessor().incluir(novo);
        }


    }

}
