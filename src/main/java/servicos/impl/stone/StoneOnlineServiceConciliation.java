package servicos.impl.stone;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.enumeradores.SituacaoItemExtratoEnum;
import br.com.pactosolucoes.enumeradores.TipoConciliacaoEnum;
import br.com.pactosolucoes.oamd.controle.basico.DAO;
import com.google.gson.Gson;
import controle.arquitetura.SuperControle;
import negocio.comuns.arquitetura.DetalhesRequestEnviadaVO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.crm.ConfiguracaoSistemaCRMVO;
import negocio.comuns.financeiro.CartaoCreditoTO;
import negocio.comuns.financeiro.ConfiguracaoFinanceiroVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.ExtratoDiarioItemVO;
import negocio.comuns.financeiro.MovPagamentoVO;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisEmail;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.arquitetura.DetalhesRequestEnviada;
import negocio.facade.jdbc.arquitetura.Usuario;
import negocio.facade.jdbc.arquitetura.ZillyonWebFacade;
import negocio.facade.jdbc.financeiro.ExtratoDiarioItem;
import negocio.facade.jdbc.utilitarias.Conexao;
import org.apache.commons.io.Charsets;
import org.json.JSONArray;
import org.json.JSONObject;
import org.json.XML;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.stone.bean.StoneConciliation;
import servicos.impl.stone.bean.StoneConciliationCancellation;
import servicos.impl.stone.bean.StoneConciliationChargeBack;
import servicos.impl.stone.bean.StoneConciliationFinancialTransaction;
import servicos.impl.stone.bean.StoneConciliationInstallment;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;

import java.sql.Connection;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static negocio.comuns.utilitarias.Calendario.somarDias;
import static negocio.facade.jdbc.arquitetura.FacadeManager.getFacade;
import static servicos.impl.stone.SonteOnlineServiceConciliationCrypt.encryptSha512Hmac;

/**
 * Serviço responsável por realizar a comunicação com a API <b>Conciliação Stone</b>.
 * Para que o "serviço de comunicação" com a API de conciliação da Stone funcione correntamente,
 * é necessário que a empresa esteja cadastrada no (ZW) Convênio de Cobrança, tipo DCC Stone Online.
 *
 * <p>
 * Veja coleção da API de HOMOLOGAÇÃO da STONE no postman em: <b>./doc/stone/postman</b>
 * </p>
 *
 * <AUTHOR> Christian
 * @see <a href="https://conciliacao.stone.com.br/v2.2/docs">Stone Online (API de Conciliação)</a>
 * @since 04/04/2019
 */
public class StoneOnlineServiceConciliation {

    private final static String URL_API_STONE_V1 = PropsService.getPropertyValue(PropsService.urlApiStoneOnlineConciliationV1);
    private final static String URL_API_STONE_V2 = PropsService.getPropertyValue(PropsService.urlApiStoneOnlineConciliationV2);
    private final static String CLIENT_APPLICATION_KEY = "Bearer " + PropsService.getPropertyValue(PropsService.clientApplicationKeyConciliation); // Authorization
    private final static String SECRET_KEY = PropsService.getPropertyValue(PropsService.secretKeyConciliation);
    private final static String CLIENT_ENCRYPTION_STRING = PropsService.getPropertyValue(PropsService.xAuthorizationRawData); // X-Authorization-Raw-Data


    public static void processarExtratosStone(EmpresaVO empresa, ConvenioCobrancaVO convenio, Date reprocessarAPartirDe, Date reprocessarAte, boolean convenioPossuiVariasEmpresasConfiguradas,
                                              Connection conexao, String origem) throws StoneErroComunicacaoAPIException {
        int dia = 1;
        int i = 0;
        if (reprocessarAPartirDe != null && Math.abs(Calendario.diferencaEmDias(reprocessarAPartirDe, reprocessarAte)) >= 0)
            dia = Math.abs(Calendario.diferencaEmDias(reprocessarAPartirDe, reprocessarAte));
        for (; i <= dia; i++) {

            // Conciliation generation is only permitted in past dates.
            Date dataTransacao = somarDias((reprocessarAPartirDe != null ? reprocessarAPartirDe : Calendario.ontem()), i);
            if (Calendario.maiorOuIgual(dataTransacao, Calendario.hoje())) {
                continue;
            }
            String arquivoProcessamentoTemp = "StoneOnlineServiceConciliation:" + new SimpleDateFormat("yyyyMMdd").format(dataTransacao) + "-" + convenio.getCodigoAutenticacao01();
            String retornoXml = "";
            try {
                 if (getFacade().getExtratoDiarioItem().arquivoProcessado(arquivoProcessamentoTemp)) {
                    Uteis.logarDebug("Arquivo já processado: " + arquivoProcessamentoTemp);
                    continue;
                }

                int tentativa = 0;
                while (tentativa <= 10) {
                    try {
                        ++tentativa;
                        Uteis.logarDebug("Tentativa: " + tentativa + " | Buscando conciliação do dia: " +
                                Uteis.getData(dataTransacao) + " utilizando Stone Code: " + convenio.getCodigoAutenticacao01() +
                                (!UteisValidacao.emptyString(convenio.getDescricao()) ? " | CONVÊNIO: " + convenio.getDescricao() : ""));

                        //tentar na API v1
                        retornoXml = executarRequestStone(null, convenio, dataTransacao, false);

                        if (!UteisValidacao.emptyString(retornoXml)) {
                            DetalhesRequestEnviada detalhesRequestEnviadaDAO;
                            Connection connection;
                            try {
                                connection = Conexao.getInstance().obterNovaConexaoBaseadaOutra(conexao);
                                detalhesRequestEnviadaDAO = new DetalhesRequestEnviada(connection);
                                String path = urlRequisicaoV1(convenio.getCodigoAutenticacao01(), dataTransacao);
                                detalhesRequestEnviadaDAO.incluir(montarObjDetalhesRequest(path, "extratoDiarioItem", retornoXml, origem));
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            } finally {
                                connection = null;
                                detalhesRequestEnviadaDAO = null;
                            }

                            break;
                        }

                        if (reprocessarAPartirDe == null) { //só aguardar quando é o processo automatico, reprocessamento não precisa aguardar
                            Uteis.logarDebug("Erro... Aguardar 5 segundos e tentar novamente extrato");
                            Thread.sleep(5000);
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        if (ex.getMessage() != null &&
                                ex.getMessage().contains("does not have access")) { //se não concedeu acesso não consultar mais que uma vez...
                            break;
                        }
                        if (reprocessarAPartirDe == null) { //só aguardar quando é o processo automatico, reprocessamento não precisa aguardar
                            Uteis.logarDebug("Erro... Aguardar 5 segundos e tentar novamente extrato | " + ex.getMessage());
                            Thread.sleep(5000);
                        }
                    }
                }
                if (!UteisValidacao.emptyString(retornoXml)) {
                    processarRetornoXMLtoJSON(retornoXml, convenio, empresa, arquivoProcessamentoTemp, dataTransacao, convenioPossuiVariasEmpresasConfiguradas);
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                Uteis.logarDebug("Erro ao processar '" + arquivoProcessamentoTemp + "' | " + ex.getMessage());
                try {
                    notificarErro(convenio, empresa, ex, getFacade().getCliente().getCon());
                } catch (Exception e) {
                    ex.printStackTrace();
                }
            }
        }
    }

    public static String executarRequestStone(RequestHttpService service, ConvenioCobrancaVO convenio,
                                               Date dataTransacao, boolean v2) throws Exception {
        String url = urlRequisicaoV1(convenio.getCodigoAutenticacao01(), dataTransacao);
        Map<String, String> header = header();

        Uteis.logarDebug("StoneConciliation... | URL: " + url + " | Header: " + header);

        String result = ExecuteRequestHttpService.executeHttpRequest(url, null,
                header, ExecuteRequestHttpService.METODO_GET,
                Charsets.UTF_8.name());

        JSONObject jsonObject = XML.toJSONObject(result);
        if (jsonObject.optJSONObject("Error") != null) {
            if (!UteisValidacao.emptyString(jsonObject.optJSONObject("Error").optString("Message"))) {
                throw new Exception(jsonObject.optJSONObject("Error").optString("Message"));
            } else {
                throw new Exception(result);
            }
        }

        if (jsonObject.optJSONObject("Conciliation") != null) {
            return jsonObject.toString();
        }
        throw new Exception(result);
    }

    public static String executarRequestStone(String stoneCode,
                                              String dataTransacao) throws Exception {
        String url = URL_API_STONE_V1 + "/" + stoneCode + "/conciliation-file/" + dataTransacao;
        Map<String, String> header = header();

        String result = ExecuteRequestHttpService.executeHttpRequest(url, null,
                header, ExecuteRequestHttpService.METODO_GET,
                Charsets.UTF_8.name());

        JSONObject jsonObject = XML.toJSONObject(result);
        if (jsonObject.optJSONObject("Error") != null) {
            if (!UteisValidacao.emptyString(jsonObject.optJSONObject("Error").optString("Message"))) {
                throw new Exception(jsonObject.optJSONObject("Error").optString("Message"));
            } else {
                throw new Exception(result);
            }
        }

        if (jsonObject.optJSONObject("Conciliation") != null) {
            return jsonObject.toString();
        }
        throw new Exception(result);
    }

    private static void processarRetornoXMLtoJSON(String retornoXml, ConvenioCobrancaVO convenio, EmpresaVO empresa,
                                                  String arquivoProcessamentoTemp, Date dataArquivo, boolean convenioPossuiVariasEmpresasConfiguradas) throws Exception {

        JSONObject conciliacao = new JSONObject(retornoXml);
        JSONObject conciliacaoJSON = conciliacao.getJSONObject("Conciliation");

        String[] vendaPagamento = new String[]{"FinancialTransactions", "FinancialTransactionsAccounts"};

        for (String strFinTransA : vendaPagamento) {
            if (!conciliacaoJSON.get(strFinTransA).toString().isEmpty() &&
                    (conciliacaoJSON.getJSONObject("Trailer").getInt("PaidInstallmentsQuantity") > 0 ||
                            conciliacaoJSON.getJSONObject("Trailer").getInt("CapturedTransactionsQuantity") > 0 ||
                            conciliacaoJSON.getJSONObject("Trailer").getInt("ChargedCancellationsQuantity") > 0 ||
                            conciliacaoJSON.getJSONObject("Trailer").getInt("PaidChargebacksRefundQuantity") > 0 ||
                            conciliacaoJSON.getJSONObject("Trailer").getInt("ChargedChargebacksQuantity") > 0 ||
                            conciliacaoJSON.getJSONObject("Trailer").getInt("ChargebacksQuantity") > 0)) {
                if (conciliacaoJSON.get(strFinTransA) instanceof JSONObject) {
                    JSONArray array = new JSONArray();
                    if (conciliacaoJSON.getJSONObject(strFinTransA).get("Transaction") instanceof JSONObject) {
                        JSONObject transactionSingle = conciliacaoJSON.getJSONObject(strFinTransA).getJSONObject("Transaction");
                        array = new JSONArray().put(transactionSingle);
                        conciliacaoJSON.remove(strFinTransA);
                        conciliacaoJSON.put(strFinTransA, array);
                    } else if (conciliacaoJSON.getJSONObject(strFinTransA).get("Transaction") instanceof JSONArray) {
                        array = conciliacaoJSON.getJSONObject(strFinTransA).getJSONArray("Transaction");
                    }
                    conciliacaoJSON.remove(strFinTransA);
                    conciliacaoJSON.put(strFinTransA, array);
                }

                for (int i = 0; i < conciliacaoJSON.getJSONArray(strFinTransA).length(); i++) {
                    JSONArray array = new JSONArray();
                    if (conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).has("Installments") &&
                            conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).get("Installments") instanceof JSONObject) {
                        if (conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).getJSONObject("Installments").get("Installment") instanceof JSONObject) {
                            JSONObject installmentSingle = conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).getJSONObject("Installments").getJSONObject("Installment");
                            array = new JSONArray().put(installmentSingle);
                        } else if (conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).getJSONObject("Installments").get("Installment") instanceof JSONArray) {
                            array = conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).getJSONObject("Installments").getJSONArray("Installment");
                        }
                        conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).remove("Installments");

                        //processar Chargebacks
                        for (int e = 0; e < array.length(); e++) {
                            JSONObject obj = array.getJSONObject(e);
                            JSONArray arrayCharge = new JSONArray();
                            if (obj.has("Chargebacks") &&
                                    !UteisValidacao.emptyString(obj.get("Chargebacks").toString())) {

                                if (obj.getJSONObject("Chargebacks").get("Chargeback") instanceof JSONObject) {
                                    JSONObject chargebackSingle = obj.getJSONObject("Chargebacks").getJSONObject("Chargeback");
                                    arrayCharge = new JSONArray().put(chargebackSingle);
                                } else if (obj.getJSONObject("Chargebacks").get("Chargeback") instanceof JSONArray) {
                                    arrayCharge = obj.getJSONObject("Chargebacks").getJSONArray("Chargeback");
                                }
                                obj.getJSONObject("Chargebacks").remove("Chargeback");
                                obj.getJSONObject("Chargebacks").put("Chargeback", arrayCharge);
                            }
                        }

                        conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).put("Installments", array);
                    } else {
                        conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).put("Installments", (Object) null);
                    }
                }

                processarJSONCancellations(conciliacaoJSON, strFinTransA);

            } else
                conciliacaoJSON.put(strFinTransA, (Object) null);

        }

        conciliacaoJSON = new JSONObject(conciliacaoJSON.toString().replace("\"ChargebackRefunds\":\"\"", "\"ChargebackRefunds\":null").
                replace("\"Chargebacks\":\"\"", "\"Chargebacks\":null")
                .replace("\"\",", "\"0\","));

        StoneConciliation stone;
        try {
            stone = new Gson().fromJson(conciliacaoJSON.toString(), StoneConciliation.class);
        } catch (Exception ex) {
            Uteis.logarDebug("ERRO Converter JSON StoneConciliation.class");
            Uteis.logarDebug("JSON: " + conciliacaoJSON.toString());
            Uteis.logarDebug("ERRO: " + ex.getMessage());
            throw ex;
        }

        // Esses valores do TipoConciliacao = 5, devem ser exibidos com a data da Solicitação do Cancelamento com filtro de Faturamento, na tela de Recebíveis > Conciliação
        // Essa mudança é devido a reclamação de clientes que exibia a Solicitação de Cancelamento e o Efetivado tudo como Compensação, dado uma impressão de Dado Duplicado.
        for (StoneConciliationFinancialTransaction positivoSolicitacaoCancelamento: stone.getStoneConciliationFinancialTransactions()) {
            if (positivoSolicitacaoCancelamento.getStoneConciliationCancellations() != null && positivoSolicitacaoCancelamento.getStoneConciliationCancellations().size() > 0) {
                String dataSolicitacaoCancelamento = positivoSolicitacaoCancelamento.getStoneConciliationCancellations().get(0).getCancellationDate();
                positivoSolicitacaoCancelamento.setCaptureLocalDateTime(dataSolicitacaoCancelamento);
            }
        }

        List<ExtratoDiarioItemVO> extratoDiarioItens = new ArrayList<>();

        boolean configuracaoConcilicarSemNumeroParcela = getFacade().getConfiguracaoSistema().isConciliarSemNumeroParcela();

        //Processamento de CAPTURA (VENDAS, Cancelamento, ChargeBack)
        for (StoneConciliationFinancialTransaction transaction : stone.getStoneConciliationFinancialTransactions()) {
            if (transaction.getStoneConciliationEvents().getTipoConciliacao() == TipoConciliacaoEnum.VENDAS || isCancelamentoOrChargeback(transaction)) {
                extratoDiarioItens.addAll(
                        processarTransaction(convenio, stone.getHeader().getStoneCode(), transaction, empresa, arquivoProcessamentoTemp, true, convenioPossuiVariasEmpresasConfiguradas, dataArquivo, configuracaoConcilicarSemNumeroParcela));
            }
        }


        //Processamento de (PAGAMENTOS ,Cancelamento, ChargeBack)
        for (StoneConciliationFinancialTransaction transaction : stone.getStoneConciliationFinancialTransactionAccounts()) {
            if (transaction.getStoneConciliationEvents().getTipoConciliacao() == TipoConciliacaoEnum.PAGAMENTOS || isCancelamentoOrChargeback(transaction)) {
                extratoDiarioItens.addAll(
                        processarTransaction(convenio, stone.getHeader().getStoneCode(), transaction, empresa, arquivoProcessamentoTemp, false, convenioPossuiVariasEmpresasConfiguradas, dataArquivo, configuracaoConcilicarSemNumeroParcela));
            }
        }

        lancarContaAPagarOuReceberAutomaticaFinanceiro(convenio, empresa, arquivoProcessamentoTemp, stone, dataArquivo);

        Uteis.logarDebug("Conciliação Stone - Capturou " + extratoDiarioItens.size());
        if (extratoDiarioItens.size() > 0) {
            getFacade().getExtratoDiarioItem().processarListaExtratoDiario(extratoDiarioItens, false, convenio);
        }
        try {
            preencherPessoaItemExtrato(extratoDiarioItens, getFacade().getCliente().getCon());
        } catch (Exception ignored) {}
    }

    private static boolean isCancelamentoOrChargeback(StoneConciliationFinancialTransaction transaction) {
        if (transaction.getStoneConciliationEvents().getTipoConciliacao() != null &&
                (transaction.getStoneConciliationEvents().getTipoConciliacao() == TipoConciliacaoEnum.CHARGEBACK ||
                        transaction.getStoneConciliationEvents().getTipoConciliacao() == TipoConciliacaoEnum.CANCELAMENTO ||
                        transaction.getStoneConciliationEvents().getTipoConciliacao() == TipoConciliacaoEnum.ESTORNO_CHARGEBACK ||
                        transaction.getStoneConciliationEvents().getTipoConciliacao() == TipoConciliacaoEnum.TAXA_CANCELAMENTO)) {
            return true;
        }
        return false;
    }

    private static void processarJSONCancellations(JSONObject conciliacaoJSON, String strFinTransA) {
        try {
            for (int i = 0; i < conciliacaoJSON.getJSONArray(strFinTransA).length(); i++) {
                JSONArray array = new JSONArray();
                if (conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).has("Cancellations") &&
                        conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).get("Cancellations") instanceof JSONObject) {
                    if (conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).getJSONObject("Cancellations").get("Cancellation") instanceof JSONObject) {
                        JSONObject installmentSingle = conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).getJSONObject("Cancellations").getJSONObject("Cancellation");
                        array = new JSONArray().put(installmentSingle);
                    } else if (conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).getJSONObject("Cancellations").get("Cancellation") instanceof JSONArray) {
                        array = conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).getJSONObject("Cancellations").getJSONArray("Cancellation");
                    }
                    conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).remove("Cancellations");
                    conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).put("Cancellations", array);
                } else {
                    conciliacaoJSON.getJSONArray(strFinTransA).getJSONObject(i).put("Cancellations", (Object) null);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void lancarContaAPagarOuReceberAutomaticaFinanceiro(ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresa,
                                                                       String arquivoProcessamentoTemp, StoneConciliation stone, Date dataArquivo) {
        try {
            if (!convenioCobrancaVO.isStone() &&
                    !convenioCobrancaVO.isStoneConnect()) {
                return;
            }
            ConfiguracaoFinanceiroVO configuracaoFinanceiroVO = getFacade().getConfiguracaoFinanceiro().consultar();
            if (!configuracaoFinanceiroVO.isCriarContaPagarAutomatico()) {
                return;
            }
            for (StoneConciliationFinancialTransaction transaction : stone.getStoneConciliationFinancialTransactionAccounts()) {
                List<ExtratoDiarioItemVO> listaCance = new ArrayList<>();
                if (transaction.getStoneConciliationEvents().getTipoConciliacaoArray().contains(TipoConciliacaoEnum.TAXA_CANCELAMENTO)) {
                    listaCance.addAll(processarTransactionAccountsCancellations(convenioCobrancaVO, stone.getHeader().getStoneCode(), transaction, empresa, arquivoProcessamentoTemp));
                }
                if (transaction.getStoneConciliationEvents().getTipoConciliacaoArray().contains(TipoConciliacaoEnum.CHARGEBACK)) {
                    listaCance.addAll(processarTransactionAccountsCancellationsChargebacks(convenioCobrancaVO, stone.getHeader().getStoneCode(), transaction, empresa, arquivoProcessamentoTemp));
                }
                if (transaction.getStoneConciliationEvents().getTipoConciliacaoArray().contains(TipoConciliacaoEnum.ESTORNO_CHARGEBACK)) {
                    listaCance.addAll(processarTransactionAccountsCancellationsEstornoChargebacks(convenioCobrancaVO, stone.getHeader().getStoneCode(), transaction, empresa, arquivoProcessamentoTemp));
                }
                if (!UteisValidacao.emptyList(listaCance)) {
                    for (ExtratoDiarioItemVO itemVO : listaCance) {
                        itemVO.setDataArquivo(dataArquivo);
                        getFacade().getExtratoDiarioItem().criarContaPagarOuReceberCancelamentoStone(convenioCobrancaVO, itemVO, empresa, configuracaoFinanceiroVO);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void preencherPessoaItemExtrato(List<ExtratoDiarioItemVO> extratos, Connection con) throws Exception {
        ExtratoDiarioItem extratoDiarioItemDAO;
        try {
            extratoDiarioItemDAO = new ExtratoDiarioItem(con);

            for (ExtratoDiarioItemVO extrato : extratos) {
                try {
                    //buscar por movpagamento que já existe no item do extrato
                    if (extrato.getMovPagamento() != null && extrato.getMovPagamento().getPessoa() != null) {
                        if (!UteisValidacao.emptyNumber(extrato.getMovPagamento().getPessoa().getCodigo())) {
                            extrato.getPessoa().setCodigo(extrato.getMovPagamento().getPessoa().getCodigo());
                        }
                    }
                    if (UteisValidacao.emptyNumber(extrato.getPessoa().getCodigo())) {
                        //buscar no sistema e nas transações a pessoa do extrato
                        extratoDiarioItemDAO.obterPessoaExtrato(extrato);
                    }
                    if (!UteisValidacao.emptyNumber(extrato.getPessoa().getCodigo())) {
                        extratoDiarioItemDAO.incluirInfoCodPessoa(extrato);
                    }
                } catch (Exception ignore) {
                    if (!UteisValidacao.emptyString(extrato.getAutorizacao()) && !UteisValidacao.emptyString(extrato.getAutorizacao())) {
                        Uteis.logarDebug("Não foi possível encontrar o aluno na base buscando pela autorização: " + extrato.getAutorizacao() + " e nem pelo nsu: " + extrato.getNsu());
                    } else if (!UteisValidacao.emptyString(extrato.getAutorizacao()) && UteisValidacao.emptyString(extrato.getAutorizacao())) {
                        Uteis.logarDebug("Não foi possível encontrar o aluno na base buscando pela autorização: " + extrato.getAutorizacao());
                    } else if (UteisValidacao.emptyString(extrato.getAutorizacao()) && !UteisValidacao.emptyString(extrato.getAutorizacao())) {
                        Uteis.logarDebug("Não foi possível encontrar o aluno na base buscando pelo nsu: " + extrato.getNsu());
                    }
                }
            }
        } finally {
            extratoDiarioItemDAO = null;
        }
    }

    private static List<ExtratoDiarioItemVO> processarTransaction(ConvenioCobrancaVO convenio, String stoneCode, StoneConciliationFinancialTransaction transaction, EmpresaVO empresa,
                                                                  String arquivoProcessamentoTemp, boolean financialTransaction, boolean convenioPossuiVariasEmpresasConfiguradas,
                                                                  Date dataArquivo, boolean isConciliarSemNumeroParcela) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat sdfYMD = new SimpleDateFormat("yyyyMMdd");
        Map<String, String> mapa = new HashMap();
        mapa.put("Conciliation.StoneConciliationFinancialTransactionAccounts.Transaction", new Gson().toJson(transaction));
        List<ExtratoDiarioItemVO> extratoDiarioItens = new ArrayList<>();
        ExtratoDiarioItemVO extratoDiarioItemVO;
        String dataPrevistaPagamento = "";
        if (transaction.getStoneConciliationEvents().getTipoConciliacao() == null)
            return new ArrayList<>();

        //**PROCESSAR INSTALLMENTS PRIMEIRO**
        if (transaction.getStoneConciliationInstallments() != null) {
            for (StoneConciliationInstallment stoneConciliationInstallment : transaction.getStoneConciliationInstallments()) {
                extratoDiarioItemVO = new ExtratoDiarioItemVO();
                extratoDiarioItemVO.setArquivo(arquivoProcessamentoTemp);
                extratoDiarioItemVO.setProps(mapa);
                extratoDiarioItemVO.setDataLancamento(sdf.parse(transaction.getCaptureLocalDateTime()));
                extratoDiarioItemVO.setTipoConciliacao(transaction.getStoneConciliationEvents().getTipoConciliacao().getCodigo());
                extratoDiarioItemVO.setNsu(transaction.getAcquirerTransactionKey());
                extratoDiarioItemVO.setAutorizacao(transaction.getIssuerAuthorizationCode());
                extratoDiarioItemVO.setEstabelecimento(stoneCode);
                extratoDiarioItemVO.setNrCartao(transaction.getCardNumber());
                extratoDiarioItemVO.setDataProcessamentoExtrato(Calendario.hoje());
                extratoDiarioItemVO.setNrParcela(stoneConciliationInstallment.getInstallmentNumber());
                extratoDiarioItemVO.setNrTotalParcelas(transaction.getNumberOfInstallments());
                extratoDiarioItemVO.setApresentarExtrato(true);
                extratoDiarioItemVO.setConvenio(convenio);
                extratoDiarioItemVO.setTipoConvenioCobrancaEnum(extratoDiarioItemVO.getConvenio().getTipo());
                extratoDiarioItemVO.setEmpresa(empresa);
                extratoDiarioItemVO.setEstorno(false);
                extratoDiarioItemVO.setRo(transaction.getAcquirerTransactionKey());
                extratoDiarioItemVO.setTipoParcelamento(transaction.getInstallmentType());

                if (transaction.getStoneConciliationEvents().getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS)) {
                    if (!UteisValidacao.emptyString(stoneConciliationInstallment.getAdvancedReceivableOriginalPaymentDate())) {
                        extratoDiarioItemVO.setDataPgtoOriginalAntesDaAntecipacao(sdfYMD.parse(stoneConciliationInstallment.getAdvancedReceivableOriginalPaymentDate()));
                        extratoDiarioItemVO.setAntecipacao(true);
                    }
                    extratoDiarioItemVO.setDataPrevistaPagamento(sdfYMD.parse(stoneConciliationInstallment.getPaymentDate()));
                } else {
                    if (UteisValidacao.emptyString(stoneConciliationInstallment.getPrevisionPaymentDate())) {
                        dataPrevistaPagamento = stoneConciliationInstallment.getOriginalPaymentDate();
                    } else {
                        dataPrevistaPagamento = stoneConciliationInstallment.getPrevisionPaymentDate();
                    }
                    try {
                        extratoDiarioItemVO.setDataPrevistaPagamento(sdfYMD.parse(dataPrevistaPagamento));
                    } catch (Exception ex) {}
                }

                if (extratoDiarioItemVO.getTipoConciliacao().equals(TipoConciliacaoEnum.VENDAS.getCodigo())) {
                    if (transaction.getAccountType() == StoneAccountTypeConciliationEnum.Credit.getId() || transaction.getAccountType() == StoneAccountTypeConciliationEnum.PrepaidCredit.getId()) {
                        extratoDiarioItemVO.setCredito(true);
                    }
                } else {
                    getFacade().getExtratoDiarioItem().verificarCreditoDebitoExtrato(extratoDiarioItemVO);
                }

                if (extratoDiarioItemVO.getTipoConciliacao().equals(TipoConciliacaoEnum.VENDAS.getCodigo())) {
                    getFacade().getExtratoDiarioItem().preencherMovPagamento(extratoDiarioItemVO);
                } else if (extratoDiarioItemVO.getCredito()) {
                    getFacade().getExtratoDiarioItem().preencherCartaoCredito(extratoDiarioItemVO, isConciliarSemNumeroParcela);
                } else if (!extratoDiarioItemVO.getCredito()) {
                    getFacade().getExtratoDiarioItem().preencherCartaoDebito(extratoDiarioItemVO);
                }

                try {
                    if (convenioPossuiVariasEmpresasConfiguradas && !UteisValidacao.emptyNumber(extratoDiarioItemVO.getCodigoMovPagamento())) {
                        //caso tenha várias empresas para o mesmo convênio de cobrança, será necessário usar a empresa do movpagamento para setar no item do extrato.
                        extratoDiarioItemVO.setEmpresa(getFacade().getMovPagamento().consultarPorChavePrimaria(extratoDiarioItemVO.getCodigoMovPagamento(), Uteis.NIVELMONTARDADOS_TELACONSULTA).getEmpresa());
                    }
                } catch (Exception ex) {}


                double valorBruto = stoneConciliationInstallment.getGrossAmount();
                double valorLiquido = stoneConciliationInstallment.getNetAmount();
                double valorComissao = valorBruto - valorLiquido;
                double taxa = 0.0;
                double taxaAntecipacao = 0.0;

                extratoDiarioItemVO.setValorBruto(valorBruto);
                extratoDiarioItemVO.setValorLiquido(valorLiquido);
                extratoDiarioItemVO.setValorComissao(valorComissao);

                if (extratoDiarioItemVO.isAntecipacao()) {
                    extratoDiarioItemVO.setValorDescontadoAntecipacao(stoneConciliationInstallment.getAdvanceRateAmount());  //valor em R$ descontado pela taxa de antecipação (double).
                    taxaAntecipacao = (extratoDiarioItemVO.getValorDescontadoAntecipacao() * 100) / valorBruto;   //calcular porcentagem da taxa de antecipação
                    extratoDiarioItemVO.setTaxaCalculadaAntecipacao(taxaAntecipacao);

                    taxa = ((valorComissao - extratoDiarioItemVO.getValorDescontadoAntecipacao()) * 100) / valorBruto;
                } else {
                    taxa = (valorComissao * 100) / valorBruto;
                }

                extratoDiarioItemVO.setTaxa(taxa);

                //para antecipação de recebíveis, mudar a data do recebimento no zw também automaticamente;
                if (extratoDiarioItemVO.isAntecipacao()) {
                    Uteis.logarDebug("Encontrei um pagamento que foi antecipado, vou mudar a data de pagamento dentro do ZW automaticamente..." +
                            (!UteisValidacao.emptyString(extratoDiarioItemVO.getAutorizacao()) ? " | AUT: " + extratoDiarioItemVO.getAutorizacao() : "") +
                            (!UteisValidacao.emptyString(extratoDiarioItemVO.getNsu()) ? " | NSU: " + extratoDiarioItemVO.getNsu() : ""));
                    try {
                        getFacade().getExtratoDiarioItem().alterarDatasPagamento(extratoDiarioItemVO);
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        try {
                            Uteis.logarDebug("Não foi possível mudar a data de pagamento dentro do ZW automaticamente... | " + transaction);
                        } catch (Exception ignored) {
                        }
                    }

                } else {
                    taxa = (valorComissao * 100) / valorBruto;
                }

                ExtratoDiarioItemVO extratoDiarioItemVOEstornoChargeBack = (ExtratoDiarioItemVO) extratoDiarioItemVO.getClone(true);
                extratoDiarioItens.add(extratoDiarioItemVO);

                //**PROCESSAR CHARGEBACK**
                if (stoneConciliationInstallment.getChargeBacks() != null && stoneConciliationInstallment.getChargeBacks().getChargeback() != null) {
                    for (StoneConciliationChargeBack chargeBack : stoneConciliationInstallment.getChargeBacks().getChargeback()) {
                        if (extratoDiarioItemVO.getDataPrevistaPagamento() == null) {
                            extratoDiarioItemVO.setDataPrevistaPagamento(sdfYMD.parse(chargeBack.getChargeDate()));
                        }
                        if ((Calendario.maiorOuIgual(sdfYMD.parse(chargeBack.getDate()), extratoDiarioItemVO.getDataPrevistaPagamento()) && !financialTransaction) ||
                                (Calendario.menorOuIgual(sdfYMD.parse(chargeBack.getDate()), extratoDiarioItemVO.getDataPrevistaPagamento()) && financialTransaction)) {
                            ExtratoDiarioItemVO extratoDiarioItemVO1 = (ExtratoDiarioItemVO) extratoDiarioItemVO.getClone(true);
                            extratoDiarioItemVO1.setTipoConciliacao(TipoConciliacaoEnum.CHARGEBACK.getCodigo());
                            extratoDiarioItemVO1.setValorBruto(chargeBack.getAmount() > 0 ? chargeBack.getAmount() * -1 : chargeBack.getAmount());
                            extratoDiarioItemVO1.setValorLiquido(extratoDiarioItemVO1.getValorBruto());
                            extratoDiarioItemVO1.setDataLancamento(sdfYMD.parse(chargeBack.getDate()));
                            extratoDiarioItemVO1.setDataPrevistaPagamento(sdfYMD.parse(chargeBack.getChargeDate()));
                            extratoDiarioItemVO1.setEstorno(true);
                            extratoDiarioItemVO1.setIdExterno(chargeBack.getId());
                            extratoDiarioItens.add(extratoDiarioItemVO1);
                        }
                    }
                }
                if (transaction.getStoneConciliationEvents().getTipoConciliacao() == TipoConciliacaoEnum.CHARGEBACK) {
                    extratoDiarioItens.remove(extratoDiarioItemVO);
                }

                //**PROCESSAR ESTORNO DE CHARGEBACK**
                if (
                        transaction.getStoneConciliationEvents().getTipoConciliacaoArray().contains(TipoConciliacaoEnum.ESTORNO_CHARGEBACK)
                                && extratoDiarioItemVOEstornoChargeBack != null
                                && ((extratoDiarioItemVOEstornoChargeBack.getTipoConciliacao().equals(TipoConciliacaoEnum.PAGAMENTOS.getCodigo())) ||
                                (extratoDiarioItemVOEstornoChargeBack.getTipoConciliacao().equals(TipoConciliacaoEnum.ESTORNO_CHARGEBACK.getCodigo())))
                ) {
                    montarExtratoDiarioItemEstornoChargebackPagamentos(extratoDiarioItens, stoneConciliationInstallment, extratoDiarioItemVOEstornoChargeBack);
                }
            }
        }

        try {
            //**PROCESSAR CANCELAMENTOS e TAXAS DE CANCELAMENTOS**
            if (!UteisValidacao.emptyList(transaction.getStoneConciliationCancellations())){
                extratoDiarioItens.addAll(processarTransactionCancellations(transaction, convenio, stoneCode, empresa, arquivoProcessamentoTemp, convenioPossuiVariasEmpresasConfiguradas, dataArquivo));
            }
        } catch (Exception ignore) {}

        return extratoDiarioItens;
    }

    private static void montarExtratoDiarioItemEstornoChargebackPagamentos(List<ExtratoDiarioItemVO> extratoDiarioItens, StoneConciliationInstallment stoneConciliationInstallment,
                                                                           ExtratoDiarioItemVO extratoDiarioItemVOEstornoChargeBack) throws ParseException {
        SimpleDateFormat sdfYMD = new SimpleDateFormat("yyyyMMdd");
        extratoDiarioItemVOEstornoChargeBack.setTipoConciliacao(TipoConciliacaoEnum.ESTORNO_CHARGEBACK.getCodigo());
        extratoDiarioItemVOEstornoChargeBack.setValorBruto(stoneConciliationInstallment.getChargebackRefunds().getChargebackRefund().getAmount());
        extratoDiarioItemVOEstornoChargeBack.setValorLiquido(stoneConciliationInstallment.getChargebackRefunds().getChargebackRefund().getAmount());
        extratoDiarioItemVOEstornoChargeBack.setValorComissao(0.0);
        extratoDiarioItemVOEstornoChargeBack.setTaxa(0.0);
        extratoDiarioItemVOEstornoChargeBack.setMovPagamento(new MovPagamentoVO());
        extratoDiarioItemVOEstornoChargeBack.setCodigoMovPagamento(0);
        extratoDiarioItemVOEstornoChargeBack.setCartao(new CartaoCreditoTO());
        extratoDiarioItemVOEstornoChargeBack.setCodigoCartaoCredito(0);
        extratoDiarioItemVOEstornoChargeBack.setAutorizacao("");
        extratoDiarioItemVOEstornoChargeBack.setSituacao(SituacaoItemExtratoEnum.ESTORNADO_SISTEMA);
        if (stoneConciliationInstallment.getChargebackRefunds() != null && stoneConciliationInstallment.getChargebackRefunds().getChargebackRefund() != null &&
                !UteisValidacao.emptyString(stoneConciliationInstallment.getChargebackRefunds().getChargebackRefund().getChargeDate())) {
            extratoDiarioItemVOEstornoChargeBack.setDataPrevistaPagamento(sdfYMD.parse(stoneConciliationInstallment.getChargebackRefunds().getChargebackRefund().getChargeDate()));
        }
        extratoDiarioItens.add(extratoDiarioItemVOEstornoChargeBack);
    }

    private static List<ExtratoDiarioItemVO> processarTransactionAccountsCancellations(ConvenioCobrancaVO convenio, String stoneCode,
                                                                                       StoneConciliationFinancialTransaction transaction, EmpresaVO empresa, String arquivoProcessamentoTemp) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        Map<String, String> mapa = new HashMap();
        mapa.put("Conciliation.StoneConciliationFinancialTransactionAccounts.Transaction", new Gson().toJson(transaction));
        List<ExtratoDiarioItemVO> extratoDiarioItens = new ArrayList<>();
        ExtratoDiarioItemVO extratoDiarioItemVO;
        if (transaction.getStoneConciliationEvents().getTipoConciliacao() == null)
            return new ArrayList<>();
        for (StoneConciliationCancellation stoneConciliationCancellation : transaction.getStoneConciliationCancellations()) {
            extratoDiarioItemVO = new ExtratoDiarioItemVO();
            extratoDiarioItemVO.setArquivo(arquivoProcessamentoTemp);
            extratoDiarioItemVO.setProps(mapa);
            extratoDiarioItemVO.setDataLancamento(sdf.parse(transaction.getCaptureLocalDateTime()));
            extratoDiarioItemVO.setDataCancelamento(sdf.parse(stoneConciliationCancellation.getCancellationDate()));
            extratoDiarioItemVO.setTipoConciliacao(TipoConciliacaoEnum.TAXA_CANCELAMENTO.getCodigo());
            extratoDiarioItemVO.setNsu(transaction.getAcquirerTransactionKey());
            extratoDiarioItemVO.setAutorizacao(transaction.getIssuerAuthorizationCode());
            extratoDiarioItemVO.setEstabelecimento(stoneCode);
            extratoDiarioItemVO.setNrCartao(transaction.getCardNumber());
            extratoDiarioItemVO.setDataProcessamentoExtrato(Calendario.hoje());
            try {
                extratoDiarioItemVO.setNrParcela(stoneConciliationCancellation.getInstallmentNumber());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            try {
                extratoDiarioItemVO.setIdExterno(stoneConciliationCancellation.getOperationKey());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            extratoDiarioItemVO.setNrTotalParcelas(0);
            extratoDiarioItemVO.setApresentarExtrato(false);
            extratoDiarioItemVO.setConvenio(convenio);
            extratoDiarioItemVO.setTipoConvenioCobrancaEnum(extratoDiarioItemVO.getConvenio().getTipo());
            extratoDiarioItemVO.setEmpresa(empresa);
            extratoDiarioItemVO.setEstorno(false);
            extratoDiarioItemVO.setRo(transaction.getAcquirerTransactionKey());
            extratoDiarioItemVO.setValorBruto(stoneConciliationCancellation.getReturnedAmount());

            extratoDiarioItens.add(extratoDiarioItemVO);
        }
        return extratoDiarioItens;
    }

    public static List<ExtratoDiarioItemVO> processarTransactionCancellations(StoneConciliationFinancialTransaction transaction, ConvenioCobrancaVO convenio, String stoneCode,
                                                                              EmpresaVO empresa, String arquivoProcessamentoTemp, boolean convenioPossuiVariasEmpresasConfiguradas, Date dataArquivo) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        Map<String, String> mapa = new HashMap();
        mapa.put("Conciliation.StoneConciliationFinancial.Transaction", new Gson().toJson(transaction));
        List<ExtratoDiarioItemVO> extratoDiarioItensCancelamento = new ArrayList<>();
        ExtratoDiarioItemVO extratoDiarioItemVO;
        if (!UteisValidacao.emptyList(transaction.getStoneConciliationCancellations())) {
            for (StoneConciliationCancellation stoneConciliationCancellation : transaction.getStoneConciliationCancellations()) {
                extratoDiarioItemVO = new ExtratoDiarioItemVO();
                extratoDiarioItemVO.setArquivo(arquivoProcessamentoTemp);
                extratoDiarioItemVO.setProps(mapa);
                extratoDiarioItemVO.setDataLancamento(sdf.parse(transaction.getCaptureLocalDateTime()));
                extratoDiarioItemVO.setDataCancelamento(sdf.parse(stoneConciliationCancellation.getCancellationDate()));
                extratoDiarioItemVO.setTipoConciliacao(transaction.getStoneConciliationEvents().getTipoCancelamento().getCodigo());
                extratoDiarioItemVO.setNsu(transaction.getAcquirerTransactionKey());
                extratoDiarioItemVO.setAutorizacao(transaction.getIssuerAuthorizationCode());
                extratoDiarioItemVO.setEstabelecimento(stoneCode);
                extratoDiarioItemVO.setNrCartao(transaction.getCardNumber());
                extratoDiarioItemVO.setDataProcessamentoExtrato(Calendario.hoje());
                extratoDiarioItemVO.setDataPrevistaPagamento(dataArquivo); //Usar sempre a data que está processando o arquivo aqui. Não usar a data do evento.
                extratoDiarioItemVO.setNrParcela(0);
                extratoDiarioItemVO.setNrTotalParcelas(0);
                extratoDiarioItemVO.setApresentarExtrato(true);
                extratoDiarioItemVO.setConvenio(convenio);
                extratoDiarioItemVO.setTipoConvenioCobrancaEnum(extratoDiarioItemVO.getConvenio().getTipo());
                extratoDiarioItemVO.setEmpresa(empresa);
                extratoDiarioItemVO.setEstorno(true);
                extratoDiarioItemVO.setRo(transaction.getAcquirerTransactionKey());
                extratoDiarioItemVO.setValorBruto(stoneConciliationCancellation.getReturnedAmount() * -1);
                extratoDiarioItemVO.setCredito(true); //TODO Implementar regra para descobrir se é crédito ou débito futuramente

                getFacade().getExtratoDiarioItem().preencherMovPagamento(extratoDiarioItemVO);

                try {
                    if (convenioPossuiVariasEmpresasConfiguradas && !UteisValidacao.emptyNumber(extratoDiarioItemVO.getCodigoMovPagamento())) {
                        //caso tenha várias empresas para o mesmo convênio de cobrança, será necessário usar a empresa do movpagamento para setar no item do extrato.
                        extratoDiarioItemVO.setEmpresa(getFacade().getMovPagamento().consultarPorChavePrimaria(extratoDiarioItemVO.getCodigoMovPagamento(), Uteis.NIVELMONTARDADOS_TELACONSULTA).getEmpresa());
                    }
                } catch (Exception ex) {}

                extratoDiarioItensCancelamento.add(extratoDiarioItemVO);
            }
        }
        return extratoDiarioItensCancelamento;
    }

    private static List<ExtratoDiarioItemVO> processarTransactionAccountsCancellationsChargebacks(ConvenioCobrancaVO convenio, String stoneCode,
                                                                                       StoneConciliationFinancialTransaction transaction, EmpresaVO empresa, String arquivoProcessamentoTemp) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMdd");
        Map<String, String> mapa = new HashMap();
        mapa.put("Conciliation.StoneConciliationFinancialTransactionAccounts.Transaction", new Gson().toJson(transaction));
        List<ExtratoDiarioItemVO> extratoDiarioItens = new ArrayList<>();
        ExtratoDiarioItemVO extratoDiarioItemVO;
        if (transaction.getStoneConciliationEvents().getTipoConciliacao() == null)
            return new ArrayList<>();
        for (StoneConciliationInstallment stoneConciliationInstallment : transaction.getStoneConciliationInstallments()) {
            if (stoneConciliationInstallment.getChargeBacks() != null && stoneConciliationInstallment.getChargeBacks().getChargeback() != null) {
                for (StoneConciliationChargeBack chargeBack : stoneConciliationInstallment.getChargeBacks().getChargeback()) {

                    extratoDiarioItemVO = new ExtratoDiarioItemVO();
                    extratoDiarioItemVO.setArquivo(arquivoProcessamentoTemp);
                    extratoDiarioItemVO.setProps(mapa);
                    extratoDiarioItemVO.setDataLancamento(sdf.parse(transaction.getCaptureLocalDateTime()));
                    extratoDiarioItemVO.setDataCancelamento(sdf2.parse(chargeBack.getDate()));
                    extratoDiarioItemVO.setTipoConciliacao(TipoConciliacaoEnum.CHARGEBACK.getCodigo());
                    extratoDiarioItemVO.setNsu(transaction.getAcquirerTransactionKey());
                    extratoDiarioItemVO.setAutorizacao(transaction.getIssuerAuthorizationCode());
                    extratoDiarioItemVO.setEstabelecimento(stoneCode);
                    extratoDiarioItemVO.setNrCartao(transaction.getCardNumber());
                    extratoDiarioItemVO.setDataProcessamentoExtrato(Calendario.hoje());
                    try {
                        extratoDiarioItemVO.setNrParcela(stoneConciliationInstallment.getInstallmentNumber());
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                    extratoDiarioItemVO.setNrTotalParcelas(0);
                    extratoDiarioItemVO.setApresentarExtrato(false);
                    extratoDiarioItemVO.setConvenio(convenio);
                    extratoDiarioItemVO.setTipoConvenioCobrancaEnum(extratoDiarioItemVO.getConvenio().getTipo());
                    extratoDiarioItemVO.setEmpresa(empresa);
                    extratoDiarioItemVO.setEstorno(false);
                    extratoDiarioItemVO.setRo(transaction.getAcquirerTransactionKey());
                    extratoDiarioItemVO.setValorBruto(chargeBack.getAmount());
                    extratoDiarioItemVO.setIdExterno(chargeBack.getId());

                    extratoDiarioItens.add(extratoDiarioItemVO);
                }
            }
        }
        return extratoDiarioItens;
    }

    private static List<ExtratoDiarioItemVO> processarTransactionAccountsCancellationsEstornoChargebacks(ConvenioCobrancaVO convenio, String stoneCode,
                                                                                                  StoneConciliationFinancialTransaction transaction, EmpresaVO empresa, String arquivoProcessamentoTemp) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMdd");
        Map<String, String> mapa = new HashMap();
        mapa.put("Conciliation.StoneConciliationFinancialTransactionAccounts.Transaction", new Gson().toJson(transaction));
        List<ExtratoDiarioItemVO> extratoDiarioItens = new ArrayList<>();
        ExtratoDiarioItemVO extratoDiarioItemVO;
        if (transaction.getStoneConciliationEvents().getTipoConciliacao() == null)
            return new ArrayList<>();
        for (StoneConciliationInstallment stoneConciliationInstallment : transaction.getStoneConciliationInstallments()) {
            if (stoneConciliationInstallment.getChargebackRefunds() != null &&
                    stoneConciliationInstallment.getChargebackRefunds().getChargebackRefund() != null) {

                extratoDiarioItemVO = new ExtratoDiarioItemVO();
                extratoDiarioItemVO.setArquivo(arquivoProcessamentoTemp);
                extratoDiarioItemVO.setProps(mapa);
                extratoDiarioItemVO.setDataLancamento(sdf.parse(transaction.getCaptureLocalDateTime()));
                extratoDiarioItemVO.setDataCancelamento(sdf2.parse(stoneConciliationInstallment.getChargebackRefunds().getChargebackRefund().getDate()));
                extratoDiarioItemVO.setTipoConciliacao(TipoConciliacaoEnum.ESTORNO_CHARGEBACK.getCodigo());
                extratoDiarioItemVO.setNsu(transaction.getAcquirerTransactionKey());
                extratoDiarioItemVO.setAutorizacao(transaction.getIssuerAuthorizationCode());
                extratoDiarioItemVO.setEstabelecimento(stoneCode);
                extratoDiarioItemVO.setNrCartao(transaction.getCardNumber());
                extratoDiarioItemVO.setDataProcessamentoExtrato(Calendario.hoje());
                try {
                    extratoDiarioItemVO.setIdExterno(stoneConciliationInstallment.getChargebackRefunds().getChargebackRefund().getId());
                } catch (Exception ignored) {}
                try {
                    extratoDiarioItemVO.setNrParcela(stoneConciliationInstallment.getInstallmentNumber());
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                extratoDiarioItemVO.setNrTotalParcelas(0);
                extratoDiarioItemVO.setApresentarExtrato(false);
                extratoDiarioItemVO.setConvenio(convenio);
                extratoDiarioItemVO.setTipoConvenioCobrancaEnum(extratoDiarioItemVO.getConvenio().getTipo());
                extratoDiarioItemVO.setEmpresa(empresa);
                extratoDiarioItemVO.setEstorno(false);
                extratoDiarioItemVO.setRo(transaction.getAcquirerTransactionKey());
                extratoDiarioItemVO.setValorBruto(stoneConciliationInstallment.getChargebackRefunds().getChargebackRefund().getAmount());

                extratoDiarioItens.add(extratoDiarioItemVO);
            }
        }
        return extratoDiarioItens;
    }

    private static String urlRequisicaoV1(String stoneCode, Date dataTransacao) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String endPoint = stoneCode + "/conciliation-file/" + sdf.format(dataTransacao);
        return URL_API_STONE_V1 + endPoint;
    }

    private static String urlRequisicaoV2(String stoneCode, Date dataTransacao) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String endPoint = sdf.format(dataTransacao) + "?affiliationCode=" + stoneCode;
        return URL_API_STONE_V2 + endPoint;
    }

    /**
     * Cabeçalho com as credenciais de acesso a API Stone Conciliação.
     *
     * @return Header para integração com a API Stone Conciliação.
     * @throws Exception
     */
    private static Map<String, String> header() throws Exception {
        String xAuthorizationEncryptedData = encryptSha512Hmac(CLIENT_ENCRYPTION_STRING, SECRET_KEY); // X-Authorization-Encrypted-Data
        Map<String, String> headersMap = new HashMap<String, String>();
        headersMap.put("Content-Type", "application/xml");
        headersMap.put("Authorization", CLIENT_APPLICATION_KEY);
        headersMap.put("X-Authorization-Raw-Data", CLIENT_ENCRYPTION_STRING);
        headersMap.put("X-Authorization-Encrypted-Data", xAuthorizationEncryptedData);
        return headersMap;
    }

    public static String concessaoAcesso(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        try {

            if (UteisValidacao.emptyString(convenioCobrancaVO.getCodigoAutenticacao01())) {
                throw new Exception("StoneCode não informado.");
            }

            String url = "https://conciliation.stone.com.br/v1/merchant/" + convenioCobrancaVO.getCodigoAutenticacao01() + "/access-authorization";

            RequestHttpService service = new RequestHttpService();
            service.connectTimeout = 10000;
            service.connectionRequestTimeout = 120000;
            RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, header(), null, null, MetodoHttpEnum.PUT);
            Uteis.logarDebug("Solicitou Concessão acesso conciliação Stone...");
            Uteis.logarDebug("URL: " + url);
            Uteis.logarDebug("Resposta da Stone: " + new JSONObject(respostaHttpDTO).toString());
            if (respostaHttpDTO.getHttpStatus().equals(200)) {
                return "A empresa Pacto Soluções, já está autorizada a acessar os dados de conciliação " +
                        "do lojista identificado pelo StoneCode: " + convenioCobrancaVO.getCodigoAutenticacao01() + ".";
            } else if (respostaHttpDTO.getHttpStatus().equals(202)) {
                return "Foi enviado um e-mail de solicitação para o lojista para que ele conceda o acesso.";
            } else if (respostaHttpDTO.getHttpStatus().equals(400)) {
                throw new Exception("Erro nos dados informados para a API.");
            } else if (respostaHttpDTO.getHttpStatus().equals(404)) {
                throw new Exception("Erro no formato da URL ou no método enviado.");
            } else if (respostaHttpDTO.getHttpStatus().equals(500)) {
                throw new Exception("Ocorreu um erro interno na API, por favor nos informe.");
            } else if (respostaHttpDTO.getHttpStatus().equals(503)) {
                throw new Exception("Um dos sistemas da Stone falhou, por favor nos informe.");
            } else if (respostaHttpDTO.getHttpStatus().equals(504)) {
                throw new Exception("Ocorreu um timeout e não foi possível concluir o trabalho, tente novamente.");
            } else {
                throw new Exception(respostaHttpDTO.getResponse());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro ao solicitar concessão de acesso. Retorno Stone: " + ex.getMessage());
        }
    }

    public static String verificarStatusConcessaoAcesso(ConvenioCobrancaVO convenioCobrancaVO) throws Exception {
        try {
            //Esse método apenas faz uma requisição de consulta da conciliação com a data de ontem e vê se vai dar o retorno que não tem permissão
            String url = urlRequisicaoV1(convenioCobrancaVO.getCodigoAutenticacao01(), Calendario.ontem());
            Map<String, String> header = header();

            Uteis.logarDebug("StoneConciliation... | URL: " + url + " | Header: " + header);

            String result = ExecuteRequestHttpService.executeHttpRequest(url, null,
                    header, ExecuteRequestHttpService.METODO_GET,
                    Charsets.UTF_8.name());

            JSONObject jsonObject = XML.toJSONObject(result);
            if (jsonObject.optJSONObject("Error") != null) {
                if (!UteisValidacao.emptyString(jsonObject.optJSONObject("Error").optString("Message"))) {
                    return jsonObject.optJSONObject("Error").optString("Message");
                }
            }
            if (jsonObject.optJSONObject("Conciliation") != null) {
                return "liberado";
            }
            return "";
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new Exception("Erro ao solicitar concessão de acesso. Retorno Stone: " + ex.getMessage());
        }
    }

    private static void notificarErro(ConvenioCobrancaVO convenioCobrancaVO, EmpresaVO empresaVO,
                                      Exception exception, Connection con) {

        ZillyonWebFacade zillyonWebFacadeDAO;
        Usuario usuarioDAO;
        try {
            usuarioDAO = new Usuario(con);
            zillyonWebFacadeDAO = new ZillyonWebFacade(con);

            String chave = DAO.resolveKeyFromConnection(con);

            try {
                UsuarioVO usuarioVO = usuarioDAO.getUsuarioRecorrencia();
                zillyonWebFacadeDAO.notificarRecursoSistema(chave, RecursoSistema.ERRO_EXTRATO_STONE, usuarioVO, empresaVO);
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            String mensagem = exception.getMessage();
            try {
                StringBuilder result = new StringBuilder(exception.toString() + "\n");
                StackTraceElement[] trace = exception.getStackTrace();
                for (StackTraceElement stackTraceElement : trace) {
                    result.append(stackTraceElement.toString()).append("\n");
                }
                mensagem = result.toString();
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            UteisEmail email = new UteisEmail();
            ConfiguracaoSistemaCRMVO config = SuperControle.getConfiguracaoSMTPNoReply();
            String assunto = "Erro processar Stone: " + convenioCobrancaVO.getCodigo() + " - " + convenioCobrancaVO.getDescricao() + " | Chave " + chave;
            email.novo(assunto, config);

            String[] emailEnviar = new String[]{"<EMAIL>"};

            StringBuilder emailTexto = new StringBuilder();
            emailTexto.append("<h2>Erro ao processar extrato Stone </h2>");
            emailTexto.append("<h3>Chave: ").append(chave).append("</h3>");
            emailTexto.append("<h3></h3>");
            emailTexto.append("<h3>Convênio: </h3>");
            emailTexto.append("<h3>Código: ").append(convenioCobrancaVO.getCodigo()).append(" </h3>");
            emailTexto.append("<h3>Descrição: ").append(convenioCobrancaVO.getDescricao()).append(" </h3>");
            emailTexto.append("<h3>StoneCode: ").append(convenioCobrancaVO.getCodigoAutenticacao01()).append(" </h3>");
            emailTexto.append("<h2></h2>");
            emailTexto.append("<h2>Erro: </h2>");
            emailTexto.append("<h2></h2>");
            emailTexto.append("<p> - ").append(mensagem).append("</p>");

            email.enviarEmailN(emailEnviar, emailTexto.toString(), assunto, "");
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logar(null, "Erro notificarErro: " + ex.getMessage());
        } finally {
            zillyonWebFacadeDAO = null;
            usuarioDAO = null;
        }
    }

    private static DetalhesRequestEnviadaVO montarObjDetalhesRequest(String endpoint, String nomeTabelaForeignKey, String response, String origem) {
        DetalhesRequestEnviadaVO obj = new DetalhesRequestEnviadaVO();
        obj.setDataRegistro(Calendario.hoje());
        obj.setUrl(endpoint);
        obj.setTempoRequisicaoMs(0);
        obj.setSucesso(true);
        obj.setNomeTabelaForeignKey(nomeTabelaForeignKey);
        obj.setCodigoTabelaForeignKey(0);
        obj.setResponse(response);
        obj.setOrigem(origem);
        return obj;
    }

}
