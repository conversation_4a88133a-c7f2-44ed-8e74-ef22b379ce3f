package servicos.impl.stone.connect;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DadosPedidoDTO extends SuperTO {

    private List<Integer> parcelas;
    private Integer formapagamento;
    private Double multaJuros;
    private String tipo; //crédito ou débito

    public List<Integer> getParcelas() {
        if (parcelas == null) {
            parcelas = new ArrayList<>();
        }
        return parcelas;
    }

    public void setParcelas(List<Integer> parcelas) {
        this.parcelas = parcelas;
    }

    public Integer getFormapagamento() {
        if (formapagamento == null) {
            formapagamento = 0;
        }
        return formapagamento;
    }

    public void setFormapagamento(Integer formapagamento) {
        this.formapagamento = formapagamento;
    }

    public String toString() {
        return new JSONObject(this).toString();
    }

    public Double getMultaJuros() {
        if (multaJuros == null) {
            multaJuros = 0.0;
        }
        return multaJuros;
    }

    public void setMultaJuros(Double multaJuros) {
        this.multaJuros = multaJuros;
    }

    public String getTipo() {
        if (tipo == null) {
            tipo = "";
        }
        return tipo;
    }

    public void setTipo(String tipo) {
        this.tipo = tipo;
    }
}
