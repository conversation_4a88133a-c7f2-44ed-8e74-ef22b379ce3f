package servicos.impl.stone.connect;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import negocio.comuns.financeiro.enumerador.OperadorasExternasAprovaFacilEnum;
import negocio.comuns.financeiro.enumerador.StatusPinpadEnum;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PedidoRetornoDTO extends SuperTO {

    private StatusPinpadEnum status;
    private String numeroUnicoTransacao;
    private String nsu;
    private String autorizacao;
    private String bandeira;
    private String dados;

    public StatusPinpadEnum getStatus() {
        return status;
    }

    public void setStatus(StatusPinpadEnum status) {
        this.status = status;
    }

    public String getNsu() {
        return nsu;
    }

    public void setNsu(String nsu) {
        this.nsu = nsu;
    }

    public String getAutorizacao() {
        return autorizacao;
    }

    public void setAutorizacao(String autorizacao) {
        this.autorizacao = autorizacao;
    }

    public OperadorasExternasAprovaFacilEnum getOperadora() {
        if (this.getBandeira() == null) {
            return null;
        }
        return OperadorasExternasAprovaFacilEnum.obterPorDescricao(this.getBandeira());
    }

    public String getBandeira() {
        return bandeira;
    }

    public void setBandeira(String bandeira) {
        this.bandeira = bandeira;
    }

    public String getDados() {
        return dados;
    }

    public void setDados(String dados) {
        this.dados = dados;
    }

    public String getNumeroUnicoTransacao() {
        return numeroUnicoTransacao;
    }

    public void setNumeroUnicoTransacao(String numeroUnicoTransacao) {
        this.numeroUnicoTransacao = numeroUnicoTransacao;
    }
}
