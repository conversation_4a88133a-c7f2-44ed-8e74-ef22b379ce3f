package servicos.impl.stone.connect;

import controle.financeiro.PinpadTO;
import negocio.comuns.arquitetura.UsuarioVO;
import negocio.comuns.basico.EmpresaVO;
import negocio.comuns.basico.PessoaVO;
import negocio.comuns.financeiro.*;
import negocio.comuns.financeiro.enumerador.OpcoesPinpadEnum;
import negocio.comuns.financeiro.enumerador.OrigemCobrancaEnum;
import negocio.comuns.financeiro.enumerador.StatusPinpadEnum;
import negocio.comuns.financeiro.enumerador.TipoFormaPagto;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.PinPad;
import negocio.facade.jdbc.financeiro.PinPadPedido;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.propriedades.PropsService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class StoneConnectService {

    private final static String URL_PEDIDO = (PropsService.getPropertyValue(PropsService.urlApiStoneConnect) + "/orders");
    private final static String URL_COBRANCA = (PropsService.getPropertyValue(PropsService.urlApiStoneConnect) + "/charges");

    public static PinPadPedidoVO gerarPedido(String key, TipoFormaPagto tipoFormaPagto, PessoaVO pessoaVO,
                                             EmpresaVO empresaVO, PinPadVO pinPadVO, PinpadTO pinpadTO,
                                             FormaPagamentoVO formaPagamentoVO, OrigemCobrancaEnum origemCobrancaEnum,
                                             UsuarioVO usuarioVO, List<MovParcelaVO> listaParcelas, Connection con) throws Exception {

        //Stone Connect cobrando valor errado. Quando descobrir de onde vem o problema, apagar esses logs.
        if (pinpadTO.getPinpadEnum().equals(OpcoesPinpadEnum.STONE_CONNECT)) {
            Uteis.logarDebug("Stone Connect - StoneConnectService.gerarPedido - pinpadTO.valorPinpad: " + pinpadTO.getValorPinpad());

            Double somaValorParcelas = 0.0;
            if (!UteisValidacao.emptyList(listaParcelas)) {
                for (MovParcelaVO movParcelaVO: listaParcelas) {
                    somaValorParcelas += movParcelaVO.getValorParcela();
                }
            }
            Uteis.logarDebug("Stone Connect - StoneConnectService.gerarPedido - listaParcelas.totalSomaValorParcelas: " + somaValorParcelas);
        }

        PinPadPedido pinPadPedidoDAO = null;
        RequestHttpService service;
        PinPadPedidoVO pinPadPedidoVO = new PinPadPedidoVO();
        try {
            pinPadPedidoDAO = new PinPadPedido(con);
            service = new RequestHttpService();

            pinPadPedidoVO = pinPadPedidoDAO.criarPedido(pessoaVO, pinpadTO.getValorPinpad(), pinpadTO.getPinpadEnum(),
                    empresaVO, pinPadVO.getConvenioCobranca(), pinPadVO.getPdvPinpad(), formaPagamentoVO, origemCobrancaEnum,
                    usuarioVO, listaParcelas);

            JSONObject metadata = new JSONObject();
            metadata.put("key", key);
            metadata.put("pedido", pinPadPedidoVO.getCodigo());
            metadata.put("empresa", empresaVO.getCodigo());

            PedidoDTO pedidoDTO = new PedidoDTO();
            pedidoDTO.setClosed(false);
            pedidoDTO.setMetadata(metadata);

            ItemsDTO itemsDTO = new ItemsDTO();
            itemsDTO.setDescription("PAGAMENTO - " + empresaVO.getNome());
            itemsDTO.setQuantity(1);

            int amount = getAmount(pinpadTO);
            itemsDTO.setAmount(amount);

            pedidoDTO.setItems(new ArrayList<>());
            pedidoDTO.getItems().add(itemsDTO);

            CustomerDTO customerDTO = new CustomerDTO();
            customerDTO.setName(pessoaVO.getNome());
            pedidoDTO.setCustomer(customerDTO);

            PaymentSetupDTO setupDTO = new PaymentSetupDTO();
            setupDTO.setType(tipoFormaPagto.equals(TipoFormaPagto.CARTAOCREDITO) ? "credit" : "debit"); //Valores possíveis: ("debit", "credit", "voucher").
            setupDTO.setInstallments(pinpadTO.getNrParcelas());

//            MCHT = LOJISTA
//            ISSR = EMISSOR
//            merchant: o custo do parcelamento fica sob responsabilidade do estabelecimento. É o tipo mais comum de parcelamento.
//            issuer: o custo do parcelamento é definido pelo emissor do cartão do portador. Para mais informações, sobre juros emissor.
            if (pinPadVO.getConvenioCobranca().getTipoParcelamentoStone().equalsIgnoreCase("MCHT")) {
                setupDTO.setInstallment_type("merchant");//"merchant", "issuer"
            } else if (pinPadVO.getConvenioCobranca().getTipoParcelamentoStone().equalsIgnoreCase("ISSR")) {
                setupDTO.setInstallment_type("issuer");//"merchant", "issuer"
            }

            PaymentSettingsDTO settingsDTO = new PaymentSettingsDTO();
            settingsDTO.setVisible(true);
            settingsDTO.setDisplay_name(pessoaVO.getNome().length() > 17 ? pessoaVO.getNome().substring(0, 17) : pessoaVO.getNome()); //(MÁX: 17 caracteres)
            settingsDTO.setDevices_serial_number(new ArrayList<>());
            settingsDTO.getDevices_serial_number().add(pinPadVO.getPdvPinpad());
            settingsDTO.setPayment_setup(setupDTO);
            pedidoDTO.setPoi_payment_settings(settingsDTO);

            pinPadPedidoVO.setParamsEnvio(new JSONObject(pedidoDTO).toString());

            String resposta = "";
            RespostaHttpDTO respostaHttpDTO = null;
            try {
                respostaHttpDTO = service.executeRequest(URL_PEDIDO, obterHeaders(pinPadVO.getConvenioCobranca(), con), null, pinPadPedidoVO.getParamsEnvio(), MetodoHttpEnum.POST);
                resposta = respostaHttpDTO != null ? new JSONObject(respostaHttpDTO).toString() : "";

                if (respostaHttpDTO != null && respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
                    JSONObject jsonResp = new JSONObject(respostaHttpDTO.getResponse());
                    pinPadPedidoVO.setIdExterno(jsonResp.getString("id"));
                    pinPadPedidoVO.setStatus(StatusPinpadEnum.AGUARDANDO);
                    resposta = jsonResp.toString();
                } else {
                    String msgApresentar = "";
                    try {
                        msgApresentar = (respostaHttpDTO != null ? respostaHttpDTO.getResponse() : "Erro desconhecido");
                        if (respostaHttpDTO != null) {
                            JSONObject jsonE = new JSONObject(respostaHttpDTO.getResponse());
                            msgApresentar = tratarMensagensErro(jsonE.getString("message"));
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }
                    throw new Exception(msgApresentar);
                }

            } catch (Exception ex) {
                resposta = respostaHttpDTO != null ? new JSONObject(respostaHttpDTO).toString() : ex.getMessage();
                throw ex;
            } finally {
                pinPadPedidoVO.setParamsResp(resposta);
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            pinPadPedidoVO.setStatus(StatusPinpadEnum.FALHA);
            pinPadPedidoVO.setMsg(ex.getMessage());
        } finally {
            if (pinPadPedidoDAO != null) {
                if (UteisValidacao.emptyNumber(pinPadPedidoVO.getCodigo())) {
                    pinPadPedidoDAO.incluir(pinPadPedidoVO);
                } else {
                    pinPadPedidoDAO.alterar(pinPadPedidoVO);
                }
            }
            pinPadPedidoDAO = null;
            service = null;
        }
        return pinPadPedidoVO;
    }

    private static int getAmount(PinpadTO pinpadTO) {
        // Processo de converter Double em BigDecimal para evitar problemas de diferença de 0,01
        // 1) Cria BigDecimal a partir do double de forma segura
        BigDecimal valorBD = BigDecimal.valueOf(pinpadTO.getValorPinpad());
        // 2) Multiplica por 100 e arredonda para 0 casas decimais, HALF_UP
        BigDecimal centsBD = valorBD
                .multiply(BigDecimal.valueOf(100))
                .setScale(0, RoundingMode.HALF_UP);
        // 3) Converte para int
        int amount = centsBD.intValueExact();
        return amount;
    }

    private static String tratarMensagensErro(String mensagem) {
        if (!UteisValidacao.emptyString(mensagem)) {
            if (mensagem.equals("Authorization has been denied for this request.")) {
                return "Autorização negada! É provável que a \"Chave secreta\" informada lá no convênio de cobrança esteja incorreta.";
            }
            if (mensagem.equals("Open Orders is disabled")) {
                return "Entre em contato com a Stone e solicite a liberação do \"Pedido em Aberto\" e também do Checkout";
            }
        }
        return mensagem;
    }
    private static Map<String, String> obterHeaders(ConvenioCobrancaVO convenioCobrancaVO, Connection con) throws Exception {
        String secretKey = obterSecretKey(convenioCobrancaVO, con);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Basic " + new String(new Base64().encode((secretKey + ":").getBytes())));
        return headers;
    }

    private static String obterPedido(PinPadPedidoVO obj, Connection con) throws Exception {
        String url = URL_PEDIDO + "/" + obj.getIdExterno();
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, obterHeaders(obj.getConvenioCobrancaVO(), con), null, null, MetodoHttpEnum.GET);
        if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
            throw new Exception(respostaHttpDTO.getResponse());
        }
        return respostaHttpDTO.getResponse();
    }

    public static PedidoRetornoDTO obterPedidoRetornoDTO(PinPadPedidoVO obj, Connection con) {
        PedidoRetornoDTO dto = new PedidoRetornoDTO();
        PinPadPedido pinPadPedidoDAO = null;
        try {
            pinPadPedidoDAO = new PinPadPedido(con);
            dto.setStatus(obj.getStatus());

            String dados = obterPedido(obj, con);

            pinPadPedidoDAO.gravarRetornoPagamentoStoneConnect(dados, obj);

            dto.setDados(dados);
            JSONObject jsonObject = new JSONObject(dados);
            StatusPinpadEnum statusAtual = obterStatusPinpadEnum(jsonObject);
            if (statusAtual != null) {
                if (statusAtual.equals(StatusPinpadEnum.PAGO)) {
                    JSONArray jsonArray = jsonObject.optJSONArray("charges");
                    JSONObject jsonCharge = jsonArray.getJSONObject(0);
                    JSONObject jsonMetaData = jsonCharge.getJSONObject("metadata");
                    String scheme_name = jsonMetaData.optString("scheme_name");
                    String authorization_code = jsonMetaData.optString("authorization_code");
                    String nsu = jsonCharge.optString("code");
                    dto.setAutorizacao(authorization_code);
                    dto.setNsu(nsu);
                    dto.setBandeira(scheme_name);
                    dto.setStatus(statusAtual);
                } else if (statusAtual.equals(StatusPinpadEnum.AGUARDANDO)) {
                    JSONArray jsonArray = jsonObject.optJSONArray("charges");
                    JSONObject jsonCharge = jsonArray != null ? jsonArray.getJSONObject(0) : null;
                    if (jsonCharge != null &&
                            jsonCharge.optString("status").equalsIgnoreCase("paid")) {
                        //foi pago então vamos fechar o pedido
                        fecharPedido(obj, StatusPinpadEnum.PAGO, false, false, con);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            pinPadPedidoDAO = null;
        }
        return dto;
    }

    public static String fecharPedido(PinPadPedidoVO obj, StatusPinpadEnum statusPinpadEnum,
                                      boolean buscarStatusStone, boolean alterarStatus, Connection con) throws Exception {
        String retorno = "";

        Map<String, String> headers = obterHeaders(obj.getConvenioCobrancaVO(), con);
        String body = null;
        if (statusPinpadEnum != null &&
                (statusPinpadEnum.equals(StatusPinpadEnum.FALHA) ||
                        statusPinpadEnum.equals(StatusPinpadEnum.CANCELADO))) {
            headers.put("Content-Type", "application/x-www-form-urlencoded");
            if (statusPinpadEnum.equals(StatusPinpadEnum.CANCELADO)) {
                body = "status=canceled"; //paid, canceled ou failed
            } else {
                body = "status=failed"; //paid, canceled ou failed
            }
        }

        if (UteisValidacao.emptyString(obj.getIdExterno())) {
            throw new Exception("Não existe pedido criado para cancelar!");
        }

        String url = URL_PEDIDO + "/" + obj.getIdExterno() + "/closed";
        RequestHttpService service = new RequestHttpService();
        RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, headers, null, body, MetodoHttpEnum.PATCH);

        if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK) && (!respostaHttpDTO.getResponse().contains("This order is closed.") &&
                !respostaHttpDTO.getResponse().contains("Order not found."))) {
            String msgApresentar = respostaHttpDTO.getResponse();
            try {
                JSONObject jsonE = new JSONObject(respostaHttpDTO.getResponse());
                if (!UteisValidacao.emptyString(jsonE.optJSONObject("error").optString("message"))) {
                    msgApresentar = jsonE.optJSONObject("error").optString("message");
                } else {
                    msgApresentar = jsonE.getString("message");
                }
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            throw new Exception(msgApresentar);
        }

        if (alterarStatus) {
            PinPadPedido pinPadPedidoDAO = new PinPadPedido(con);
            obj.setStatus(statusPinpadEnum != null ? statusPinpadEnum : StatusPinpadEnum.FINALIZADO);
            pinPadPedidoDAO.alterarStatus(obj);
            pinPadPedidoDAO = null;
        }
        return retorno;
    }

    private static StatusPinpadEnum obterStatusPedidoAtual(PinPadPedidoVO obj, Connection con) throws Exception {
        String dados = obterPedido(obj, con);
        JSONObject json = new JSONObject(dados);
        return obterStatusPinpadEnum(json);
    }

    private static StatusPinpadEnum obterStatusPinpadEnum(JSONObject json) throws Exception {
        String status = json.optString("status"); //paid, canceled ou failed
        if (status.equalsIgnoreCase("canceled")) {
            return StatusPinpadEnum.CANCELADO;
        } else if (status.equalsIgnoreCase("paid")) {
            return StatusPinpadEnum.PAGO;
        } else if (status.equalsIgnoreCase("failed")) {
            return StatusPinpadEnum.FALHA;
        } else if (status.equalsIgnoreCase("pending")) {
            return StatusPinpadEnum.AGUARDANDO;
        }
        return null;
    }

    private static String obterSecretKey(ConvenioCobrancaVO obj, Connection con) throws Exception {
        ConvenioCobranca convenioCobrancaDAO;
        try {
            convenioCobrancaDAO = new ConvenioCobranca(con);

            String secret = "";
            if (!UteisValidacao.emptyString(obj.getCodigoAutenticacao02())) {
                secret = obj.getCodigoAutenticacao02();
            } else {
                ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(obj.getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                secret = convenioCobrancaVO.getCodigoAutenticacao02();
            }

            if (UteisValidacao.emptyString(secret)) {
                throw new Exception("Chave secreta não encontrada. Convênio Cod.: " + obj.getCodigo());
            }
            return secret;
        } finally {
            convenioCobrancaDAO = null;
        }
    }

//    public static JSONObject estornarCobranca(MovPagamentoVO movPagamentoVO, Connection con) throws Exception {
//        //retorno null é pq não fez nada (não é erro) erro deve lançar a exceção;
//
//        PinPad pinPadDAO;
//        PinPadPedido pinPadPedidoDAO;
//        ConvenioCobranca convenioCobrancaDAO;
//        try {
//            pinPadDAO = new PinPad(con);
//            pinPadPedidoDAO = new PinPadPedido(con);
//            convenioCobrancaDAO = new ConvenioCobranca(con);
//
//            if (UteisValidacao.emptyString(movPagamentoVO.getRespostaRequisicaoPinpad()) ||
//                    !movPagamentoVO.getRespostaRequisicaoPinpad().toUpperCase().contains("STONE_CONNECT")) {
//                return null;
//            }
//
//            String chargeId = null;
//            Integer convenioCobranca = null;
//            Integer pinpadPedido = null;
//            try {
//                JSONObject jsonGeral = new JSONObject(movPagamentoVO.getRespostaRequisicaoPinpad());
//                convenioCobranca = jsonGeral.optInt("convenioCobranca");
//                pinpadPedido = jsonGeral.optInt("pinpadPedido");
//                JSONObject jsonResposta = new JSONObject(jsonGeral.getString("respostaRequisicao"));
//                chargeId = jsonResposta.getJSONArray("charges").getJSONObject(0).getString("id");
//            } catch (Exception ignored) {
//            }
//
//            if (UteisValidacao.emptyNumber(pinpadPedido)) {
//                Uteis.logarDebug("Pedido Pinpad não encontrado");
//                return null;
//            }
//
//            PinPadPedidoVO pinPadPedidoVO = pinPadPedidoDAO.consultarPorChavePrimaria(pinpadPedido);
//            if (!pinPadPedidoVO.getPinpad().equals(OpcoesPinpadEnum.STONE_CONNECT)) {
//                return null;
//            }
//
//            if (UteisValidacao.emptyString(chargeId)) {
//                throw new Exception("charge_id não encontrado");
//            }
//
//            if (UteisValidacao.emptyNumber(convenioCobranca)) {
//                convenioCobranca = pinPadPedidoVO.getConvenioCobrancaVO().getCodigo();
//            }
//
//            if (UteisValidacao.emptyNumber(convenioCobranca)) {
//                throw new Exception("Convênio de Cobrança não encontrado");
//            }
//
//            ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(convenioCobranca, Uteis.NIVELMONTARDADOS_DADOSBASICOS);
//            pinPadPedidoVO.setConvenioCobrancaVO(convenioCobrancaVO);
//
//            String url = URL_COBRANCA + "/" + chargeId;
//            RequestHttpService service = new RequestHttpService();
//            RespostaHttpDTO respostaHttpDTO = service.executeRequest(url, obterHeaders(pinPadPedidoVO.getConvenioCobrancaVO(), con), null, null, MetodoHttpEnum.DELETE);
//            if (!respostaHttpDTO.getHttpStatus().equals(HttpStatus.SC_OK)) {
//                if (!respostaHttpDTO.getResponse().toUpperCase().contains("THIS CHARGE CAN NOT BE CANCELED")) {
//                    throw new Exception(respostaHttpDTO.getResponse());
//                }
//            }
//
//            pinPadDAO.incluirHistorico(pinPadPedidoVO.getCodigo(), pinPadPedidoVO.getIdExterno(), "estorno", new JSONObject(respostaHttpDTO).toString());
//
//            pinPadPedidoVO.setStatus(StatusPinpadEnum.ESTORNADA);
//            pinPadPedidoDAO.alterarStatus(pinPadPedidoVO);
//
//            JSONObject json = new JSONObject();
//            json.put("sucesso", true);
//            json.put("idexterno", pinPadPedidoVO.getIdExterno());
//            json.put("pedido", pinPadPedidoVO.getCodigo());
//            json.put("data", pinPadPedidoVO.getDataRegistroApresentar());
//            json.put("valor", pinPadPedidoVO.getValorApresentar());
//            return json;
//        } finally {
//            pinPadDAO = null;
//            pinPadPedidoDAO = null;
//            convenioCobrancaDAO = null;
//        }
//    }

    public static void validarPedidoAguardandoEFechar(PinPadVO obj,Connection con) throws Exception {
        PinPadPedido pinPadPedidoDAO;
        PinPad pinpadDAO;
        try {
            pinPadPedidoDAO = new PinPadPedido(con);
            pinpadDAO = new PinPad(con);

            List<PinPadPedidoVO> pedidosAguardando = new ArrayList<>();
            pedidosAguardando = pinPadPedidoDAO.consultarPedidosAguardando(obj.getPdvPinpad());

            for (PinPadPedidoVO pedidoAguardando: pedidosAguardando) {
                StatusPinpadEnum statusPedido = obterStatusPedidoAtual(pedidoAguardando, con);
                if (statusPedido.equals(StatusPinpadEnum.AGUARDANDO)) {
                    fecharPedido(pedidoAguardando, StatusPinpadEnum.CANCELADO, false, true, con);
                    pinpadDAO.incluirHistorico(pedidoAguardando.getCodigo(), pedidoAguardando.getIdExterno(), "cancelar pedido automatico", pedidoAguardando.getParamsRespCancel());
                }
            }
        } finally {
            pinPadPedidoDAO = null;
            pinpadDAO = null;
        }
    }

}
