package servicos.impl.stone.connect;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentSettingsDTO extends SuperTO {

    private boolean visible = true;
    private String display_name;
    private List<String> devices_serial_number;
    private PaymentSetupDTO payment_setup;

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public String getDisplay_name() {
        return display_name;
    }

    public void setDisplay_name(String display_name) {
        this.display_name = display_name;
    }

    public List<String> getDevices_serial_number() {
        return devices_serial_number;
    }

    public void setDevices_serial_number(List<String> devices_serial_number) {
        this.devices_serial_number = devices_serial_number;
    }

    public PaymentSetupDTO getPayment_setup() {
        return payment_setup;
    }

    public void setPayment_setup(PaymentSetupDTO payment_setup) {
        this.payment_setup = payment_setup;
    }
}
