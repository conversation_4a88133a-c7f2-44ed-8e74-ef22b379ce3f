package servicos.impl.stone.connect;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import negocio.comuns.arquitetura.SuperTO;
import org.json.JSONObject;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PedidoDTO extends SuperTO {

    private List<ItemsDTO> items;
    private CustomerDTO customer;
    private boolean closed = false;
    private PaymentSettingsDTO poi_payment_settings;
    private JSONObject metadata;

    public List<ItemsDTO> getItems() {
        return items;
    }

    public void setItems(List<ItemsDTO> items) {
        this.items = items;
    }

    public CustomerDTO getCustomer() {
        return customer;
    }

    public void setCustomer(CustomerDTO customer) {
        this.customer = customer;
    }

    public boolean isClosed() {
        return closed;
    }

    public void setClosed(boolean closed) {
        this.closed = closed;
    }

    public PaymentSettingsDTO getPoi_payment_settings() {
        return poi_payment_settings;
    }

    public void setPoi_payment_settings(PaymentSettingsDTO poi_payment_settings) {
        this.poi_payment_settings = poi_payment_settings;
    }

    public JSONObject getMetadata() {
        return metadata;
    }

    public void setMetadata(JSONObject metadata) {
        this.metadata = metadata;
    }
}
