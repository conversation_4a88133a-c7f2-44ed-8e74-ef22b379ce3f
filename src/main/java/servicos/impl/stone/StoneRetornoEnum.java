package servicos.impl.stone;

import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;
import negocio.comuns.utilitarias.UteisValidacao;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 01/05/2020
 */
public enum StoneRetornoEnum {

    StatusNENHUM("NENHUM", ""),
    Status0("0000", "Aprovado", CodigoRetornoPactoEnum.SUCESSO, OperacaoRetornoCobrancaEnum.NENHUM),
    Status1("0001", "Aprovar após verificação de identidade"),
    Status2("0002", "Parcialmente aprovado"),
    Status3("0003", "Aprovado (VIP)"),
    Status4("0004", "Aprovado, atualizar trilha 3"),
    Status5("0005", "Aprovado, tipo de conta definido pelo emissor"),
    Status6("0006", "Parcialmente aprovado, tipo de conta definido pelo emissor"),
    Status8("0008", "Approved but fees disputed"),
    Status9("0009", "Approved with overdraft"),
    Status10("0010", "Approved, customer reactivated"),
    Status11("0011", "Aprovado offline, não foi possível ir online"),
    Status12("0012", "Approved, transaction processed offline by terminal"),
    Status13("0013", "Approved, transaction processed offline after referral"),
    Status1000("1000", "Não aprovado"),
    Status1001("1001", "Cartão vencido", CodigoRetornoPactoEnum.CARTAO_VENCIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status1002("1002", "Suspeita de fraude"),
    Status1003("1003", "Estabelecimento entrar em contato com o adquirente"),
    Status1004("1004", "Cartão com restrição"),
    Status1005("1005", "Estabelecimento entrar em contato com departamento de segurança do adquirente"),
    Status1006("1006", "Tentativas de senha excedidas"),
    Status1007("1007", "Consultar o emissor"),
    Status1008("1008", "Consultar as condições especiais do emissor"),
    Status1009("1009", "Estabelecimento inválido"),
    Status1010("1010", "Valor inválido"),
    Status1011("1011", "Cartão inválido", CodigoRetornoPactoEnum.CARTAO_INVALIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status1012("1012", "Senha necessária"),
    Status1013("1013", "Unacceptable fee"),
    Status1014("1014", "Nenhuma conta do tipo selecionado"),
    Status1015("1015", "Função selecionada não suportada"),
    Status1016("1016", "Saldo insuficiente", CodigoRetornoPactoEnum.SALDO_INSUFICIENTE, OperacaoRetornoCobrancaEnum.REENVIAR),
    Status1017("1017", "Senha inválida"),
    Status1018("1018", "No card record"),
    Status1019("1019", "Transação não permitida para o cartão"),
    Status1020("1020", "Transação não permitida para o terminal"),
    Status1021("1021", "Limite de valor para saque excedido"),
    Status1022("1022", "Violação de segurança"),
    Status1023("1023", "Limite de quantidade de saques excedido"),
    Status1024("1024", "Violação da lei"),
    Status1025("1025", "Cartão bloqueado", CodigoRetornoPactoEnum.CARTAO_BLOQUEADO_PERMANENTE, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status1026("1026", "Dados de senha inválidos"),
    Status1027("1027", "Erro no tamanho da senha"),
    Status1028("1028", "Erro de sincronia de chave de senha"),
    Status1029("1029", "Suspeita de cartão falso"),
    Status1030("1030", "Moeda inaceitável para o emissor"),
    Status1031("1031", "Not authorised and fees disputed"),
    Status1032("1032", "Cartão perdido ou roubado", CodigoRetornoPactoEnum.CARTAO_BLOQUEADO_PERMANENTE, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status1033("1033", "Ciclo de vida inválido para a transação"),
    Status1034("1034", "Authorization lifecycle has expired"),
    Status1035("1035", "Conta encerrada"),
    Status1036("1036", "Conta poupança encerrada ou bloqueada para encerramento", CodigoRetornoPactoEnum.CARTAO_CONTA_ENCERRADA, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status1037("1037", "Conta de crédito encerrada ou bloqueada para encerramento", CodigoRetornoPactoEnum.CARTAO_CONTA_ENCERRADA, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status1038("1038", "Closed credit facility cheque account or restricted for closing"),
    Status1039("1039", "Conta corrente encerrada ou bloqueada para encerramento", CodigoRetornoPactoEnum.CARTAO_CONTA_ENCERRADA, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status1040("1040", "Bad debt - Estabelecimento entrar em contato com adquirente"),
    Status1041("1041", "Status ruim para conta de origem"),
    Status1042("1042", "Status ruim para conta de destino"),
    Status1043("1043", "Cheque already posted"),
    Status1044("1044", "Information not on file"),
    Status1045("1045", "Código de segurança inválido"),
    Status1046("1046", "Amount not found"),
    Status1047("1047", "Troca de senha necessária"),
    Status1048("1048", "Nova senha inválida"),
    Status1049("1049", "Bank not found"),
    Status1050("1050", "Bank not effective"),
    Status1051("1051", "Customer vendor not found"),
    Status1052("1052", "Customer vendor not effective"),
    Status1053("1053", "Customer vendor account invalid"),
    Status1054("1054", "Vendor not found"),
    Status1055("1055", "Vendor not effective"),
    Status1056("1056", "Vendor data invalid"),
    Status1057("1057", "Data de pagamento inválida"),
    Status1058("1058", "Personal identification not found"),
    Status1059("1059", "Scheduled transactions exist"),
    Status1060("1060", "Transação não completou normalmente no terminal"),
    Status1061("1061", "Transação não suportada pelo emissor"),
    Status1062("1062", "Troco fácil não disponível"),
    Status1063("1063", "Limite de troco fácil excedido"),
    Status1064("1064", "Negado offline pelo terminal"),
    Status1065("1065", "Declined, terminal unable to process offline"),
    Status1066("1066", "Declined, transaction processed offline after referral"),
    Status1068("1068", "Identification number invalid"),
    Status1069("1069", "Driver number invalid"),
    Status1070("1070", "Vehicle number invalid"),
    Status1071("1071", "Digital certificate expired"),
    Status1801("1801", "Não é possivel Validar o PIN"),
    Status1802("1802", "Falha na Criptografia"),
    Status1813("1813", "Restricted card - Cartão não permite transação internacional"),
    Status1828("1828", "Contate a central do seu cartão"),
    Status1832("1832", "Erro no cartão - não tente novamente"),
    Status1841("1841", "Tente novamente mais tarde"),
    Status2000("2000", "Não aprovado"),
    Status2001("2001", "Cartão vencido", CodigoRetornoPactoEnum.CARTAO_VENCIDO, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status2002("2002", "Suspeita de fraude"),
    Status2003("2003", "Estabelecimento entrar em contato com o adquirente"),
    Status2004("2004", "Cartão com restrição"),
    Status2005("2005", "Estabelecimento entrar em contato com departamento de segurança do adquirente"),
    Status2006("2006", "Tentativas de senha excedidas"),
    Status2007("2007", "Condições especiais"),
    Status2008("2008", "Cartão perdido", CodigoRetornoPactoEnum.CARTAO_BLOQUEADO_PERMANENTE, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status2009("2009", "Cartão roubado", CodigoRetornoPactoEnum.CARTAO_BLOQUEADO_PERMANENTE, OperacaoRetornoCobrancaEnum.CONTATO_ALUNO),
    Status2010("2010", "Suspeita de cartão falso"),
    Status2011("2011", "Limite de quantidade de saques excedido"),
    Status2012("2012", "Limite de valor para saque excedido"),
    Status3002("3002", "Contate a central do seu cartão - não tente novamente"),
    Status9100("9100", "Erro no formato da mensagem"),
    Status9102("9102", "Transação inválida"),
    Status9103("9103", "Re-enter transaction"),
    Status9105("9105", "Adquirente não suportado pelo switch"),
    Status9106("9106", "Cutover in process"),
    Status9107("9107", "Emissor fora de operação"),
    Status9108("9108", "Não foi possível enviar a transação para o destinatário"),
    Status9109("9109", "Erro no sistema"),
    Status9110("9110", "Emissor se desconectou"),
    Status9111("9111", "Emissor não respondeu em tempo"),
    Status9112("9112", "Emissor indisponível"),
    Status9113("9113", "Transmissão duplicada"),
    Status9114("9114", "Não foi possível encontrar a transação original"),
    Status9115("9115", "Reconciliation cutover or checkpoint error"),
    Status9116("9116", "MAC incorreto"),
    Status9117("9117", "Erro de sincronização de chave de MAC"),
    Status9118("9118", "Nenhuma chave de comunicação disponível"),
    Status9119("9119", "Erro de sincronização de chave de encriptação"),
    Status9120("9120", "Erro de segurança de software/hardware, tente novamente"),
    Status9121("9121", "Erro de segurança de software/hardware"),
    Status9122("9122", "Número da mensagem fora de sequência"),
    Status9123("9123", "Requisição em progresso"),
    Status9124("9124", "Código de segurança inválido"),
    Status9125("9125", "Erro no banco de dados"),
    Status9128("9128", "Customer vendor format error"),
    Status9132("9132", "Erro nos dados de recorrência"),
    Status9133("9133", "Atualização não permitida"),
    Status9350("9350", "Violação de acordo comercial"),
    Status9999("9999", "Erro não especificado"),
    StatusUNPR("UNPR", "Não Foi possível processar. Não foi possível processar a mensagem. Tente novamente."),
    StatusIMSG("IMSG", "Mensagem Invalida. A mensagem enviada possui um formato inválido."),
    StatusPARS("PARS", "Erro na leitura da mensagem. Algum campo obrigatório não esta sendo enviado"),
    StatusSECU("SECU", "Segurança. Algum erro no processo de segurança. A chave de criptografia pode não estar presente no terminal."),
    StatusINTP("INTP", "SAK Invalido. O SAK enviado não foi reconhecido"),
    StatusRCPP("RCPP", "Destinatario Invalido. O local para aonde a mensagem foi enviada esta invalida"),
    StatusDPMG("DPMG", "Mensagem Duplicada. Esta mensagem já foi recebida pela Stone."),
    StatusVERS("VERS", "Protocolo. A versão do protocolo enviada não é suportada."),
    StatusMSGT("MSGT", "Tipo da Mensagem. o Message Type enviado não é reconhecido.");

    private String id;
    private String descricao;
    private CodigoRetornoPactoEnum codigoRetornoPacto;
    private OperacaoRetornoCobrancaEnum operacaoRetornoCobranca;

    private StoneRetornoEnum(String id, String descricao) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = OperacaoRetornoCobrancaEnum.NENHUM;
    }

    private StoneRetornoEnum(String id, String descricao, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = CodigoRetornoPactoEnum.OUTRO;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    private StoneRetornoEnum(String id, String descricao, CodigoRetornoPactoEnum codigoRetornoPacto, OperacaoRetornoCobrancaEnum operacaoRetornoCobranca) {
        this.id = id;
        this.descricao = descricao;
        this.codigoRetornoPacto = codigoRetornoPacto;
        this.operacaoRetornoCobranca = operacaoRetornoCobranca;
    }

    public String getId() {
        return id;
    }

    public String getDescricao() {
        return descricao;
    }

    public CodigoRetornoPactoEnum getCodigoRetornoPacto() {
        return codigoRetornoPacto;
    }

    public static StoneRetornoEnum valueOff(String id) {
        for (StoneRetornoEnum stone : StoneRetornoEnum.values()) {
            if (stone.getId().equals(id)) {
                return stone;
            }
        }
        return StatusNENHUM;
    }

    public OperacaoRetornoCobrancaEnum getOperacaoRetornoCobranca() {
        return operacaoRetornoCobranca;
    }
}
