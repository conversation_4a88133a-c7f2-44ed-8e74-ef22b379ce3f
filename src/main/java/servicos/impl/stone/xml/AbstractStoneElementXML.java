package servicos.impl.stone.xml;

import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import org.reflections.Reflections;
import servicos.impl.stone.xml.authorization.request.DocumentAcceptorAuthorisationRequestV02_1;
import servicos.impl.stone.xml.authorization.response.DocumentAcceptorAuthorisationResponseV02_1;
import servicos.impl.stone.xml.authorization.response.rejection.DocumentAcceptorRejectionV02_1;
import servicos.impl.stone.xml.cancellation.response.DocumentAcceptorCancellationResponseV02_1;

import java.util.Set;

/**
 * <AUTHOR>
 * @see <a href="https://online.stone.com.br/reference#introducao">Stone Online (E-Commerce)</a>
 * @since 26/02/2019
 */
public abstract class AbstractStoneElementXML {

    @XStreamAsAttribute
    private final String xmlns;

    protected AbstractStoneElementXML(String xmlns) {
        this.xmlns = xmlns;
    }

    public String toXML() {
        try {
            return getXStream(getClass()).toXML(this);
        } catch (Exception e) {
            throw new ConversaoXMLStoneException(getClass(), e);
        }
    }

    /**
     * Descobre todas as implementações de {@link #AbstractStoneElementXML(String)} e tenta
     * converter para um <code>&lt;T&gt;</code> usando como parâmetro de descoberta o {@link #getXmlns() Namespace}.
     *
     * @throws IllegalAccessException                        durante a descoberta das classes filhas.
     * @throws InstantiationException                        durante a descoberta das classes filhas.
     * @throws ConversaoXMLReflectionNamespaceStoneException se nenhuma classe com o namespace que contém no <code>xml</code> for encontrado.
     * @throws ConversaoXMLStoneException                    caso não consiga converter do XML para a classe.
     */
    public static <T extends AbstractStoneElementXML> T tryConverterFromXMLByNameSpace(String xml)
            throws IllegalAccessException, InstantiationException, ConversaoXMLReflectionNamespaceStoneException, ConversaoXMLStoneException {
        String packageName = AbstractStoneElementXML.class.getPackage().getName();
        Set<Class<? extends AbstractStoneElementXML>> implementationsClasses = getImplementationsAbstractStoneElementXML(packageName);

        for (Class<? extends AbstractStoneElementXML> clazz : implementationsClasses) {
            if (xml.contains(clazz.newInstance().getXmlns())) {
                xml = tratarRetornoXML(xml);
                return (T) fromXML(clazz, xml);
            }
        }

        throw new ConversaoXMLReflectionNamespaceStoneException(implementationsClasses, packageName, xml);
    }
    private static String tratarRetornoXML(String retornoXML) {
        //Quando retornar a bandeira do cartão, retirar pois não mandamos a mesma no envio e irá causar erro na conversão do xml caso à tenha no retorno.
        if(retornoXML.contains("<CardBrnd>") && retornoXML.contains("</CardBrnd>")) {
            String[] retornoXMLTRATADO;
            String[] retornoXMLTRATADO2;
            retornoXMLTRATADO = retornoXML.split("<Card>");
            retornoXMLTRATADO2 = retornoXMLTRATADO[1].split("</Card>");
            retornoXML = retornoXMLTRATADO[0] + retornoXMLTRATADO2[1];
        }
        if(retornoXML.contains("<Card />")) {
            String[] retornoXMLTRATADO;
            retornoXMLTRATADO = retornoXML.split("<Card />");
            retornoXML = retornoXMLTRATADO[0]  + retornoXMLTRATADO[1];
        }
        if(retornoXML.contains("<RcptOprId>") && retornoXML.contains("</RcptOprId>")) {
            String[] retornoXMLTRATADO;
            String[] retornoXMLTRATADO2;
            retornoXMLTRATADO = retornoXML.split("<RcptOprId>");
            retornoXMLTRATADO2 = retornoXMLTRATADO[1].split("</RcptOprId>");
            retornoXML = retornoXMLTRATADO[0] + retornoXMLTRATADO2[1];
        }
        if(retornoXML.contains("<IssrTxId>") && retornoXML.contains("</IssrTxId>")) {
            String[] retornoXMLTRATADO;
            String[] retornoXMLTRATADO2;
            retornoXMLTRATADO = retornoXML.split("<IssrTxId>");
            retornoXMLTRATADO2 = retornoXMLTRATADO[1].split("</IssrTxId>");
            retornoXML = retornoXMLTRATADO[0] + retornoXMLTRATADO2[1];
        }
        return retornoXML;
    }
    public boolean isAuthorisationResponse() {
        return this instanceof DocumentAcceptorAuthorisationResponseV02_1;
    }

    public DocumentAcceptorAuthorisationResponseV02_1 toAuthorisationResponse() {
        return (DocumentAcceptorAuthorisationResponseV02_1) this;
    }

    public DocumentAcceptorAuthorisationRequestV02_1 toAuthorisationRequest() {
        return (DocumentAcceptorAuthorisationRequestV02_1) this;
    }

    public boolean isRejection() {
        return this instanceof DocumentAcceptorRejectionV02_1;
    }

    public DocumentAcceptorRejectionV02_1 toRejection() {
        return (DocumentAcceptorRejectionV02_1) this;
    }

    public boolean isCancellationResponse() {
        return this instanceof DocumentAcceptorCancellationResponseV02_1;
    }

    public DocumentAcceptorCancellationResponseV02_1 toCancellationResponse() {
        return (DocumentAcceptorCancellationResponseV02_1) this;
    }

    public String getXmlns() {
        return xmlns;
    }

    private static Set<Class<? extends AbstractStoneElementXML>> getImplementationsAbstractStoneElementXML(String packageName) {
        return new Reflections(packageName).getSubTypesOf(AbstractStoneElementXML.class);
    }

    private static <T extends AbstractStoneElementXML> T fromXML(Class<T> clazz, String xml) throws ConversaoXMLStoneException {
        try {
            return (T) getXStream(clazz).fromXML(xml);
        } catch (Exception e) {
            throw new ConversaoXMLStoneException(clazz, xml, e);
        }
    }

    private static XStream getXStream(Class<?> clazz) {
        XStream xStream = new XStream();
        xStream.processAnnotations(clazz);

        return xStream;
    }

}
