package servicos.impl.stone.xml.cancellation.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import servicos.impl.stone.xml.authorization.response.TransactionResponseTx;

/**
 * <AUTHOR>
 * @since 01/03/2019
 */
class CancellationResponseCxlRspn {

    @XStreamAlias("Envt")
    private EnvironmentCancellationResponseEnvt environmentCancellationResponseEnvt;

    @XStreamAlias("TxRspn")
    private TransactionCancellationResponseTxRspn transactionCancellationResponseTxRspn;

    @XStreamAlias("Tx")
    private TransactionResponseTx transactionResponseTx;

    EnvironmentCancellationResponseEnvt getEnvironmentCancellationResponseEnvt() {
        return environmentCancellationResponseEnvt;
    }

    TransactionCancellationResponseTxRspn getTransactionCancellationResponseTxRspn() {
        return transactionCancellationResponseTxRspn;
    }

    TransactionResponseTx getTransactionResponseTx() {
        return transactionResponseTx;
    }
}
