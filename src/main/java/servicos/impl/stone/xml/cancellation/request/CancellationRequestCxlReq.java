package servicos.impl.stone.xml.cancellation.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 01/03/2019
 */
class CancellationRequestCxlReq {

    @XStreamAlias("Envt")
    private EnvironmentCancellationEnvt environmentCancellationEnvt;

    @XStreamAlias("Tx")
    private TransactionCancellationTx transactionCancellationTx;

    CancellationRequestCxlReq() {
    }

    CancellationRequestCxlReq(EnvironmentCancellationEnvt environmentCancellationEnvt,
                              TransactionCancellationTx transactionCancellationTx) {
        this.environmentCancellationEnvt = environmentCancellationEnvt;
        this.transactionCancellationTx = transactionCancellationTx;
    }

    void setEnvironmentCancellationEnvt(EnvironmentCancellationEnvt environmentCancellationEnvt) {
        this.environmentCancellationEnvt = environmentCancellationEnvt;
    }

    void setTransactionCancellationTx(TransactionCancellationTx transactionCancellationTx) {
        this.transactionCancellationTx = transactionCancellationTx;
    }
}
