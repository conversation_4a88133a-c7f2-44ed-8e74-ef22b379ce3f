package servicos.impl.stone.xml.cancellation.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import servicos.impl.stone.xml.AbstractStoneElementXML;
import servicos.impl.stone.xml.authorization.response.ResponseRspn;
import servicos.impl.stone.xml.authorization.response.ResponseToAuthorisationRspnToAuthstn;

/**
 * Documento de retorno da solicitação de cancelamento de uma cobrança no <b>E-Commerce da Stone</b>.
 *
 * <AUTHOR>
 * @since 01/03/2019
 */
@XStreamAlias("Document")
public class DocumentAcceptorCancellationResponseV02_1 extends AbstractStoneElementXML {

    @XStreamAsAttribute
    @XStreamAlias("xmlns:xsd")
    private String xmlnsXsd = "http://www.w3.org/2001/XMLSchema";

    @XStreamAsAttribute
    @XStreamAlias("xmlns:xsi")
    private String xmlnsXsi = "http://www.w3.org/2001/XMLSchema-instance";

    @XStreamAlias("AccptrCxlRspn")
    private AcceptorCancellationResponseAccptrCxlRspn acceptorCancellationResponseAccptrCxlRspn;

    public DocumentAcceptorCancellationResponseV02_1() {
        super("urn:AcceptorCancellationResponseV02.1");
    }

    public ResponseRspn getRespostaTransacao() {
        return getResponseToAuthorisationRspnToAuthstn().getResponseRspnType();
    }

    public String getDescricaoRespostaTransacao() {
        return getResponseToAuthorisationRspnToAuthstn().getResponseReasonRspnRsnDescricao();
    }

    private ResponseToAuthorisationRspnToAuthstn getResponseToAuthorisationRspnToAuthstn() {
        return getAcceptorCancellationResponseAccptrCxlRspn()
                .getCancellationResponseCxlRspn()
                .getTransactionCancellationResponseTxRspn()
                .getCancellationAuthorisationResultAuthstnRslt()
                .getResponseToAuthorisationRspnToAuthstn();
    }

    String getXmlnsXsd() {
        return xmlnsXsd;
    }

    String getXmlnsXsi() {
        return xmlnsXsi;
    }

    AcceptorCancellationResponseAccptrCxlRspn getAcceptorCancellationResponseAccptrCxlRspn() {
        return acceptorCancellationResponseAccptrCxlRspn;
    }
}
