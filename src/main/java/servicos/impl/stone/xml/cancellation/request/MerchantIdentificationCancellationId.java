package servicos.impl.stone.xml.cancellation.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 01/03/2019
 */
public class MerchantIdentificationCancellationId {

    @XStreamAlias("Id")
    private String id;

    MerchantIdentificationCancellationId(String id) {
        this.id = id;
    }

    MerchantIdentificationCancellationId() {

    }

    public String getId() {
        return id;
    }

    void setId(String id) {
        this.id = id;
    }
}
