package servicos.impl.stone.xml.cancellation.request;

import servicos.impl.stone.xml.authorization.request.*;
import servicos.impl.stone.xml.authorization.response.DocumentAcceptorAuthorisationResponseV02_1;
import servicos.impl.stone.xml.authorization.response.TransactionDetailsResponseTxDtls;

import java.util.Date;

import static servicos.impl.stone.xml.authorization.request.MessageFunctionMsgFctn.CANCELLATION_REQUEST_CCAQ;
import static servicos.impl.stone.xml.authorization.request.ProtocolVersionPrtcolVrsn.V_2_0_EPAS_ISO_20022_CAPE;

/**
 * Construtor de {@link DocumentAcceptorCancellationRequestV0_21}.
 *
 * <AUTHOR>
 * @since 01/03/2019
 */
public class DocumentAcceptorCancellationRequestV0_21Builder {

    private AcceptorCancellationRequestAccptrCxlReq acceptorCancellationRequestAccptrCxlReq = new AcceptorCancellationRequestAccptrCxlReq();
    private MerchantIdentificationCancellationId merchantIdentificationCancellationId = new MerchantIdentificationCancellationId();
    private POIIdentificationId poiIdentificationId = new POIIdentificationId();
    private TransactionIdentificationTxId transactionIdentificationTxId = new TransactionIdentificationTxId();
    private TransactionDetailsResponseTxDtls transactionDetailsResponseTxDtls = new TransactionDetailsResponseTxDtls();
    private OriginalTransactionOrgnlTx originalTransactionOrgnlTx = new OriginalTransactionOrgnlTx();

    private DocumentAcceptorCancellationRequestV0_21Builder() {
        MerchantCancellationMrchnt merchantCancellationMrchnt = new MerchantCancellationMrchnt();
        merchantCancellationMrchnt.setMerchantIdentificationCancellationId(merchantIdentificationCancellationId);

        PointInteractionPOI pointInteractionPOI = new PointInteractionPOI();
        pointInteractionPOI.setPoiIdentificationId(poiIdentificationId);

        EnvironmentCancellationEnvt environmentCancellationEnvt = new EnvironmentCancellationEnvt();
        environmentCancellationEnvt.setMerchantMrchnt(merchantCancellationMrchnt);
        environmentCancellationEnvt.setPointInteractionPOI(pointInteractionPOI);

        TransactionCancellationTx transactionCancellationTx = new TransactionCancellationTx(true);
        transactionCancellationTx.setTransactionIdentificationTxId(transactionIdentificationTxId);
        transactionCancellationTx.setTransactionDetailsResponseTxDtls(transactionDetailsResponseTxDtls);

        transactionCancellationTx.setOriginalTransactionOrgnlTx(originalTransactionOrgnlTx);

        CancellationRequestCxlReq cancellationRequestCxlReq = new CancellationRequestCxlReq();
        cancellationRequestCxlReq.setEnvironmentCancellationEnvt(environmentCancellationEnvt);
        cancellationRequestCxlReq.setTransactionCancellationTx(transactionCancellationTx);

        HeaderRequestHdr headerHdr = new HeaderRequestHdr(CANCELLATION_REQUEST_CCAQ, V_2_0_EPAS_ISO_20022_CAPE);
        acceptorCancellationRequestAccptrCxlReq.setHeaderHdr(headerHdr);
        acceptorCancellationRequestAccptrCxlReq.setCancellationRequestCxlReq(cancellationRequestCxlReq);
    }

    public static DocumentAcceptorCancellationRequestV0_21Builder init() {
        return new DocumentAcceptorCancellationRequestV0_21Builder();
    }

    /**
     * Sales Affiliation Key. É a chave utiliza para transacionar com a Stone, também conhecida como número lógico.
     *
     * @param saleAffiliationKeySAK Identificação do estabelecimento comercial no adquirente. Também conhecido internamente como <b>?SaleAffiliationKey?</b>.
     */
    public DocumentAcceptorCancellationRequestV0_21Builder comSaleAffiliationKeySAK(String saleAffiliationKeySAK) {
        merchantIdentificationCancellationId.setId(saleAffiliationKeySAK);
        return this;
    }

    /**
     * @param codigoIdentificacaoDoPontoDeInteracao Código de identificação do ponto de interação atribuído pelo estabelecimento.
     */
    public DocumentAcceptorCancellationRequestV0_21Builder comCodigoIdentificacaoDoPontoDeInteracao(String codigoIdentificacaoDoPontoDeInteracao) {
        poiIdentificationId.setId(codigoIdentificacaoDoPontoDeInteracao);
        return this;
    }

    /**
     * @param dataHoraLocalDaTransacaoDoPontoDeInteracao Data local e hora da transação atribuídas pelo POI (ponto de interação), no formato: <br>
     *                                                   <b>yyyy-mm-ddTHH:mm:SS</b>
     */
    public DocumentAcceptorCancellationRequestV0_21Builder comDataHoraLocalDaTransacaoDoPontoDeInteracao(String dataHoraLocalDaTransacaoDoPontoDeInteracao) {
        transactionIdentificationTxId.setTransactionDateTimeTxDtTm(dataHoraLocalDaTransacaoDoPontoDeInteracao);
        return this;
    }

    /**
     * @param identificacaoDaTransacaoPontoDeInteracao Valor que pode ser atribuído pelo cliente para identificar a transação. <br>
     *                                                 Este valor não é armazenado pela Stone ou demonstrado em nenhum relatório. <br>
     *                                                 <b>O formato é livre</b>.
     */
    public DocumentAcceptorCancellationRequestV0_21Builder comIdentificacaoDaTransacaoPontoDeInteracao(String identificacaoDaTransacaoPontoDeInteracao) {
        transactionIdentificationTxId.setTransactionReferenceTxRef(identificacaoDaTransacaoPontoDeInteracao);
        return this;
    }

    /**
     * @param valorTotalTransacaoEmCentavos Valor total da transação em <b>Valor total da transação em centavos</b>.
     */
    public DocumentAcceptorCancellationRequestV0_21Builder comValorTotalTransacaoEmCentavosASerCancelado(String valorTotalTransacaoEmCentavos) {
        transactionDetailsResponseTxDtls.setTotalAmountTtlAmt(valorTotalTransacaoEmCentavos);
        return this;
    }

    /**
     * @param identificacaoTransacaoSistemaITK Veja em {@link DocumentAcceptorAuthorisationRequestV02_1#setIdentificacaoTransacaoSistemaITK(String, Date)}.
     */
    public DocumentAcceptorCancellationRequestV0_21Builder comIdentificacaoTransacaoSistemaITK(String identificacaoTransacaoSistemaITK) {
        originalTransactionOrgnlTx.setInitiatorTransactionIdentificationInitrTxId(identificacaoTransacaoSistemaITK);
        return this;
    }

    /**
     * @param identificadorTransacaoStoneATK Veja em {@link DocumentAcceptorAuthorisationResponseV02_1#getIdentificadorTransacaoStoneATK()}.
     */
    public DocumentAcceptorCancellationRequestV0_21Builder comIdentificadorTransacaoStoneATK(String identificadorTransacaoStoneATK) {
        originalTransactionOrgnlTx.setRecipientTransactionIdentificationRcptTxId(identificadorTransacaoStoneATK);
        return this;
    }

    public DocumentAcceptorCancellationRequestV0_21 build() {
        return new DocumentAcceptorCancellationRequestV0_21(acceptorCancellationRequestAccptrCxlReq);
    }
}
