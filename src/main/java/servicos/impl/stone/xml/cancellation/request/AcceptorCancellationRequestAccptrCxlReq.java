package servicos.impl.stone.xml.cancellation.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import servicos.impl.stone.xml.authorization.request.HeaderRequestHdr;

/**
 * <AUTHOR>
 * @since 28/02/2019
 */
class AcceptorCancellationRequestAccptrCxlReq {

    @XStreamAlias("Hdr")
    private HeaderRequestHdr headerHdr;

    @XStreamAlias("CxlReq")
    private CancellationRequestCxlReq cancellationRequestCxlReq;

    AcceptorCancellationRequestAccptrCxlReq() {
    }

    AcceptorCancellationRequestAccptrCxlReq(HeaderRequestHdr headerHdr, CancellationRequestCxlReq cancellationRequestCxlReq) {
        this.headerHdr = headerHdr;
        this.cancellationRequestCxlReq = cancellationRequestCxlReq;
    }

    void setHeaderHdr(HeaderRequestHdr headerHdr) {
        this.headerHdr = headerHdr;
    }

    void setCancellationRequestCxlReq(CancellationRequestCxlReq cancellationRequestCxlReq) {
        this.cancellationRequestCxlReq = cancellationRequestCxlReq;
    }
}
