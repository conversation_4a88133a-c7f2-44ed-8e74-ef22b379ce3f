package servicos.impl.stone.xml.cancellation.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 01/03/2019
 */
class OriginalTransactionOrgnlTx {

    @XStreamAlias("InitrTxId")
    private String initiatorTransactionIdentificationInitrTxId;

    @XStreamAlias("RcptTxId")
    private String recipientTransactionIdentificationRcptTxId;

    OriginalTransactionOrgnlTx(String initiatorTransactionIdentificationInitrTxId,
                               String recipientTransactionIdentificationRcptTxId) {
        this.initiatorTransactionIdentificationInitrTxId = initiatorTransactionIdentificationInitrTxId;
        this.recipientTransactionIdentificationRcptTxId = recipientTransactionIdentificationRcptTxId;
    }

    OriginalTransactionOrgnlTx() {

    }

    void setInitiatorTransactionIdentificationInitrTxId(String initiatorTransactionIdentificationInitrTxId) {
        this.initiatorTransactionIdentificationInitrTxId = initiatorTransactionIdentificationInitrTxId;
    }

    void setRecipientTransactionIdentificationRcptTxId(String recipientTransactionIdentificationRcptTxId) {
        this.recipientTransactionIdentificationRcptTxId = recipientTransactionIdentificationRcptTxId;
    }
}
