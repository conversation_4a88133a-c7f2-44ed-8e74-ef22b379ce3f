package servicos.impl.stone.xml.cancellation.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import servicos.impl.stone.xml.authorization.response.ResponseToAuthorisationRspnToAuthstn;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
public class CancellationAuthorisationResultAuthstnRslt {

    @XStreamAlias("RspnToAuthstn")
    private ResponseToAuthorisationRspnToAuthstn responseToAuthorisationRspnToAuthstn;

    @XStreamAlias("CmpltnReqrd")
    private Boolean completionRequiredCmpltnReqrd;

    @XStreamAlias("AuthstnCd")
    private String AuthstnCd;

    public CancellationAuthorisationResultAuthstnRslt(ResponseToAuthorisationRspnToAuthstn responseToAuthorisationRspnToAuthstn,
                                                      Boolean completionRequiredCmpltnReqrd) {
        this.responseToAuthorisationRspnToAuthstn = responseToAuthorisationRspnToAuthstn;
        this.completionRequiredCmpltnReqrd = completionRequiredCmpltnReqrd;
    }

    public ResponseToAuthorisationRspnToAuthstn getResponseToAuthorisationRspnToAuthstn() {
        return responseToAuthorisationRspnToAuthstn;
    }

    public boolean isCompletionRequiredCmpltnReqrd() {
        return completionRequiredCmpltnReqrd;
    }

    public String getAuthstnCd() {
        return AuthstnCd;
    }
}
