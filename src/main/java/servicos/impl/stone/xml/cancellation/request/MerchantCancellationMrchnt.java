package servicos.impl.stone.xml.cancellation.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 01/03/2019
 */
class MerchantCancellationMrchnt {

    @XStreamAlias("Id")
    private MerchantIdentificationCancellationId merchantIdentificationCancellationId;

    MerchantCancellationMrchnt() {
    }

    MerchantCancellationMrchnt(MerchantIdentificationCancellationId merchantIdentificationCancellationId) {
        this.merchantIdentificationCancellationId = merchantIdentificationCancellationId;
    }

    void setMerchantIdentificationCancellationId(MerchantIdentificationCancellationId merchantIdentificationCancellationId) {
        this.merchantIdentificationCancellationId = merchantIdentificationCancellationId;
    }
}
