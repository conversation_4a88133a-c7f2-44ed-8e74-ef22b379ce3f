package servicos.impl.stone.xml.cancellation.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import servicos.impl.stone.xml.authorization.AbstractTransactionTx;
import servicos.impl.stone.xml.authorization.request.TransactionIdentificationTxId;
import servicos.impl.stone.xml.authorization.response.TransactionDetailsResponseTxDtls;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
class TransactionCancellationTx extends AbstractTransactionTx {

    @XStreamAlias("TxCaptr")
    private Boolean transactionCaptureTxCaptr = true;

    @XStreamAlias("TxDtls")
    private TransactionDetailsResponseTxDtls transactionDetailsResponseTxDtls;

    @XStreamAlias("OrgnlTx")
    private OriginalTransactionOrgnlTx originalTransactionOrgnlTx;

    public TransactionCancellationTx(Boolean transactionCaptureTxCaptr) {
        this.transactionCaptureTxCaptr = transactionCaptureTxCaptr;
    }

    TransactionCancellationTx(Boolean transactionCaptureTxCaptr,
                              TransactionIdentificationTxId transactionIdentificationTxId,
                              TransactionDetailsResponseTxDtls transactionDetailsResponseTxDtls,
                              OriginalTransactionOrgnlTx originalTransactionOrgnlTx) {
        super(transactionIdentificationTxId);
        this.transactionCaptureTxCaptr = transactionCaptureTxCaptr;
        this.transactionDetailsResponseTxDtls = transactionDetailsResponseTxDtls;
        this.originalTransactionOrgnlTx = originalTransactionOrgnlTx;
    }

    void setTransactionDetailsResponseTxDtls(TransactionDetailsResponseTxDtls transactionDetailsResponseTxDtls) {
        this.transactionDetailsResponseTxDtls = transactionDetailsResponseTxDtls;
    }

    void setOriginalTransactionOrgnlTx(OriginalTransactionOrgnlTx originalTransactionOrgnlTx) {
        this.originalTransactionOrgnlTx = originalTransactionOrgnlTx;
    }
}
