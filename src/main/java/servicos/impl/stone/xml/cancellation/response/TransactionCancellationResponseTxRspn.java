package servicos.impl.stone.xml.cancellation.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
class TransactionCancellationResponseTxRspn {

    @XStreamAlias("AuthstnRslt")
    private CancellationAuthorisationResultAuthstnRslt cancellationAuthorisationResultAuthstnRslt;

    CancellationAuthorisationResultAuthstnRslt getCancellationAuthorisationResultAuthstnRslt() {
        return cancellationAuthorisationResultAuthstnRslt;
    }

}
