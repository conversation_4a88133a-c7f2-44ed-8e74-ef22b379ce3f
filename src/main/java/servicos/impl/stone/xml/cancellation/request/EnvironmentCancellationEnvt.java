package servicos.impl.stone.xml.cancellation.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import servicos.impl.stone.xml.authorization.request.PointInteractionPOI;

/**
 * <AUTHOR>
 * @since 01/03/2019
 */
class EnvironmentCancellationEnvt {

    @XStreamAlias("Mrchnt")
    private MerchantCancellationMrchnt merchantMrchnt;

    @XStreamAlias("POI")
    private PointInteractionPOI pointInteractionPOI;

    EnvironmentCancellationEnvt() {
    }

    EnvironmentCancellationEnvt(MerchantCancellationMrchnt merchantMrchnt,
                                PointInteractionPOI pointInteractionPOI) {
        this.merchantMrchnt = merchantMrchnt;
        this.pointInteractionPOI = pointInteractionPOI;
    }

    void setMerchantMrchnt(MerchantCancellationMrchnt merchantMrchnt) {
        this.merchantMrchnt = merchantMrchnt;
    }

    void setPointInteractionPOI(PointInteractionPOI pointInteractionPOI) {
        this.pointInteractionPOI = pointInteractionPOI;
    }
}
