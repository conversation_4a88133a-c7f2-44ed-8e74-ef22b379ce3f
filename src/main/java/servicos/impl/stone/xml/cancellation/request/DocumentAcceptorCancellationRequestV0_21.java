package servicos.impl.stone.xml.cancellation.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import servicos.impl.stone.xml.AbstractStoneElementXML;

/**
 * Documento de envio para solicitar um cancelamento de uma cobrança no <b>E-Commerce da Stone</b>.
 *
 * <AUTHOR>
 * @since 28/02/2019
 */
@XStreamAlias("Document")
public class DocumentAcceptorCancellationRequestV0_21 extends AbstractStoneElementXML {

    @XStreamAlias("AccptrCxlReq")
    private AcceptorCancellationRequestAccptrCxlReq acceptorCancellationRequestAccptrCxlReq;

    public DocumentAcceptorCancellationRequestV0_21() {
        super("urn:AcceptorCancellationRequestV02.1");
    }

    DocumentAcceptorCancellationRequestV0_21(AcceptorCancellationRequestAccptrCxlReq acceptorCancellationRequestAccptrCxlReq) {
        this();
        this.acceptorCancellationRequestAccptrCxlReq = acceptorCancellationRequestAccptrCxlReq;
    }

}
