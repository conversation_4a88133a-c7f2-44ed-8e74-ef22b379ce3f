package servicos.impl.stone.xml;

import java.util.Set;

import static java.lang.String.format;

/**
 * Lançada quando algum erro acontecer durante a descoberta de qual classe filha de {@link AbstractStoneElementXML} se trata.
 *
 * <AUTHOR>
 * @since 13/02/2019
 */
class ConversaoXMLReflectionNamespaceStoneException extends StoneException {

    ConversaoXMLReflectionNamespaceStoneException(Set<Class<? extends AbstractStoneElementXML>> clazz,
                                                  String packageName,
                                                  String xml) {
        super(
                format("Houve uma falha na hora de descobrir qual classe o xml em questão se trata, através de seu namespace."
                        + "\nO package informado foi: %s"
                        + "\nAs classes usadas para descoberta foram: %s"
                        + "\nO XML que não foi possível descobrir e converter: %s", packageName, clazz, xml)
        );
    }

}
