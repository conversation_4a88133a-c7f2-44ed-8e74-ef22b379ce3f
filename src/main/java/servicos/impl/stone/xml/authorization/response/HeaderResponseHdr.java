package servicos.impl.stone.xml.authorization.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import servicos.impl.stone.xml.authorization.request.HeaderRequestHdr;
import servicos.impl.stone.xml.authorization.request.MessageFunctionMsgFctn;
import servicos.impl.stone.xml.authorization.request.ProtocolVersionPrtcolVrsn;

/**
 * <AUTHOR>
 * @since 26/02/2019
 */
public class HeaderResponseHdr extends HeaderRequestHdr {

    @XStreamAlias("CreDtTm")
    private String creationDateTimeCreDtTm;

    HeaderResponseHdr(MessageFunctionMsgFctn messageFunction,
                      ProtocolVersionPrtcolVrsn protocolVersion,
                      String creationDateTimeCreDtTm) {
        super(messageFunction, protocolVersion);
        this.creationDateTimeCreDtTm = creationDateTimeCreDtTm;
    }

    public String getCreationDateTimeCreDtTm() {
        return creationDateTimeCreDtTm;
    }
}
