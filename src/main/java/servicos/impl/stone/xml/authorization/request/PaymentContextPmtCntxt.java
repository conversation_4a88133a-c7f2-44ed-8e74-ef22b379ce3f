package servicos.impl.stone.xml.authorization.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
class PaymentContextPmtCntxt {

    @XStreamAlias("CardDataNtryMd")
    private String cardDataEntryModeCardDataNtryMd = CardDataEntryModeCardDataNtryMd.ECOMMERCE_OU_PRIMEIRA_RECORRENCIA.getValue();

    @XStreamAlias("TxChanl")
    private String transactionChannelTxChanl = TransactionChannelTxChanl.ECOMMERCE_OU_DIGITADA.getValue();

    PaymentContextPmtCntxt() {
    }

    PaymentContextPmtCntxt(CardDataEntryModeCardDataNtryMd cardDataEntryModeCardDataNtryMd,
                           TransactionChannelTxChanl transactionChannelTxChanl) {
        this.cardDataEntryModeCardDataNtryMd = cardDataEntryModeCardDataNtryMd.getValue();
        this.transactionChannelTxChanl = transactionChannelTxChanl.getValue();
    }

    void setCardDataEntryModeCardDataNtryMd(String cardDataEntryModeCardDataNtryMd) {
        this.cardDataEntryModeCardDataNtryMd = cardDataEntryModeCardDataNtryMd;
    }

    void setTransactionChannelTxChanl(String transactionChannelTxChanl) {
        this.transactionChannelTxChanl = transactionChannelTxChanl;
    }
}
