package servicos.impl.stone.xml.authorization.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
public class Card {

    @XStreamAlias("PlainCardData")
    private PlainCardData plainCardData;

    Card() {
    }

    public Card(PlainCardData plainCardData) {
        this.plainCardData = plainCardData;
    }

    PlainCardData getPlainCardData() {
        return plainCardData;
    }

    void setPlainCardData(PlainCardData plainCardData) {
        this.plainCardData = plainCardData;
    }
}
