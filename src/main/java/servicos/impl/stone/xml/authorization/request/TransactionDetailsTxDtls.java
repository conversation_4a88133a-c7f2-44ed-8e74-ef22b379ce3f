package servicos.impl.stone.xml.authorization.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import servicos.impl.stone.xml.authorization.response.TransactionDetailsResponseTxDtls;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
public class TransactionDetailsTxDtls extends TransactionDetailsResponseTxDtls {

    @XStreamAlias("RcrngTx")
    private RecurringTransactionRcrngTx recurringTransactionRcrngTx;

    TransactionDetailsTxDtls(CurrencyCcy currencyCcy,
                             AccountTypeAcctTp accountTypeAcctTp) {
        super(currencyCcy, accountTypeAcctTp);
    }

    public TransactionDetailsTxDtls(CurrencyCcy currencyCcy,
                                    String totalAmountTtlAmt,
                                    AccountTypeAcctTp accountTypeAcctTp,
                                    RecurringTransactionRcrngTx recurringTransactionRcrngTx) {
        super(currencyCcy, totalAmountTtlAmt, accountTypeAcctTp);
        this.recurringTransactionRcrngTx = recurringTransactionRcrngTx;
    }

    void setRecurringTransactionRcrngTx(RecurringTransactionRcrngTx recurringTransactionRcrngTx) {
        this.recurringTransactionRcrngTx = recurringTransactionRcrngTx;
    }
}
