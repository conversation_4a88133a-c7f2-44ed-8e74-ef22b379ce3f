package servicos.impl.stone.xml.authorization.response;

import negocio.comuns.utilitarias.RecuperadorEnumPadrao;
import negocio.comuns.utilitarias.ValorRecuperavel;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
enum MessageDestinationMsgDstn implements ValorRecuperavel {

    MENSAGEM_EXIBIDA_DISPLAY("MDSP"),
    MENSAGEM_EXIBIDA_DISPLAY_CLIENTE("CDSP");

    private final String value;

    MessageDestinationMsgDstn(String value) {
        this.value = value;
    }

    public static MessageDestinationMsgDstn fromValue(String value) {
        return RecuperadorEnumPadrao.fromValue(values(), value);
    }

    @Override
    public String getValor() {
        return value;
    }

    boolean isMensagemExibidaDisplayMDSP() {
        return this == MENSAGEM_EXIBIDA_DISPLAY;
    }

    boolean isMensagemExibidaDisplayClienteCDSP() {
        return this == MENSAGEM_EXIBIDA_DISPLAY_CLIENTE;
    }

}
