package servicos.impl.stone.xml.authorization.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
public class PointInteractionPOI {

    @XStreamAlias("Id")
    POIIdentificationId poiIdentificationId;

    @XStreamAlias("SysNm")
    private String SysNm = "PACTO SOLUCOES TECNOLOGICAS"; //nome do gateway para envio

    public PointInteractionPOI() {
    }

    public PointInteractionPOI(POIIdentificationId poiIdentificationId) {
        this.poiIdentificationId = poiIdentificationId;
    }

    POIIdentificationId getPoiIdentificationId() {
        return poiIdentificationId;
    }

    public void setPoiIdentificationId(POIIdentificationId poiIdentificationId) {
        this.poiIdentificationId = poiIdentificationId;
    }

    public String getSysNm() {
        return SysNm;
    }

    public void setSysNm(String sysNm) {
        SysNm = sysNm;
    }
}
