package servicos.impl.stone.xml.authorization.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
public class TransactionIdentificationTxId {

    @XStreamAlias("TxDtTm")
    private String transactionDateTimeTxDtTm;

    @XStreamAlias("TxRef")
    private String transactionReferenceTxRef;

    public TransactionIdentificationTxId() {
    }

    public TransactionIdentificationTxId(String transactionDateTimeTxDtTm, String transactionReferenceTxRef) {
        this.transactionDateTimeTxDtTm = transactionDateTimeTxDtTm;
        this.transactionReferenceTxRef = transactionReferenceTxRef;
    }

    public String getTransactionDateTimeTxDtTm() {
        return transactionDateTimeTxDtTm;
    }

    public String getTransactionReferenceTxRef() {
        return transactionReferenceTxRef;
    }

    public void setTransactionDateTimeTxDtTm(String transactionDateTimeTxDtTm) {
        this.transactionDateTimeTxDtTm = transactionDateTimeTxDtTm;
    }

    public void setTransactionReferenceTxRef(String transactionReferenceTxRef) {
        this.transactionReferenceTxRef = transactionReferenceTxRef;
    }
}
