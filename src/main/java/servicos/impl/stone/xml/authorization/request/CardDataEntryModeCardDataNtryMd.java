package servicos.impl.stone.xml.authorization.request;

/**
 * <AUTHOR>
 * @since 26/02/2019
 */
public enum CardDataEntryModeCardDataNtryMd {

    ECOMMERCE_OU_PRIMEIRA_RECORRENCIA("PHYS"),
    ECOMMERCE_OU_DEMAIS_RECORRENCIA("CDFL");

    private final String value;

    CardDataEntryModeCardDataNtryMd(String value) {
        this.value = value;
    }

    String getValue() {
        return value;
    }
}
