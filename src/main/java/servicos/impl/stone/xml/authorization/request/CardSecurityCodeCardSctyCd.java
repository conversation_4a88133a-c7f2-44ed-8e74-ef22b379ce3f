package servicos.impl.stone.xml.authorization.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
class CardSecurityCodeCardSctyCd {

    @XStreamAlias("CSCVal")
    private String cardSecurityCodeValueCSCVal;

    CardSecurityCodeCardSctyCd() {
    }

    CardSecurityCodeCardSctyCd(String cardSecurityCodeValueCSCVal) {
        this.cardSecurityCodeValueCSCVal = cardSecurityCodeValueCSCVal;
    }

    String getCardSecurityCodeValueCSCVal() {
        return cardSecurityCodeValueCSCVal;
    }

    void setCardSecurityCodeValueCSCVal(String cardSecurityCodeValueCSCVal) {
        this.cardSecurityCodeValueCSCVal = cardSecurityCodeValueCSCVal;
    }
}
