package servicos.impl.stone.xml.authorization.request;

import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import servicos.impl.stone.xml.StoneException;

import static servicos.impl.stone.xml.authorization.request.AccountTypeAcctTp.CREDITO;
import static servicos.impl.stone.xml.authorization.request.CurrencyCcy.REAL_BRASILEIRO_ISO_4217;
import static servicos.impl.stone.xml.authorization.request.InstalmentTypeInstlmtTp.A_VISTA;
import static servicos.impl.stone.xml.authorization.request.MessageFunctionMsgFctn.AUTHORISATION_REQUEST_AUTQ;
import static servicos.impl.stone.xml.authorization.request.ProtocolVersionPrtcolVrsn.V_2_0_EPAS_ISO_20022_CAPE;

/**
 * Construtor de {@link AcceptorAuthorisationRequestAccptrAuthstnReq}.
 *
 * <AUTHOR>
 * @since 26/02/2019
 */
public class DocumentAcceptorAuthorisationRequestV02_1Builder {

    private AcceptorAuthorisationRequestAccptrAuthstnReq acceptorAuthorisationRequestAccptrAuthstnReq = new AcceptorAuthorisationRequestAccptrAuthstnReq();
    private AuthorisationRequestAuthstnReq authorisationRequestAuthstnReq = new AuthorisationRequestAuthstnReq();
    private HeaderRequestHdr headerRequestHdr = new HeaderRequestHdr(AUTHORISATION_REQUEST_AUTQ, V_2_0_EPAS_ISO_20022_CAPE);
    private MerchantIdentificationId merchantIdentificationId = new MerchantIdentificationId();
    private POIIdentificationId poiIdentificationId = new POIIdentificationId();
    private PlainCardData plainCardData = new PlainCardData();
    private CardSecurityCodeCardSctyCd cardSecurityCodeCardSctyCd = new CardSecurityCodeCardSctyCd();
    private TransactionIdentificationTxId transactionIdentificationTxId = new TransactionIdentificationTxId();
    private TransactionDetailsTxDtls transactionDetailsTxDtls = new TransactionDetailsTxDtls(REAL_BRASILEIRO_ISO_4217, CREDITO);
    private RecurringTransactionRcrngTx recurringTransactionRcrngTx = new RecurringTransactionRcrngTx(A_VISTA, "0");
    private String dataExpiracaoAno;

    private DocumentAcceptorAuthorisationRequestV02_1Builder(CartaoCreditoTO dadosCartao) {
        MerchantMrchnt merchantMrchnt = new MerchantMrchnt();
        merchantMrchnt.setId(merchantIdentificationId);

        EnvironmentRequestEnvt environmentRequestEnvt = new EnvironmentRequestEnvt();
        environmentRequestEnvt.setMrchnt(merchantMrchnt);

        PointInteractionPOI pointInteractionPOI = new PointInteractionPOI();
        pointInteractionPOI.setPoiIdentificationId(poiIdentificationId);
        environmentRequestEnvt.setPointInteractionPOI(pointInteractionPOI);
        plainCardData.setCardSecurityCodeCardSctyCd(cardSecurityCodeCardSctyCd);

        Card card = new Card();
        card.setPlainCardData(plainCardData);
        environmentRequestEnvt.setCard(card);
        authorisationRequestAuthstnReq.setEnvironmentRequestEnvt(environmentRequestEnvt);

        TransactionTx transactionTx = new TransactionTx(!dadosCartao.isTransacaoVerificarCartao());
        transactionTx.setTransactionIdentificationTxId(transactionIdentificationTxId);

        transactionDetailsTxDtls.setRecurringTransactionRcrngTx(recurringTransactionRcrngTx);
        transactionTx.setTransactionDetailsTxDtls(transactionDetailsTxDtls);

        ContextCntxt contextCntxt = new ContextCntxt();
        PaymentContextPmtCntxt paymentContextPmtCntxt = new PaymentContextPmtCntxt(CardDataEntryModeCardDataNtryMd.ECOMMERCE_OU_PRIMEIRA_RECORRENCIA, TransactionChannelTxChanl.ECOMMERCE_OU_DIGITADA);

        if (dadosCartao.getParcelas() == 1) {
            if (dadosCartao.isTransacaoPresencial()) {
                //transação não recorrente...
                transactionTx.setAdditionalService("RECP");
                transactionTx.setServiceAttribute("FREC");
                paymentContextPmtCntxt = new PaymentContextPmtCntxt(CardDataEntryModeCardDataNtryMd.ECOMMERCE_OU_PRIMEIRA_RECORRENCIA, TransactionChannelTxChanl.ECOMMERCE_OU_DIGITADA);
            } else {
                //transação recorrente...
                transactionTx.setAdditionalService("RECP");
                transactionTx.setServiceAttribute("RREC");
                paymentContextPmtCntxt = new PaymentContextPmtCntxt(CardDataEntryModeCardDataNtryMd.ECOMMERCE_OU_DEMAIS_RECORRENCIA, TransactionChannelTxChanl.ECOMMERCE_OU_DIGITADA);
            }
        }

        authorisationRequestAuthstnReq.setTransactionTx(transactionTx);


        contextCntxt.setPaymentContextPmtCntxt(paymentContextPmtCntxt);
        authorisationRequestAuthstnReq.setContextCntxt(contextCntxt);

        acceptorAuthorisationRequestAccptrAuthstnReq.setAuthorisationRequestAuthstnReq(authorisationRequestAuthstnReq);
        acceptorAuthorisationRequestAccptrAuthstnReq.setHeaderHdr(headerRequestHdr);
    }

    public static DocumentAcceptorAuthorisationRequestV02_1Builder init(CartaoCreditoTO dadosCartao) {
        return new DocumentAcceptorAuthorisationRequestV02_1Builder(dadosCartao);
    }

    /**
     * Sales Affiliation Key. É a chave utiliza para transacionar com a Stone, também conhecida como número lógico.
     *
     * @param saleAffiliationKeySAK Identificação do estabelecimento comercial no adquirente. Também conhecido internamente como <b>?SaleAffiliationKey?</b>.
     */
    public DocumentAcceptorAuthorisationRequestV02_1Builder comSaleAffiliationKeySAK(String saleAffiliationKeySAK) {
        merchantIdentificationId.setId(saleAffiliationKeySAK);
        return this;
    }

    /**
     * @param nomeQueApareceraNaFatura O nome que aparecerá na fatura.
     *                                 Se a transação for:
     *                                 <ul>
     *                                 <li>Mastercard - O limite é 22 caracteres.</li>
     *                                 <li>Visa - O limite é 25 caracteres.</li>
     *                                 <li>Visa Parcelado - A visa usa os 8 primeiros caracteres do nome do lojista pra passar a informação de parcelamento, sobrando 17 caracteres.</li>
     */
    public DocumentAcceptorAuthorisationRequestV02_1Builder comNomeQueApareceraNaFatura(String nomeQueApareceraNaFatura) {
        merchantIdentificationId.setShortNameShrtNm(nomeQueApareceraNaFatura);
        return this;
    }

    /**
     * @param codigoIdentificacaoDoPontoDeInteracao Código de identificação do ponto de interação atribuído pelo estabelecimento.
     */
    public DocumentAcceptorAuthorisationRequestV02_1Builder comCodigoIdentificacaoDoPontoDeInteracao(String codigoIdentificacaoDoPontoDeInteracao) {
        poiIdentificationId.setId(codigoIdentificacaoDoPontoDeInteracao);
        return this;
    }

    /**
     * @param numeroDoCartao Número do cartão. <b>(Primary Account Number)</b>.
     */
    public DocumentAcceptorAuthorisationRequestV02_1Builder comNumeroDoCartao(String numeroDoCartao) {
        plainCardData.setPrimaryAccountNumberPAN(numeroDoCartao);
        return this;
    }

    /**
     * O formato final a ser enviado será: <b>yyyy-MM</b>.
     *
     * @param dataExpiracaoAno Ano da expiração do cartão.
     */
    public DocumentAcceptorAuthorisationRequestV02_1Builder comDataExpiracaoAno(String dataExpiracaoAno) {
        this.dataExpiracaoAno = dataExpiracaoAno;
        return this;
    }

    /**
     * O formato final a ser enviado será: <b>yyyy-MM</b>.
     *
     * @param dataExpiracaoMes Mês da expiração do cartão.
     */
    public DocumentAcceptorAuthorisationRequestV02_1Builder comDataExpiracaoMes(String dataExpiracaoMes) {
        plainCardData.setExpiryDateXpryDt(dataExpiracaoAno + "-" + dataExpiracaoMes);
        return this;
    }

    /**
     * @param codigoValidador CVV estampado no verso do cartão.
     */
    public DocumentAcceptorAuthorisationRequestV02_1Builder comCodigoValidador(String codigoValidador) {
        cardSecurityCodeCardSctyCd.setCardSecurityCodeValueCSCVal(codigoValidador);
        return this;
    }

    /**
     * @param dataHoraLocalDaTransacaoDoPontoDeInteracao Data local e hora da transação atribuídas pelo POI (ponto de interação), no formato: <br>
     *                                                   <b>yyyy-mm-ddTHH:mm:ss</b>
     */
    public DocumentAcceptorAuthorisationRequestV02_1Builder comDataHoraLocalDaTransacaoDoPontoDeInteracao(String dataHoraLocalDaTransacaoDoPontoDeInteracao) {
        transactionIdentificationTxId.setTransactionDateTimeTxDtTm(dataHoraLocalDaTransacaoDoPontoDeInteracao);
        return this;
    }

    /**
     * @param valorTotalTransacaoEmCentavos Valor total da transação em <b>Valor total da transação em centavos</b>.
     */
    public DocumentAcceptorAuthorisationRequestV02_1Builder comValorTotalTransacaoEmCentavos(String valorTotalTransacaoEmCentavos) {
        transactionDetailsTxDtls.setTotalAmountTtlAmt(valorTotalTransacaoEmCentavos);
        return this;
    }

    /**
     * Tipo de parcelamento.
     *
     * @param tipoParcelamentoStone Veja as opções em: {@link InstalmentTypeInstlmtTp}.
     *
     * @return
     */
    public DocumentAcceptorAuthorisationRequestV02_1Builder comTipoParcelamento(InstalmentTypeInstlmtTp tipoParcelamentoStone) {
        recurringTransactionRcrngTx.setInstalmentTypeInstlmtTp(tipoParcelamentoStone);
        return this;
    }

    /**
     * Trata-se de uma compra parcelada, porém utilizando o limite de uma única vez, ou seja, quem cuidará da recorrência é a própria Stone.
     *
     * @param quantidadeDeParcelas Número do total de parcelas.
     *                             Para transação a vista deve ser enviado o <b>valor &ldquo;0&quot;</b>.
     */
    public DocumentAcceptorAuthorisationRequestV02_1Builder comQuantidadeDeParcelas(Integer quantidadeDeParcelas) {
        recurringTransactionRcrngTx.setTotalNumberOfPaymentsTtlNbOfPmts(quantidadeDeParcelas.toString());
        return this;
    }

    public DocumentAcceptorAuthorisationRequestV02_1 build() {
        validarConstrucao();
        return new DocumentAcceptorAuthorisationRequestV02_1(acceptorAuthorisationRequestAccptrAuthstnReq);
    }

    private void validarConstrucao() {
        if (isNumeroParcelaMaiorUmEParcelamentoAvista()) {
            throw new StoneException("Não é permitido realizar uma compra a vista com um número de parcelas maior do que 1");
        }
    }

    private boolean isNumeroParcelaMaiorUmEParcelamentoAvista() {
        return Integer.valueOf(recurringTransactionRcrngTx.getTotalNumberOfPaymentsTtlNbOfPmts()) > 1
                && recurringTransactionRcrngTx.getInstalmentTypeInstlmtTpType().isAVista();
    }
}
