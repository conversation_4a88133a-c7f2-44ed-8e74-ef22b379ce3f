package servicos.impl.stone.xml.authorization.response.rejection;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 28/02/2019
 */
class RejectionRjct {

    @XStreamAlias("RjctRsn")
    private String rejectionReasonRjctRsn;

    @XStreamAlias("AddtlInf")
    private String additionalInformationAddtlInf;

    @XStreamAlias("MsgInErr")
    private String MensageInternalErrorMsgInErr;

    String getRejectionReasonRjctRsn() {
        return rejectionReasonRjctRsn;
    }

    String getAdditionalInformationAddtlInf() {
        return additionalInformationAddtlInf;
    }
}
