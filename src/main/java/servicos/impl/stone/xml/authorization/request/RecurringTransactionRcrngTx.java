package servicos.impl.stone.xml.authorization.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamOmitField;

import static servicos.impl.stone.xml.authorization.request.InstalmentTypeInstlmtTp.A_VISTA;

/**
 * <AUTHOR>
 * @since 28/02/2019
 */
class RecurringTransactionRcrngTx {

    @XStreamAlias("InstlmtTp")
    private String instalmentTypeInstlmtTp = A_VISTA.getValor();

    @XStreamOmitField
    private InstalmentTypeInstlmtTp instalmentTypeInstlmtTpType;

    @XStreamAlias("TtlNbOfPmts")
    private String totalNumberOfPaymentsTtlNbOfPmts = "0";

    RecurringTransactionRcrngTx() {
    }

    RecurringTransactionRcrngTx(InstalmentTypeInstlmtTp instalmentTypeInstlmtTp, String totalNumberOfPaymentsTtlNbOfPmts) {
        this.instalmentTypeInstlmtTp = instalmentTypeInstlmtTp.getValor();
        this.totalNumberOfPaymentsTtlNbOfPmts = totalNumberOfPaymentsTtlNbOfPmts;
    }

    InstalmentTypeInstlmtTp getInstalmentTypeInstlmtTpType() {
        return InstalmentTypeInstlmtTp.fromValue(instalmentTypeInstlmtTp);
    }

    String getTotalNumberOfPaymentsTtlNbOfPmts() {
        return totalNumberOfPaymentsTtlNbOfPmts;
    }

    void setTotalNumberOfPaymentsTtlNbOfPmts(String totalNumberOfPaymentsTtlNbOfPmts) {
        this.totalNumberOfPaymentsTtlNbOfPmts = totalNumberOfPaymentsTtlNbOfPmts;
    }

    void setInstalmentTypeInstlmtTp(InstalmentTypeInstlmtTp instalmentTypeInstlmtTp) {
        this.instalmentTypeInstlmtTp = instalmentTypeInstlmtTp.getValor();
    }

}
