package servicos.impl.stone.xml.authorization.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 26/02/2019
 */
class AcceptorAuthorisationRequestAccptrAuthstnReq {

    @XStreamAlias("Hdr")
    private HeaderRequestHdr headerHdr;

    @XStreamAlias("AuthstnReq")
    private AuthorisationRequestAuthstnReq authorisationRequestAuthstnReq;

    AcceptorAuthorisationRequestAccptrAuthstnReq() {
    }

    AcceptorAuthorisationRequestAccptrAuthstnReq(HeaderRequestHdr headerRequestHdr,
                                                 AuthorisationRequestAuthstnReq authorisationRequestAuthstnReq) {
        this.headerHdr = headerRequestHdr;
        this.authorisationRequestAuthstnReq = authorisationRequestAuthstnReq;
    }

    HeaderRequestHdr getHeaderHdr() {
        return headerHdr;
    }

    AuthorisationRequestAuthstnReq getAuthorisationRequestAuthstnReq() {
        return authorisationRequestAuthstnReq;
    }

    void setAuthorisationRequestAuthstnReq(AuthorisationRequestAuthstnReq authorisationRequestAuthstnReq) {
        this.authorisationRequestAuthstnReq = authorisationRequestAuthstnReq;
    }

    void setHeaderHdr(HeaderRequestHdr headerHdr) {
        this.headerHdr = headerHdr;
    }
}
