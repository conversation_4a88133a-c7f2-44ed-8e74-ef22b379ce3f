package servicos.impl.stone.xml.authorization.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
class ContextCntxt {

    @XStreamAlias("PmtCntxt")
    private PaymentContextPmtCntxt paymentContextPmtCntxt;

    ContextCntxt() {
    }

    ContextCntxt(PaymentContextPmtCntxt paymentContextPmtCntxt) {
        this.paymentContextPmtCntxt = paymentContextPmtCntxt;
    }

    void setPaymentContextPmtCntxt(PaymentContextPmtCntxt paymentContextPmtCntxt) {
        this.paymentContextPmtCntxt = paymentContextPmtCntxt;
    }
}
