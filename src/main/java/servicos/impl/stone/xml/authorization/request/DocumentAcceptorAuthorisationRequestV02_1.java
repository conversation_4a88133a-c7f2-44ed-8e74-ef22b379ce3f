package servicos.impl.stone.xml.authorization.request;

import br.com.pactosolucoes.comuns.util.StringUtilities;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import servicos.impl.apf.APF;
import servicos.impl.stone.xml.AbstractStoneElementXML;

import java.util.Date;

/**
 * Documento de envio para solicitar uma cobrança no <b>E-Commerce da Stone</b>.
 *
 * <AUTHOR>
 * @since 28/02/2019
 */
@XStreamAlias("Document")
public class DocumentAcceptorAuthorisationRequestV02_1 extends AbstractStoneElementXML {

    @XStreamAlias("AccptrAuthstnReq")
    private AcceptorAuthorisationRequestAccptrAuthstnReq acceptorAuthorisationRequestAccptrAuthstnReq;

    public DocumentAcceptorAuthorisationRequestV02_1() {
        super("urn:AcceptorAuthorisationRequestV02.1");
    }

    DocumentAcceptorAuthorisationRequestV02_1(AcceptorAuthorisationRequestAccptrAuthstnReq acceptorAuthorisationRequestAccptrAuthstnReq) {
        this();
        this.acceptorAuthorisationRequestAccptrAuthstnReq = acceptorAuthorisationRequestAccptrAuthstnReq;
    }

    /**
     * @return Veja em {@link #setIdentificacaoTransacaoSistemaITK(String, Date)}.
     */
    public String getIdentificacaoTransacaoSistemaITK() {
        return getTransactionTx().getInitiatorTransactionIdentificationInitrTxId();
    }

    /**
     * Identificação da transação definida pelo sistema que se comunica com o Host Stone. <br>
     * Este é o valor que será referencia para identificar uma transação na Stone. Este valor não pode se repetir para um SAK por 5 anos. <br><br>
     *
     * Deve ser formatado seguindo a seguinte regra: <br>
     * <b>nnnnnnddmmyyyyHHMMSS</b> <br>
     * De maneira mais legível: <b>nnnnnn dd mm yyyy HH MM SS</b> <br>
     * Sendo que <b>nnnnnn</b> = numero sequencial gerado pelo Sistema que se comunica com o Host Stone. <br><br>
     *
     * Este campo é conhecido internamente na Stone como <b>ITK</b>.
     *
     * @param codigoTransacaoOcorrente código da transacao do sistema da Pacto.
     * @param dataTransacaoOcorrente   data da transação ocorrente
     */
    public void setIdentificacaoTransacaoSistemaITK(String codigoTransacaoOcorrente, Date dataTransacaoOcorrente) {
        getTransactionTx().setInitiatorTransactionIdentificationInitrTxId(
                StringUtilities.formatarCampoForcandoZerosAEsquerda(codigoTransacaoOcorrente, 6)
                        + Uteis.getDataAplicandoFormatacao(dataTransacaoOcorrente, "ddMMyyyyHHmmss")
        );
    }

    /**
     * @return Veja em {@link #setIdentificadorTransacaoNaoArmazenadoPelaStone(String)}.
     */
    public String getIdentificadorTransacaoNaoArmazenadoPelaStone() {
        TransactionIdentificationTxId transactionIdentificationTxId = getTransactionIdentificationTxId();
        return transactionIdentificationTxId.getTransactionReferenceTxRef();
    }

    /**
     * @param identificadorTransacaoNaoArmazenadoPelaStone Valor que pode ser atribuído pelo cliente para identificar a transação. <br>
     *                                                     Este valor não é armazenado pela Stone ou demonstrado em nenhum relatório. <br>
     *                                                     <b>O formato é livre</b>.
     */
    public void setIdentificadorTransacaoNaoArmazenadoPelaStone(String identificadorTransacaoNaoArmazenadoPelaStone) {
        getTransactionIdentificationTxId().setTransactionReferenceTxRef(identificadorTransacaoNaoArmazenadoPelaStone);
    }

    /**
     * @return Data local e hora da transação atribuído pelo POI (ponto de interação). Este campo será ecoado pelo adquirente.
     */
    public String getDataHoraLocalTransacaoPontoDeInteracao() {
        return getTransactionIdentificationTxId().getTransactionDateTimeTxDtTm();
    }

    public String getValorTotalTransacaoEmCentavos() {
        return getTransactionTx().getTransactionDetailsTxDtls().getTotalAmountTtlAmt();
    }

    /**
     * @return o mesmo de {@link #getIdentificacaoTransacaoSistemaITK}, porém mais legível para auditoria.
     */
    public String getIdentificacaoTransacaoStonePretty() {
        String id = getTransactionTx().getInitiatorTransactionIdentificationInitrTxId();

        return "nnnnnn dd mm yyyy HH MM SS\n"
                + id.substring(0, 6) + " "
                + id.substring(6, 8) + " "
                + id.substring(8, 10) + " "
                + id.substring(10, 14) + " "
                + id.substring(14, 16) + " "
                + id.substring(16, 18) + " "
                + id.substring(18, 20);
    }

    public String getValorCartaoMascarado() {
        String cardNumber = getAuthorisationRequestAuthstnReq()
                .getEnvironmentRequestEnvt()
                .getCard()
                .getPlainCardData()
                .getPrimaryAccountNumberPAN();

        return cardNumber.substring(0, 6) + "******" + cardNumber.substring(cardNumber.length() - 4);
    }

    private AuthorisationRequestAuthstnReq getAuthorisationRequestAuthstnReq() {
        return acceptorAuthorisationRequestAccptrAuthstnReq.getAuthorisationRequestAuthstnReq();
    }

    private TransactionTx getTransactionTx() {
        return getAuthorisationRequestAuthstnReq().getTransactionTx();
    }

    private TransactionIdentificationTxId getTransactionIdentificationTxId() {
        return getTransactionTx().getTransactionIdentificationTxId();
    }

    public String getTransactionReferenceTxRef() {
        return getTransactionTx().getTransactionIdentificationTxId().getTransactionReferenceTxRef();
    }

    public void encriptarDadosSigilososEnvio() {
        try {
            String cartao = getAuthorisationRequestAuthstnReq().getEnvironmentRequestEnvt().getCard().getPlainCardData().getPrimaryAccountNumberPAN();
            getAuthorisationRequestAuthstnReq().getEnvironmentRequestEnvt().getCard().getPlainCardData().setPrimaryAccountNumberPAN(APF.getCartaoMascarado(cartao));
        } catch (Exception ignored) {
        }
        try {
            String cvv = getAuthorisationRequestAuthstnReq().getEnvironmentRequestEnvt().getCard().getPlainCardData().getCardSecurityCodeCardSctyCd().getCardSecurityCodeValueCSCVal();
            if (!UteisValidacao.emptyString(cvv)) {
                getAuthorisationRequestAuthstnReq().getEnvironmentRequestEnvt().getCard().getPlainCardData().getCardSecurityCodeCardSctyCd().setCardSecurityCodeValueCSCVal("***");
            }
        } catch (Exception ignored) {
        }
    }
}
