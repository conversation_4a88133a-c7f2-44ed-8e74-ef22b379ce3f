package servicos.impl.stone.xml.authorization.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import servicos.impl.stone.xml.authorization.AbstractTransactionTx;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
public class TransactionTx extends AbstractTransactionTx {

    @XStreamAlias("InitrTxId")
    private String initiatorTransactionIdentificationInitrTxId;

    @XStreamAlias("TxCaptr")
    private Boolean transactionCaptureTxCaptr = true;

    @XStreamAlias("TxDtls")
    private TransactionDetailsTxDtls transactionDetailsTxDtls;

    //Campo enviado apenas para transações de recorrência.
    //Valor "FIXO" = RECP (Recurring Payment)
    @XStreamAlias("AddtlSvc")
    private String additionalService;

    //Informações da transação de recorrência. Essa tag é obrigatória apenas para transações recorrentes. Não pode ser enviada em transações parceladas.
    //FREC (First Recurring) para a PRIMEIRA TRANSAÇÃO
    //RREC (Following Recurring) para as TRANSAÇÕES POSTERIORES
    @XStreamAlias("SvcAttr")
    private String serviceAttribute;



    TransactionTx() {
    }

    TransactionTx(Boolean transactionCaptureTxCaptr) {
        this.transactionCaptureTxCaptr = transactionCaptureTxCaptr;
    }

    TransactionTx(String initiatorTransactionIdentificationInitrTxId,
                  Boolean transactionCaptureTxCaptr,
                  TransactionIdentificationTxId transactionIdentificationTxId,
                  TransactionDetailsTxDtls transactionDetailsTxDtls) {
        super(transactionIdentificationTxId);
        this.initiatorTransactionIdentificationInitrTxId = initiatorTransactionIdentificationInitrTxId;
        this.transactionCaptureTxCaptr = transactionCaptureTxCaptr;
        this.transactionDetailsTxDtls = transactionDetailsTxDtls;
    }

    String getInitiatorTransactionIdentificationInitrTxId() {
        return initiatorTransactionIdentificationInitrTxId;
    }

    void setInitiatorTransactionIdentificationInitrTxId(String initiatorTransactionIdentificationInitrTxId) {
        this.initiatorTransactionIdentificationInitrTxId = initiatorTransactionIdentificationInitrTxId;
    }

    TransactionDetailsTxDtls getTransactionDetailsTxDtls() {
        return transactionDetailsTxDtls;
    }

    void setTransactionDetailsTxDtls(TransactionDetailsTxDtls transactionDetailsTxDtls) {
        this.transactionDetailsTxDtls = transactionDetailsTxDtls;
    }

    public Boolean getTransactionCaptureTxCaptr() {
        return transactionCaptureTxCaptr;
    }

    public String getServiceAttribute() {
        return serviceAttribute;
    }

    public void setServiceAttribute(String serviceAttribute) {
        this.serviceAttribute = serviceAttribute;
    }

    public String getAdditionalService() {
        return additionalService;
    }

    public void setAdditionalService(String additionalService) {
        this.additionalService = additionalService;
    }
}
