package servicos.impl.stone.xml.authorization.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 26/02/2019
 */
class AcceptorAuthorisationResponseAccptrAuthstnRspn {

    @XStreamAlias("Hdr")
    private HeaderResponseHdr headerHdr;

    @XStreamAlias("AuthstnRspn")
    private AuthorisationResponseAuthstnRspn authorisationResponseAuthstnRspn;

    AcceptorAuthorisationResponseAccptrAuthstnRspn(HeaderResponseHdr headerHdr) {
        this.headerHdr = headerHdr;
    }

    HeaderResponseHdr getHeaderHdr() {
        return headerHdr;
    }

    AuthorisationResponseAuthstnRspn getAuthorisationResponseAuthstnRspn() {
        return authorisationResponseAuthstnRspn;
    }
}
