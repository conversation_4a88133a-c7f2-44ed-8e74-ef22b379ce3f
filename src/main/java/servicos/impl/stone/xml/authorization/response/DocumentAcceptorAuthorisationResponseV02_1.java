package servicos.impl.stone.xml.authorization.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import servicos.impl.stone.xml.AbstractStoneElementXML;

import java.util.List;

/**
 * Documento de retorno da solicitação de uma cobrança no <b>E-Commerce da Stone</b>.
 *
 * <AUTHOR>
 * @since 28/02/2019
 */
@XStreamAlias("Document")
public class DocumentAcceptorAuthorisationResponseV02_1 extends AbstractStoneElementXML {

    @XStreamAsAttribute
    @XStreamAlias("xmlns:xsd")
    private String xmlnsXsd = "http://www.w3.org/2001/XMLSchema";

    @XStreamAsAttribute
    @XStreamAlias("xmlns:xsi")
    private String xmlnsXsi = "http://www.w3.org/2001/XMLSchema-instance";

    @XStreamAlias("AccptrAuthstnRspn")
    private AcceptorAuthorisationResponseAccptrAuthstnRspn acceptorAuthorisationResponseAccptrAuthstnRspn;

    public DocumentAcceptorAuthorisationResponseV02_1() {
        super("urn:AcceptorAuthorisationResponseV02.1");
    }

    /**
     * @return Código de autorização retornado pelo emissor.
     */
    public String getCodigoAutorizacao() {
        return getAuthorisationResultAuthstnRslt().getAuthorisationCodeAuthstnCd();
    }

    public String getTransactionReferenceTxRef() {
        return getAuthorisationResponseAuthstnRspn().getTransactionResponseTx().getTransactionIdentificationTxId().getTransactionReferenceTxRef();
    }

    public String getQtdLimiteRetentativas() {
        return getAuthorisationResponseAuthstnRspn().getTransactionResponseTxRspn().getAuthorisationResultAuthstnRslt().getRtryTxLmt();
    }

    public String getDataLimiteRetentativas() {
        return getAuthorisationResponseAuthstnRspn().getTransactionResponseTxRspn().getAuthorisationResultAuthstnRslt().getRtryTxDtLmt();
    }


    public ResponseRspn getRespostaTransacao() {
        return getAuthorisationResultAuthstnRslt().getResponseToAuthorisationRspnToAuthstn().getResponseRspnType();
    }

    public String getCodigoRespostaTransacao() {
        return getAuthorisationResultAuthstnRslt().getResponseToAuthorisationRspnToAuthstn().getResponseReasonRspnRsn();
    }

    public String getDescricaoRespostaTransacao() {
        List<ActionActn> listAction = getAuthorisationResponseAuthstnRspn()
                .getTransactionResponseTxRspn()
                .getActionActn();

        for (ActionActn actionActn : listAction) {
            MessageToPresentMsgToPres messageToPresentMsgToPres = actionActn.getMessageToPresentMsgToPres();

            if (messageToPresentMsgToPres.getMessageDestinationMsgDstnType().isMensagemExibidaDisplayMDSP()) {
                return messageToPresentMsgToPres.getMessageContentMsgCntt();
            }

        }

        if (!listAction.isEmpty()) {
            return listAction.get(0).getMessageToPresentMsgToPres().getMessageContentMsgCntt();
        }

        return "";
    }

    /**
     * Acquirer Transaction Key. É o campo que identifica a transação dentro da Stone. Também conhecido como NSU da Adquirente.
     *
     * @return Identificador da transação fornecido pelo adquirente na resposta da autorização. <b>(ATK)</b>
     */
    public String getIdentificadorTransacaoStoneATK() {
        return getAuthorisationResponseAuthstnRspn().getTransactionResponseTx().getRecipientTransactionIdentificationRcptTxId();
    }

    AcceptorAuthorisationResponseAccptrAuthstnRspn getAcceptorAuthorisationResponseAccptrAuthstnRspn() {
        return acceptorAuthorisationResponseAccptrAuthstnRspn;
    }

    String getXmlnsXsd() {
        return xmlnsXsd;
    }

    String getXmlnsXsi() {
        return xmlnsXsi;
    }

    private AuthorisationResponseAuthstnRspn getAuthorisationResponseAuthstnRspn() {
        return acceptorAuthorisationResponseAccptrAuthstnRspn.getAuthorisationResponseAuthstnRspn();
    }

    private AuthorisationResultAuthstnRslt getAuthorisationResultAuthstnRslt() {
        return getAuthorisationResponseAuthstnRspn().getTransactionResponseTxRspn().getAuthorisationResultAuthstnRslt();
    }

}
