package servicos.impl.stone.xml.authorization.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import servicos.impl.stone.xml.authorization.AbstractTransactionTx;
import servicos.impl.stone.xml.authorization.request.TransactionIdentificationTxId;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
public class TransactionResponseTx extends AbstractTransactionTx {

    @XStreamAlias("RcptTxId")
    private String recipientTransactionIdentificationRcptTxId;

    @XStreamAlias("RcptOprId")
    private String receptorOperationIdentificationRcptOprId;

    @XStreamAlias("IssrTxId")
    private String issrTxId;

    @XStreamAlias("TxDtls")
    private TransactionDetailsResponseTxDtls transactionDetailsResponseTxDtls;



    TransactionResponseTx(TransactionIdentificationTxId transactionIdentificationTxId,
                          TransactionDetailsResponseTxDtls transactionDetailsResponseTxDtls,
                          String recipientTransactionIdentificationRcptTxId) {
        super(transactionIdentificationTxId);
        this.recipientTransactionIdentificationRcptTxId = recipientTransactionIdentificationRcptTxId;
        this.transactionDetailsResponseTxDtls = transactionDetailsResponseTxDtls;
    }

    TransactionResponseTx(TransactionIdentificationTxId transactionIdentificationTxId,
                          TransactionDetailsResponseTxDtls transactionDetailsResponseTxDtls,
                          String recipientTransactionIdentificationRcptTxId,String receptorOperationIdentificationRcptOprId) {
        super(transactionIdentificationTxId);
        this.recipientTransactionIdentificationRcptTxId = recipientTransactionIdentificationRcptTxId;
        this.transactionDetailsResponseTxDtls = transactionDetailsResponseTxDtls;
        this.receptorOperationIdentificationRcptOprId = receptorOperationIdentificationRcptOprId;
    }

    public String getRecipientTransactionIdentificationRcptTxId() {
        return recipientTransactionIdentificationRcptTxId;
    }

    public TransactionDetailsResponseTxDtls getTransactionDetailsResponseTxDtls() {
        return transactionDetailsResponseTxDtls;
    }

    public String getReceptorOperationIdentificationRcptOprId() {
        return receptorOperationIdentificationRcptOprId;
    }

    public void setReceptorOperationIdentificationRcptOprId(String receptorOperationIdentificationRcptOprId) {
        this.receptorOperationIdentificationRcptOprId = receptorOperationIdentificationRcptOprId;
    }

    public String getIssrTxId() {
        return issrTxId;
    }

    public void setIssrTxId(String issrTxId) {
        this.issrTxId = issrTxId;
    }
}
