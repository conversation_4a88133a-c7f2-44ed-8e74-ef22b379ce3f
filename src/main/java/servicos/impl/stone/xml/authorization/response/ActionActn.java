package servicos.impl.stone.xml.authorization.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
class ActionActn {

    @XStreamAlias("ActnTp")
    private String actionTypeActnTp;

    @XStreamAlias("MsgToPres")
    private MessageToPresentMsgToPres messageToPresentMsgToPres;

    ActionActn(String actionTypeActnTp,
               MessageToPresentMsgToPres messageToPresentMsgToPres) {
        this.actionTypeActnTp = actionTypeActnTp;
        this.messageToPresentMsgToPres = messageToPresentMsgToPres;
    }

    String getActionTypeActnTp() {
        return actionTypeActnTp;
    }

    MessageToPresentMsgToPres getMessageToPresentMsgToPres() {
        return messageToPresentMsgToPres;
    }
}
