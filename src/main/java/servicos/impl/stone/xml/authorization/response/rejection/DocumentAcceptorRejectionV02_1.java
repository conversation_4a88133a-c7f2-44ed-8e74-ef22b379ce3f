package servicos.impl.stone.xml.authorization.response.rejection;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import servicos.impl.stone.xml.AbstractStoneElementXML;

/**
 * Documento de retorno representando uma rejeição no <b>E-Commerce da Stone</b>.
 *
 * <AUTHOR>
 * @since 28/02/2019
 */
@XStreamAlias("Document")
public class DocumentAcceptorRejectionV02_1 extends AbstractStoneElementXML {

    @XStreamAsAttribute
    @XStreamAlias("xmlns:xsd")
    private String xmlnsXsd = "http://www.w3.org/2001/XMLSchema";

    @XStreamAsAttribute
    @XStreamAlias("xmlns:xsi")
    private String xmlnsXsi = "http://www.w3.org/2001/XMLSchema-instance";

    @XStreamAlias("AccptrRjctn")
    private AcceptorRejectionAccptrRjctn acceptorAuthorisationResponseAccptrAuthstnRspn;

    public DocumentAcceptorRejectionV02_1() {
        super("urn:AcceptorRejectionV02.1");
    }

    public String getInformacaoAdicionalRejeicao() {
        return acceptorAuthorisationResponseAccptrAuthstnRspn.getRejectionRjct().getAdditionalInformationAddtlInf();
    }

    String getXmlnsXsd() {
        return xmlnsXsd;
    }

    String getXmlnsXsi() {
        return xmlnsXsi;
    }

    AcceptorRejectionAccptrRjctn getAcceptorAuthorisationResponseAccptrAuthstnRspn() {
        return acceptorAuthorisationResponseAccptrAuthstnRspn;
    }
}
