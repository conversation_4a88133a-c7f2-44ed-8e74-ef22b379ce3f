package servicos.impl.stone.xml.authorization.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamOmitField;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
class MessageToPresentMsgToPres {

    @XStreamAlias("MsgDstn")
    private String messageDestinationMsgDstn;

    @XStreamOmitField
    private MessageDestinationMsgDstn messageDestinationMsgDstnType;

    @XStreamAlias("MsgCntt")
    private String messageContentMsgCntt;

    MessageToPresentMsgToPres(MessageDestinationMsgDstn messageDestinationMsgDstn,
                              String messageContentMsgCntt) {
        this.messageDestinationMsgDstn = messageDestinationMsgDstn.getValor();
        this.messageContentMsgCntt = messageContentMsgCntt;
    }

    String getMessageDestinationMsgDstn() {
        return messageDestinationMsgDstn;
    }

    String getMessageContentMsgCntt() {
        return messageContentMsgCntt;
    }

    MessageDestinationMsgDstn getMessageDestinationMsgDstnType() {
        return MessageDestinationMsgDstn.fromValue(messageDestinationMsgDstn);
    }
}
