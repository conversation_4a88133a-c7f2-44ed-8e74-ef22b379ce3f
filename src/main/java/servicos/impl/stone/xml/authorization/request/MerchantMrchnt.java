package servicos.impl.stone.xml.authorization.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 26/02/2019
 */
public class MerchantMrchnt {

    @XStreamAlias("Id")
    private MerchantIdentificationId id;

    public MerchantMrchnt() {
    }

    public MerchantMrchnt(MerchantIdentificationId id) {
        this.id = id;
    }

    public MerchantIdentificationId getId() {
        return id;
    }

    void setId(MerchantIdentificationId id) {
        this.id = id;
    }
}
