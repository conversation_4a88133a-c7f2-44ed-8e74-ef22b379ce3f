package servicos.impl.stone.xml.authorization.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
class PlainCardData {

    @XStreamAlias("PAN")
    private String primaryAccountNumberPAN;

    @XStreamAlias("XpryDt")
    private String expiryDateXpryDt;

    @XStreamAlias("CardSctyCd")
    private CardSecurityCodeCardSctyCd cardSecurityCodeCardSctyCd;

    PlainCardData() {
    }

    PlainCardData(String primaryAccountNumberPAN,
                  String expiryDateXpryDt,
                  CardSecurityCodeCardSctyCd cardSecurityCodeCardSctyCd) {
        this.primaryAccountNumberPAN = primaryAccountNumberPAN;
        this.expiryDateXpryDt = expiryDateXpryDt;
        this.cardSecurityCodeCardSctyCd = cardSecurityCodeCardSctyCd;
    }

    String getPrimaryAccountNumberPAN() {
        return primaryAccountNumberPAN;
    }

    String getExpiryDateXpryDt() {
        return expiryDateXpryDt;
    }

    CardSecurityCodeCardSctyCd getCardSecurityCodeCardSctyCd() {
        return cardSecurityCodeCardSctyCd;
    }

    void setPrimaryAccountNumberPAN(String primaryAccountNumberPAN) {
        this.primaryAccountNumberPAN = primaryAccountNumberPAN;
    }

    void setExpiryDateXpryDt(String expiryDateXpryDt) {
        this.expiryDateXpryDt = expiryDateXpryDt;
    }

    void setCardSecurityCodeCardSctyCd(CardSecurityCodeCardSctyCd cardSecurityCodeCardSctyCd) {
        this.cardSecurityCodeCardSctyCd = cardSecurityCodeCardSctyCd;
    }
}
