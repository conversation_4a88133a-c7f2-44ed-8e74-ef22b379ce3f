package servicos.impl.stone.xml.authorization.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 26/02/2019
 */
public class MerchantIdentificationId {

    @XStreamAlias("Id")
    private String id;

    @XStreamAlias("ShrtNm")
    private String shortNameShrtNm;

    MerchantIdentificationId() {
    }

    MerchantIdentificationId(String id, String shortNameShrtNm) {
        this.id = id;
        this.shortNameShrtNm = shortNameShrtNm;
    }

    public String getId() {
        return id;
    }

    public String getShortNameShrtNm() {
        return shortNameShrtNm;
    }

    void setId(String id) {
        this.id = id;
    }

    void setShortNameShrtNm(String shortNameShrtNm) {
        this.shortNameShrtNm = shortNameShrtNm;
    }
}
