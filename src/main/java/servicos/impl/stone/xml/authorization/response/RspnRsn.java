package servicos.impl.stone.xml.authorization.response;

import negocio.comuns.utilitarias.RecuperadorEnumPadrao;
import negocio.comuns.utilitarias.ValorRecuperavel;

/**
 * <AUTHOR>
 * @since 08/03/2019
 * @see <a href="https://online.stone.com.br/reference#codigos-de-retorno-2">Códigos de retorno Autorizador Stone</a>
 */
public enum RspnRsn implements ValorRecuperavel {

    CODE_0000("0000", "Approved", "Aprovado"),
    CODE_0001("0001", "Approve after identity verification", "Aprovar após verificação de identidade"),
    CODE_0002("0002", "Partially approved", "Parcialmente aprovado"),
    CODE_0003("0003", "Approved (VIP)", "Aprovado (VIP)"),
    CODE_0004("0004", "Approved, upgrade track 3", "Aprovado, atualizar trilha 3"),
    CODE_0005("0005", "Approved, account type defined by the issuer", "Aprovado, tipo de conta definido pelo emissor"),
    CODE_0006("0006", "Partially approved, account type defined by the issuer", "Parcialmente aprovado, tipo de conta definido pelo emissor"),
    CODE_0008("0008", "Approved but fees disputed", "Approved but fees disputed"),
    CODE_0009("0009", "Approved with overdraft", "Approved with overdraft"),
    CODE_0010("0010", "Approved, customer reactivated", "Approved, customer reactivated"),
    CODE_0011("0011", "Aprovado offline, não foi possível ir online", "Aprovado offline, não foi possível ir online"),
    CODE_0012("0012", "Approved, transaction processed offline by terminal", "Approved, transaction processed offline by terminal"),
    CODE_0013("0013", "Approved, transaction processed offline after referral", "Approved, transaction processed offline after referral"),
    CODE_1000("1000", "Do not honour", "Não aprovado"),
    CODE_1001("1001", "Expired card", "Cartão vencido"),
    CODE_1002("1002", "Suspected fraud", "Suspeita de fraude"),
    CODE_1003("1003", "Card acceptor contact acquirer", "Estabelecimento entrar em contato com o adquirente"),
    CODE_1004("1004", "Restricted card", "Cartão com restrição"),
    CODE_1005("1005", "Card acceptor call acquirer's security department", "Estabelecimento entrar em contato com departamento de segurança do adquirente"),
    CODE_1006("1006", "Allowable PIN tries exceeded", "Tentativas de senha excedidas"),
    CODE_1007("1007", "Refer to card issuer", "Consultar o emissor"),
    CODE_1008("1008", "Refer to card issuer's special conditions", "Consultar as condições especiais do emissor"),
    CODE_1009("1009", "Invalid card acceptor", "Estabelecimento inválido"),
    CODE_1010("1010", "Invalid amount", "Valor inválido"),
    CODE_1011("1011", "Invalid card number", "Cartão inválido"),
    CODE_1012("1012", "PIN data required", "Senha necessária"),
    CODE_1013("1013", "Unacceptable fee", "Unacceptable fee"),
    CODE_1014("1014", "No account of type requested", "Nenhuma conta do tipo selecionado"),
    CODE_1015("1015", "Requested function not supported", "Função selecionada não suportada"),
    CODE_1016("1016", "Not sufficient funds", "Saldo insuficiente"),
    CODE_1017("1017", "Incorrect PIN", "Senha inválida"),
    CODE_1018("1018", "No card record", "No card record"),
    CODE_1019("1019", "Transaction not permitted to cardholder", "Transação não permitida para o portador"),
    CODE_1020("1020", "Transaction not permitted to terminal", "Transação não permitida para o terminal"),
    CODE_1021("1021", "Exceeds withdrawal amount limit", "Limite de valor para saque excedido"),
    CODE_1022("1022", "Security violation", "Violação de segurança"),
    CODE_1023("1023", "Exceeds withdrawal frequency limit", "Limite de quantidade de saques excedido"),
    CODE_1024("1024", "Violation of law", "Violação da lei"),
    CODE_1025("1025", "Card not effective", "Cartão bloqueado"),
    CODE_1026("1026", "Invalid PIN block", "Dados de senha inválidos"),
    CODE_1027("1027", "PIN length error", "Erro no tamanho da senha"),
    CODE_1028("1028", "PIN key sync error", "Erro de sincronia de chave de senha"),
    CODE_1029("1029", "Suspected counterfeit card", "Suspeita de cartão falso"),
    CODE_1030("1030", "Currency unacceptable to card issuer", "Moeda inaceitável para o emissor"),
    CODE_1031("1031", "Not authorised and fees disputed", "Not authorised and fees disputed"),
    CODE_1032("1032", "Lost/stolen card", "Cartão perdido ou roubado"),
    CODE_1033("1033", "Invalid life cycle for the transaction", "Ciclo de vida inválido para a transação"),
    CODE_1034("1034", "Authorization lifecycle has expired", "Authorization lifecycle has expired"),
    CODE_1035("1035", "Closed account", "Conta encerrada"),
    CODE_1036("1036", "Closed savings account, or restricted for closing", "Conta poupança encerrada ou bloqueada para encerramento"),
    CODE_1037("1037", "Closed credit account or restricted for closing", "Conta de crédito encerrada ou bloqueada para encerramento"),
    CODE_1038("1038", "Closed credit facility cheque account or restricted for closing", "Closed credit facility cheque account or restricted for closing"),
    CODE_1039("1039", "Closed cheque account or restricted for closing", "Conta corrente encerrada ou bloquada para encerramento"),
    CODE_1040("1040", "Bad debt", "Bad debt"),
    CODE_1041("1041", "From account bad status", "Status ruim para conta de origem"),
    CODE_1042("1042", "To account bad status", "Status ruim para conta de destino"),
    CODE_1043("1043", "Cheque already posted", "Cheque already posted"),
    CODE_1044("1044", "Information not on file", "Information not on file"),
    CODE_1045("1045", "Card verification data failed", "Código de segurança inválido"),
    CODE_1046("1046", "Amount not found", "Amount not found"),
    CODE_1047("1047", "PIN change required", "Troca de senha necessária"),
    CODE_1048("1048", "New PIN invalid", "Nova senha inválida"),
    CODE_1049("1049", "Bank not found", "Bank not found"),
    CODE_1050("1050", "Bank not effective", "Bank not effective"),
    CODE_1051("1051", "Customer vendor not found", "Customer vendor not found"),
    CODE_1052("1052", "Customer vendor not effective", "Customer vendor not effective"),
    CODE_1053("1053", "Customer vendor account invalid", "Customer vendor account invalid"),
    CODE_1054("1054", "Vendor not found", "Vendor not found"),
    CODE_1055("1055", "Vendor not effective", "Vendor not effective"),
    CODE_1056("1056", "Vendor data invalid", "Vendor data invalid"),
    CODE_1057("1057", "Payment date invalid", "Data de pagamento inválida"),
    CODE_1058("1058", "Personal identification not found", "Personal identification not found"),
    CODE_1059("1059", "Scheduled transactions exist", "Scheduled transactions exist"),
    CODE_1060("1060", "Transaction did not complete normally at terminal", "Transação não completou normalmente no terminal"),
    CODE_1061("1061", "Transaction not supported by the card issuer", "Transação não suportada pelo emissor"),
    CODE_1062("1062", "Cashback not allowed", "Troco fácil não disponível"),
    CODE_1063("1063", "Cashback amount exceeded", "Limite de troco fácil excedido"),
    CODE_1064("1064", "Declined, transaction processed offline by terminal", "Negado offline pelo terminal"),
    CODE_1065("1065", "Declined, terminal unable to process offline", "Declined, terminal unable to process offline"),
    CODE_1066("1066", "Declined, transaction processed offline after referral", "Declined, transaction processed offline after referral"),
    CODE_1068("1068", "Identification number invalid", "Identification number invalid"),
    CODE_1069("1069", "Driver number invalid", "Driver number invalid"),
    CODE_1070("1070", "Vehicle number invalid", "Vehicle number invalid"),
    CODE_1071("1071", "Digital certificate expired", "Digital certificate expired"),
    CODE_1801("1801", "Can not Validate PIN", "Não é possivel Validar o PIN"),
    CODE_1802("1802", "Encryption failed", "Falha na Criptografia"),
    CODE_2000("2000", "Do not honour", "Não aprovado"),
    CODE_2001("2001", "Expired card", "Cartão vencido"),
    CODE_2002("2002", "Suspected fraud", "Suspeita de fraude"),
    CODE_2003("2003", "Card acceptor contact acquirer", "Estabelecimento entrar em contato com o adquirente"),
    CODE_2004("2004", "Restricted card", "Cartão com restrição"),
    CODE_2005("2005", "Card acceptor call acquirer's security department", "Estabelecimento entrar em contato com departamento de segurança do adquirente"),
    CODE_2006("2006", "Allowable PIN tries exceeded", "Tentativas de senha excedidas"),
    CODE_2007("2007", "Special conditions", "Condições especiais"),
    CODE_2008("2008", "Lost card", "Cartão perdido"),
    CODE_2009("2009", "Stolen card", "Cartão roubado"),
    CODE_2010("2010", "Suspected counterfeit card", "Suspeita de cartão falso"),
    CODE_2011("2011", "Daily withdrawal uses exceeded", "Limite de quantidade de saques excedido"),
    CODE_2012("2012", "Daily withdrawal amount exceeded", "Limite de valor para saque excedido"),
    CODE_9100("9100", "One or more data element errors (see message error indicator)", "Erro no formato da mensagem"),
    CODE_9102("9102", "Invalid transaction", "Transação inválida"),
    CODE_9103("9103", "Re-enter transaction", "Re-enter transaction"),
    CODE_9105("9105", "Acquirer not supported by switch", "Adquirente não suportado pelo switch"),
    CODE_9106("9106", "Cutover in process", "Cutover in process"),
    CODE_9107("9107", "Card issuer or switch inoperative", "Emissor fora de operação"),
    CODE_9108("9108", "Transaction destination cannot be found for routing", "Não foi possível enviar a transação para o destinatário"),
    CODE_9109("9109", "System malfunction", "Erro no sistema"),
    CODE_9110("9110", "Card issuer signed off", "Emissor se desconectou"),
    CODE_9111("9111", "Card issuer timed out", "Emissor não respondeu em tempo"),
    CODE_9112("9112", "Card issuer unavailable", "Emissor indisponível"),
    CODE_9113("9113", "Duplicate transmission", "Transmissão duplicada"),
    CODE_9114("9114", "Not able to trace back to original transaction", "Não foi possível encontrar a transação original"),
    CODE_9115("9115", "Reconciliation cutover or checkpoint error", "Reconciliation cutover or checkpoint error"),
    CODE_9116("9116", "MAC incorrect", "MAC incorreto"),
    CODE_9117("9117", "MAC key sync error", "Erro de sincronização de chave de MAC"),
    CODE_9118("9118", "No communication keys available for use", "Nenhuma chave de comunicação disponível"),
    CODE_9119("9119", "Encryption key sync error", "Erro de sincronização de chave de encriptação"),
    CODE_9120("9120", "Security software/hardware error ? try again", "Erro de segurança de software/hardware, tente novamente"),
    CODE_9121("9121", "Security software/hardware error ? no action", "Erro de segurança de software/hardware"),
    CODE_9122("9122", "Message number out of sequence", "Número da mensagem fora de sequência"),
    CODE_9123("9123", "Request in progress", "Requisição em progresso"),
    CODE_9124("9124", "Invalid security code", "Código de segurança inválido"),
    CODE_9125("9125", "Database error", "Erro no banco de dados"),
    CODE_9128("9128", "Customer vendor format error", "Customer vendor format error"),
    CODE_9132("9132", "Recurring data error", "Erro nos dados de recorrência"),
    CODE_9133("9133", "Update not allowed", "Atualização não permitida"),
    CODE_9350("9350", "Violation of business arrangement", "Violação de acordo comercial"),
    CODE_9999("9999", "Other errors", "Erro não especificado"),
    REJECTION_UNPR("UNPR", "Rejection", "Não Foi possível processar ? Não foi possível processar a mensagem. Tente novamente."),
    REJECTION_IMSG("IMSG", "Rejection", "Mensagem Invalida ? A mensagem enviada possui um formato inválido."),
    REJECTION_PARS("PARS", "Rejection", "Erro na leitura da mensagem ? Algum campo obrigatório não esta sendo enviado"),
    REJECTION_SECU("SECU", "Rejection", "Segurança ? Algum erro no processo de segurança. A chave de criptografia pode não estar presente no terminal."),
    REJECTION_INTP("INTP", "Rejection", "SAK Invalido ? O SAK enviado não foi reconhecido"),
    REJECTION_RCPP("RCPP", "Rejection", "Destinatario Invalido ? O local para aonde a mensagem foi enviada esta invalida"),
    REJECTION_DPMG("DPMG", "Rejection", "Mensagem Duplicada ? Esta mensagem já foi recebida pela Stone."),
    REJECTION_VERS("VERS", "Rejection", "Protocolo ? A versão do protocolo enviada não é suportada."),
    REJECTION_MSGT("MSGT", "Rejection", "Tipo da Mensagem ? o Message Type enviado não é reconhecido."),
    ;

    private final String value;
    private final String descricao;
    private final String mensagem;

    RspnRsn(String value, String descricao, String mensagem) {
        this.value = value;
        this.descricao = descricao;
        this.mensagem = mensagem;
    }

    public static RspnRsn fromValue(String value) {
        return RecuperadorEnumPadrao.fromValue(values(), value);
    }

    @Override
    public String getValor() {
        return value;
    }

    public String getDescricao() {
        return descricao;
    }

    public String getMensagem() {
        return mensagem;
    }
}
