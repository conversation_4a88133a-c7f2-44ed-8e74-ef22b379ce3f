package servicos.impl.stone.xml.authorization.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import servicos.impl.stone.xml.authorization.request.MerchantIdentificationId;

/**
 * <AUTHOR>
 * @since 26/02/2019
 */
class EnvironmentResponseEnvt {

    @XStreamAlias("MrchntId")
    private MerchantIdentificationId mrchnt;

    EnvironmentResponseEnvt(MerchantIdentificationId mrchnt) {
        this.mrchnt = mrchnt;
    }

    MerchantIdentificationId getMrchnt() {
        return mrchnt;
    }
}
