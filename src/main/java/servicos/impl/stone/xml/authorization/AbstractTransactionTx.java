package servicos.impl.stone.xml.authorization;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import servicos.impl.stone.xml.authorization.request.TransactionIdentificationTxId;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
public abstract class AbstractTransactionTx {

    @XStreamAlias("TxId")
    private TransactionIdentificationTxId transactionIdentificationTxId;

    public AbstractTransactionTx() {
    }

    public AbstractTransactionTx(TransactionIdentificationTxId transactionIdentificationTxId) {
        this.transactionIdentificationTxId = transactionIdentificationTxId;
    }

    public TransactionIdentificationTxId getTransactionIdentificationTxId() {
        return transactionIdentificationTxId;
    }

    public void setTransactionIdentificationTxId(TransactionIdentificationTxId transactionIdentificationTxId) {
        this.transactionIdentificationTxId = transactionIdentificationTxId;
    }

}
