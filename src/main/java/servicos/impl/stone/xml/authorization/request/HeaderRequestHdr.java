package servicos.impl.stone.xml.authorization.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamOmitField;

import static servicos.impl.stone.xml.authorization.request.ProtocolVersionPrtcolVrsn.V_2_0_EPAS_ISO_20022_CAPE;

/**
 * <AUTHOR>
 * @since 26/02/2019
 */
public class HeaderRequestHdr {

    @XStreamAlias("MsgFctn")
    private String msgFctn;

    @XStreamOmitField
    private String msgFctnType;

    @XStreamAlias("PrtcolVrsn")
    private String prtcolVrsn = V_2_0_EPAS_ISO_20022_CAPE.getValue();

    public HeaderRequestHdr() {
    }

    public HeaderRequestHdr(MessageFunctionMsgFctn messageFunction, ProtocolVersionPrtcolVrsn protocolVersion) {
        this.msgFctn = messageFunction.getValor();
        this.prtcolVrsn = protocolVersion.getValue();
    }

    public String getMsgFctn() {
        return msgFctn;
    }

    public String getPrtcolVrsn() {
        return prtcolVrsn;
    }

    public MessageFunctionMsgFctn getMsgFctnType() {
        return MessageFunctionMsgFctn.fromValue(msgFctn);
    }
}
