package servicos.impl.stone.xml.authorization.response.rejection;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import servicos.impl.stone.xml.authorization.response.HeaderResponseHdr;

/**
 * <AUTHOR>
 * @since 28/02/2019
 */
class AcceptorRejectionAccptrRjctn {

    @XStreamAlias("Hdr")
    private HeaderResponseHdr headerHdr;

    @XStreamAlias("Rjct")
    private RejectionRjct rejectionRjct;

    HeaderResponseHdr getHeaderHdr() {
        return headerHdr;
    }

    RejectionRjct getRejectionRjct() {
        return rejectionRjct;
    }
}
