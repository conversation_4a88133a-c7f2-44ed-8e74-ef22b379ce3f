package servicos.impl.stone.xml.authorization.request;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <AUTHOR>
 * @since 26/02/2019
 */
class AuthorisationRequestAuthstnReq {

    @XStreamAlias("Envt")
    private EnvironmentRequestEnvt environmentRequestEnvt;

    @XStreamAlias("Cntxt")
    private ContextCntxt contextCntxt;

    @XStreamAlias("Tx")
    private TransactionTx transactionTx;

    AuthorisationRequestAuthstnReq() {
    }

    AuthorisationRequestAuthstnReq(EnvironmentRequestEnvt environmentRequestEnvt, ContextCntxt contextCntxt, TransactionTx transactionTx) {
        this.environmentRequestEnvt = environmentRequestEnvt;
        this.contextCntxt = contextCntxt;
        this.transactionTx = transactionTx;
    }

    EnvironmentRequestEnvt getEnvironmentRequestEnvt() {
        return environmentRequestEnvt;
    }

    TransactionTx getTransactionTx() {
        return transactionTx;
    }

    void setEnvironmentRequestEnvt(EnvironmentRequestEnvt environmentRequestEnvt) {
        this.environmentRequestEnvt = environmentRequestEnvt;
    }

    void setContextCntxt(ContextCntxt contextCntxt) {
        this.contextCntxt = contextCntxt;
    }

    void setTransactionTx(TransactionTx transactionTx) {
        this.transactionTx = transactionTx;
    }

}
