package servicos.impl.stone.xml.authorization.response;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.RecuperadorEnumPadrao;
import negocio.comuns.utilitarias.ValorRecuperavel;
import servicos.impl.stone.xml.StoneException;
import servicos.impl.stone.xml.authorization.request.DocumentAcceptorAuthorisationRequestV02_1;

import static br.com.pactosolucoes.comuns.notificacao.RecursoSistema.*;

/**
 * Resposta da transação proveniente do envio de um {@link DocumentAcceptorAuthorisationRequestV02_1}.
 *
 * <AUTHOR>
 * @since 28/02/2019
 */
public enum ResponseRspn implements ValorRecuperavel {

    RECUSADO_DECLINED("DECL"),
    APROVADO_APROVED("APPR"),
    APROVADO_PARCIAL_PARTIAL_APPROVED("PART"),
    ERRO_TECNICO_TECHINICAL_ERROR("TECH");

    private static final String CONTEXTO_LABEL = "No contexo da resposta de ";
    private static final String STATUS_NAO_MAPEADO = "o status (%s) não foi mapeado para um (%s).";
    private static final String MENSAGEM_ERRO_TRANSFORMACAO_CONTEXTO_AUTORIZACAO = CONTEXTO_LABEL + " de uma autorização, " + STATUS_NAO_MAPEADO;
    private static final String MENSAGEM_ERRO_TRANSFORMACAO_CONTEXTO_CANCELAMENTO = CONTEXTO_LABEL + " um cancelamento, " + STATUS_NAO_MAPEADO;

    private static final String SITUACAO_TRANSACAO_ENUM_NAME = SituacaoTransacaoEnum.class.getSimpleName();
    private static final String RECURSO_SISTEMA_ENUM_NAME = RecursoSistema.class.getSimpleName();

    private String value;

    ResponseRspn(String value) {
        this.value = value;
    }

    @Override
    public String getValor() {
        return value;
    }

    public static ResponseRspn fromValue(String value) {
        return RecuperadorEnumPadrao.fromValue(values(), value);
    }

    public SituacaoTransacaoEnum toSituacaoTransacaoEnumParaRetornoAutorizacao() {
        switch (this) {
            case APROVADO_APROVED:
                return SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO;

            case RECUSADO_DECLINED:
            case APROVADO_PARCIAL_PARTIAL_APPROVED:
                return SituacaoTransacaoEnum.NAO_APROVADA;

            case ERRO_TECNICO_TECHINICAL_ERROR:
                return SituacaoTransacaoEnum.COM_ERRO;
        }

        throw new StoneException(
                String.format(MENSAGEM_ERRO_TRANSFORMACAO_CONTEXTO_AUTORIZACAO, this, SITUACAO_TRANSACAO_ENUM_NAME)
        );
    }

    public SituacaoTransacaoEnum toSituacaoTransacaoEnumParaRetornoCancelamento(SituacaoTransacaoEnum statusOriginal) {
        switch (this) {
            case APROVADO_APROVED:
                return SituacaoTransacaoEnum.CANCELADA;

            case RECUSADO_DECLINED:
            case APROVADO_PARCIAL_PARTIAL_APPROVED:
            case ERRO_TECNICO_TECHINICAL_ERROR:
                return statusOriginal;
        }

        throw new StoneException(
                String.format(MENSAGEM_ERRO_TRANSFORMACAO_CONTEXTO_CANCELAMENTO, this, SITUACAO_TRANSACAO_ENUM_NAME)
        );
    }

    public RecursoSistema toRecursoSistemaAutorizacao() {
        return toRecursoSistema(MENSAGEM_ERRO_TRANSFORMACAO_CONTEXTO_AUTORIZACAO, true);
    }

    public RecursoSistema toRecursoSistemaCancelamento() {
        return toRecursoSistema(MENSAGEM_ERRO_TRANSFORMACAO_CONTEXTO_CANCELAMENTO, false);
    }

    private RecursoSistema toRecursoSistema(String mensagem, boolean isAutorizacao) {
        switch (this) {
            case APROVADO_APROVED:
                return isAutorizacao ? STONE_TRANSACAO_AUTORIZACAO_APROVADO : STONE_TRANSACAO_CANCELAMENTO_APROVADO;

            case RECUSADO_DECLINED:
                return isAutorizacao ? STONE_TRANSACAO_AUTORIZACAO_NAO_APROVADO : STONE_TRANSACAO_CANCELAMENTO_NAO_APROVADO;

        }

        throw new StoneException(
                String.format(mensagem, this, RECURSO_SISTEMA_ENUM_NAME)
        );
    }

}
