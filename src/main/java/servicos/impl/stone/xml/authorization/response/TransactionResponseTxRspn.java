package servicos.impl.stone.xml.authorization.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamImplicit;

import java.util.List;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
public class TransactionResponseTxRspn {

    @XStreamAlias("AuthstnRslt")
    private AuthorisationResultAuthstnRslt authorisationResultAuthstnRslt;

    @XStreamAlias("Actn")
    @XStreamImplicit
    private List<ActionActn> actionActn;

    AuthorisationResultAuthstnRslt getAuthorisationResultAuthstnRslt() {
        return authorisationResultAuthstnRslt;
    }

    List<ActionActn> getActionActn() {
        return actionActn;
    }

}
