package servicos.impl.stone.xml.authorization.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import servicos.impl.stone.xml.authorization.request.AccountTypeAcctTp;
import servicos.impl.stone.xml.authorization.request.CurrencyCcy;

import static servicos.impl.stone.xml.authorization.request.AccountTypeAcctTp.CREDITO;
import static servicos.impl.stone.xml.authorization.request.CurrencyCcy.REAL_BRASILEIRO_ISO_4217;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
public class TransactionDetailsResponseTxDtls {

    @XStreamAlias("Ccy")
    private String currencyCcy = REAL_BRASILEIRO_ISO_4217.getValue();

    @XStreamAlias("TtlAmt")
    private String totalAmountTtlAmt;

    @XStreamAlias("AcctTp")
    private String accountTypeAcctTp = CREDITO.getValue();

    @XStreamAlias("AcctFndSrc")
    private String accountTypeAcctFndSrc;

    @XStreamAlias("Rsn")
    private String reason = "4000"; //Usar "4000 - Customer Cancelation" como padrão. Para novos valores, consultar a tabela na documentação da Stone : https://online.stone.com.br/reference/cancellation

    @XStreamAlias("TtlCxlAmt")
    private String totalAmountTtlCxlAmt;

    public TransactionDetailsResponseTxDtls() {
    }

    public TransactionDetailsResponseTxDtls(CurrencyCcy currencyCcy,
                                            AccountTypeAcctTp accountTypeAcctTp) {
        this.currencyCcy = currencyCcy.getValue();
        this.accountTypeAcctTp = accountTypeAcctTp.getValue();
    }

    public TransactionDetailsResponseTxDtls(CurrencyCcy currencyCcy, String totalAmountTtlAmt, AccountTypeAcctTp accountTypeAcctTp) {
        this.currencyCcy = currencyCcy.getValue();
        this.totalAmountTtlAmt = totalAmountTtlAmt;
        this.accountTypeAcctTp = accountTypeAcctTp.getValue();
    }

    public String getCurrencyCcy() {
        return currencyCcy;
    }

    public String getTotalAmountTtlAmt() {
        return totalAmountTtlAmt;
    }

    public String getAccountTypeAcctTp() {
        return accountTypeAcctTp;
    }

    public void setTotalAmountTtlAmt(String totalAmountTtlAmt) {
        this.totalAmountTtlAmt = totalAmountTtlAmt;
    }

    public String getReason() {
        return reason;
    }

    public String getTotalAmountTtlCxlAmt() {
        return totalAmountTtlCxlAmt;
    }

    public void setTotalAmountTtlCxlAmt(String totalAmountTtlCxlAmt) {
        this.totalAmountTtlCxlAmt = totalAmountTtlCxlAmt;
    }

}
