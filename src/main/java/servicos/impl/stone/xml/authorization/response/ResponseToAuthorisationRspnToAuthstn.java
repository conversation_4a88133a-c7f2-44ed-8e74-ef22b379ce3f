package servicos.impl.stone.xml.authorization.response;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamOmitField;

/**
 * <AUTHOR>
 * @since 27/02/2019
 */
public class ResponseToAuthorisationRspnToAuthstn {

    @XStreamAlias("Rspn")
    private String responseRspn;

    @XStreamOmitField
    private ResponseRspn responseRspnType;

    @XStreamAlias("RspnRsn")
    private String responseReasonRspnRsn;

    @XStreamAlias("SchmRspn")
    private String schmRspn;

    @XStreamAlias("SchmAddtlRspnCd")
    private String schmAddtlRspnCd;

    ResponseToAuthorisationRspnToAuthstn(String responseRspn, String responseReasonRspnRsn) {
        this.responseRspn = responseRspn;
        this.responseReasonRspnRsn = responseReasonRspnRsn;
    }

    public String getResponseRspn() {
        return responseRspn;
    }

    public String getResponseReasonRspnRsn() {
        return responseReasonRspnRsn;
    }

    public ResponseRspn getResponseRspnType() {
        return ResponseRspn.fromValue(responseRspn);
    }

    public String getResponseReasonRspnRsnDescricao() {
        return RspnRsn.fromValue(responseReasonRspnRsn).getMensagem();
    }

    public String getSchmAddtlRspnCd() {
        return schmAddtlRspnCd;
    }

    public void setSchmAddtlRspnCd(String schmAddtlRspnCd) {
        this.schmAddtlRspnCd = schmAddtlRspnCd;
    }
}
