package servicos.impl.stone;

import servicos.impl.stone.xml.StoneException;

/**
 * Lançada quando algum erro durante a comunicação com o E-Commerce da Stone ocorrer.
 *
 * <AUTHOR>
 * @since 19/03/2019
 */
class StoneErroComunicacaoAPIException extends StoneException {

    StoneErroComunicacaoAPIException(Throwable cause) {
        super("Não foi possível comunicar-se com a Stone. Tente novamente mais tarde!", cause);
    }
}
