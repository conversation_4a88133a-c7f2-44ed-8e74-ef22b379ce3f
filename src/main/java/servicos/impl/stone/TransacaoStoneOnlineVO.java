package servicos.impl.stone;

import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.utilitarias.UteisValidacao;
import org.json.JSONObject;
import org.json.XML;
import servicos.impl.apf.APF;
import servicos.impl.stone.xml.authorization.response.RspnRsn;

/**
 * <AUTHOR>
 * @since 26/02/2019
 */
public class TransacaoStoneOnlineVO extends TransacaoVO {

    @Override
    public String getValorCodigoExterno() {
        return getCodigoExterno();
    }

    @Override
    public String getValorCartaoMascarado() {
        try {
            String cartao = obterValorParametroXML(getParamsEnvio(), "PAN");
            return APF.getCartaoMascarado(cartao);
        } catch (Exception e) {
            //ignore
        }
        return "";
    }

    @Override
    public String getValorUltimaTransacaoAprovada() {
        return getValorAtributoResposta(APF.Transacao);
    }

    @Override
    public String getResultadoRequisicao() {
        String msg = obterValorParametroXML(getParamsResposta(),"MsgCntt");
        String adicional = obterValorParametroXML(getParamsResposta(),"AddtlInf");
        String retorno = "";
        if (!UteisValidacao.emptyString(msg)) {
            retorno = msg;
        }
        if (!UteisValidacao.emptyString(adicional)) {
            retorno += (" " + adicional);
        }

        if (UteisValidacao.emptyString(retorno) || retorno.equalsIgnoreCase("Não autorizada")) {
            String codigoRetorno = obterValorParametroXML(getParamsResposta(), "RspnRsn");
            if (!UteisValidacao.emptyString(codigoRetorno)) {
                StoneRetornoEnum stoneRetornoEnum = StoneRetornoEnum.valueOff(codigoRetorno);
                if (!stoneRetornoEnum.equals(StoneRetornoEnum.StatusNENHUM)) {
                    retorno += (" - " + stoneRetornoEnum.getDescricao());
                }
            }
        }
        if (!UteisValidacao.emptyString(getErroProcessamento()) && (getSituacao().equals(SituacaoTransacaoEnum.COM_ERRO) || getSituacao().equals(SituacaoTransacaoEnum.APROVADA))) {
            return getErroProcessamento();
        }

        return retorno;
    }

    @Override
    public String getAutorizacao() {
        String auto =  obterValorParametroXML(getParamsResposta(),"AuthstnCd");
        if (auto.equalsIgnoreCase("N/A")) {
            return "";
        }
        return auto;
    }

    @Override
    public String getBandeira() {
        return obterValorParametroXML(getParamsResposta(),"CardBrnd").toUpperCase();
    }

    @Override
    public String getCartaoMascarado(){
        return getValorCartaoMascarado();
    }

    @Override
    public String getValorAtributoResposta(String nomeAtributo) {
        if (nomeAtributo.equals(APF.Transacao)) {
            return obterValorParametroXML(getParamsResposta(),"RcptTxId");
        }
        return "";
    }

    @Override
    public String getValorAtributoCancelamento(String nomeAtributo) {
        return getResultadoRequisicaoCancelamento();
    }

    @Override
    public String getCodErroExterno() {
        String rspnRsn = obterValorParametroXML(getParamsResposta(),"RspnRsn");
        if (UteisValidacao.emptyString(rspnRsn)) {
            return obterValorParametroXML(getParamsResposta(),"RjctRsn");
        } else {
            return rspnRsn;
        }
    }

    @Override
    public String getNSU() {
        return obterValorParametroXML(getParamsResposta(),"RcptTxId");
    }

    private String getIdentificadorTransacaoStoneATK() {
        return obterValorParametroXML(getParamsResposta(),"RcptTxId");
    }

    private String getResultadoRequisicaoCancelamento() {
        try {
            String codigo = obterValorParametroXML(getResultadoCancelamento(),"RspnRsn");
            String msgCance = RspnRsn.fromValue(codigo).getMensagem();
            String adiconalMsg = obterValorParametroXML(getResultadoCancelamento(),"AddtlInf");

            String retorno = "";
            if (!UteisValidacao.emptyString(msgCance)) {
                retorno = msgCance;
            }
            if (!UteisValidacao.emptyString(adiconalMsg)) {
                retorno += (" " + adiconalMsg);
            }
            return retorno;
        } catch (Exception ex) {
            ex.printStackTrace();
            return "";
        }
    }

    public String obterValorParametroXML(String xml, String parametro) {
        if (!UteisValidacao.emptyString(xml) && xml.contains("<"+parametro+">")) {
            try {
                String[] split = xml.split("<"+parametro+">");
                String[] split2 = split[1].split("</"+parametro+">");
                String valor = split2[0];
                if (!UteisValidacao.emptyString(valor)) {
                    return valor;
                }
            } catch (Exception ignored) {
            }
        }
        return "";
    }

    public String getTID() {
        return obterValorParametroXML(getParamsResposta(),"TxRef");
    }

    public String getStoneCode() {
        try {
            JSONObject json = XML.toJSONObject(this.getParamsEnvio());
            JSONObject document = json.getJSONObject("Document");
            JSONObject accptrAuthstnReq = document.getJSONObject("AccptrAuthstnReq");
            JSONObject authstnReq = accptrAuthstnReq.getJSONObject("AuthstnReq");
            JSONObject envt = authstnReq.getJSONObject("Envt");
            JSONObject poi = envt.getJSONObject("POI");
            JSONObject id = poi.getJSONObject("Id");
            return id.get("Id").toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public String getSAK() {
        try {
            JSONObject json = XML.toJSONObject(this.getParamsEnvio());
            JSONObject document = json.getJSONObject("Document");
            JSONObject accptrAuthstnReq = document.getJSONObject("AccptrAuthstnReq");
            JSONObject authstnReq = accptrAuthstnReq.getJSONObject("AuthstnReq");
            JSONObject envt = authstnReq.getJSONObject("Envt");
            JSONObject mrchnt = envt.getJSONObject("Mrchnt");
            JSONObject id = mrchnt.getJSONObject("Id");
            return id.get("Id").toString();
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public Integer getNrVezes() {
        try {
            String ttlnbofpmts = obterValorParametroXML(this.getParamsEnvio(), "TtlNbOfPmts");
            return Integer.parseInt(ttlnbofpmts);
        } catch (Exception ex) {
            return 0;
        }
    }
}
