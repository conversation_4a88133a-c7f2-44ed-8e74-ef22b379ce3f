package servicos.impl.stone.bean;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class StoneConciliationCancellation implements Serializable {
    /**
     * Campo					Tipo	Tamanho	Descrição
     * InstallmentNumber		Num 	2 		identificador único da operação de cancelamento
     * OperationKey			    Alfa	32		Identificador da parcela que foi descontada
     * CancellationDateTime	    Datetime14		Data hora do cancelamento (Formato: aaaammddHHmmss)
     * ReturnedAmount			Float	20		Valor revertido e devolvido ao portador do cartão
     * Billing					list    #		Lista de cobranças relativa ao cancelamento
     */

    private static final long serialVersionUID = 1l;

    @SerializedName("CancellationDateTime")
    private String cancellationDate;

    @SerializedName("ReturnedAmount")
    private double returnedAmount;

    @SerializedName("PaymentId")
    private String paymentId;

    @SerializedName("OperationKey")
    private String operationKey;

    @SerializedName("InstallmentNumber")
    private Integer installmentNumber;

    public String getCancellationDate() {
        return cancellationDate;
    }

    public void setCancellationDate(String cancellationDate) {
        this.cancellationDate = cancellationDate;
    }

    public double getReturnedAmount() {
        return returnedAmount;
    }

    public void setReturnedAmount(double returnedAmount) {
        this.returnedAmount = returnedAmount;
    }

    public String getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(String paymentId) {
        this.paymentId = paymentId;
    }

    public String getOperationKey() {
        return operationKey;
    }

    public void setOperationKey(String operationKey) {
        this.operationKey = operationKey;
    }

    public Integer getInstallmentNumber() {
        return installmentNumber;
    }

    public void setInstallmentNumber(Integer installmentNumber) {
        this.installmentNumber = installmentNumber;
    }
}
