package servicos.impl.stone.bean;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.Date;

/**
 * Campo			Tipo	Tamanho		Descrição
 * ChargedAmount	Float	20			Valor de desconto do cancelamento (descontado do lojista)
 * ChargeDate		Datetime8			Data da cobrança (Formato: aaaammdd)
 */
public class StoneConciliationBilling implements Serializable {

    private static final long serialVersionUID = 1l;
    @SerializedName("ChargedAmount")
    private double chargedAmount;

    @SerializedName("ChargeDate")
    private String chargeDate;

    public double getChargedAmount() {
        return chargedAmount;
    }

    public void setChargedAmount(double chargedAmount) {
        this.chargedAmount = chargedAmount;
    }

    public String getChargeDate() {
        return chargeDate;
    }

    public void setChargeDate(String chargeDate) {
        this.chargeDate = chargeDate;
    }
}
