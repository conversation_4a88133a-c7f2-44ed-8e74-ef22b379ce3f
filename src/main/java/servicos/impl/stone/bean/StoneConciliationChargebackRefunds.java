package servicos.impl.stone.bean;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

public class StoneConciliationChargebackRefunds implements Serializable {
    private static final long serialVersionUID = 1l;

    @SerializedName("ChargebackRefund")
    private StoneConciliationChargebackRefund chargebackRefund;

    public StoneConciliationChargebackRefund getChargebackRefund() {
        return chargebackRefund;
    }

    public void setChargebackRefund(StoneConciliationChargebackRefund chargebackRefund) {
        this.chargebackRefund = chargebackRefund;
    }
}
