package servicos.impl.stone.bean;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Recebe objeto Json convertido de XML da integração com a API Stone Conciliação.
 * <br/>
 * <b>Header:</b> Contém as informações do lojista e do arquivo.<br/>
 * <b>FinancialTransactions:</b> Contém as informações do lojista e do arquivo.<br/>
 * <b>FinancialEvents:</b> Contém os eventos financeiros lançados para o lojista no dia.<br/>
 * <b>FinancialTransactionsAccounts:</b> Contém as transações que foram pagas/cobradas ao lojista no dia requisitado.<br/>
 * <b>FinancialEventAccounts:</b> Contém os eventos que foram pagos/cobrados ao lojista no dia requisistado.<br/>
 * <b>Payments:</b> Contém informações dos pagamentos efetuados relativos as transações e eventos financeiros do arquivo.<br/>
 * <b>Trailer:</b> Contém os totalizadores e contadores do arquivo.<br/>
 *
 * <AUTHOR> Christian
 * @see <a href="https://conciliacao.stone.com.br/v2.2/docs">Stone Online (API de Conciliação)</a>
 * @since 12/04/2019
 */
public class StoneConciliation implements Serializable {
    private static final long serialVersionUID = 1l;

    @SerializedName("Header")
    private StoneConciliationHeader header;

    @SerializedName("FinancialTransactions")
    private List<StoneConciliationFinancialTransaction> stoneConciliationFinancialTransactions;

    @SerializedName("FinancialTransactionsAccounts")
    private List<StoneConciliationFinancialTransaction> stoneConciliationFinancialTransactionAccounts;

    public StoneConciliationHeader getHeader() {
        return header;
    }

    public void setHeader(StoneConciliationHeader header) {
        this.header = header;
    }


    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public List<StoneConciliationFinancialTransaction> getStoneConciliationFinancialTransactions() {
        if (stoneConciliationFinancialTransactions == null) {
            stoneConciliationFinancialTransactions = new ArrayList<>();
        }
        return stoneConciliationFinancialTransactions;
    }

    public void setStoneConciliationFinancialTransactions(List<StoneConciliationFinancialTransaction> stoneConciliationFinancialTransactions) {
        this.stoneConciliationFinancialTransactions = stoneConciliationFinancialTransactions;
    }

    public List<StoneConciliationFinancialTransaction> getStoneConciliationFinancialTransactionAccounts() {
        if (stoneConciliationFinancialTransactionAccounts == null) {
            stoneConciliationFinancialTransactionAccounts = new ArrayList<>();
        }
        return stoneConciliationFinancialTransactionAccounts;
    }

    public void setStoneConciliationFinancialTransactionAccounts(List<StoneConciliationFinancialTransaction> stoneConciliationFinancialTransactionAccounts) {
        this.stoneConciliationFinancialTransactionAccounts = stoneConciliationFinancialTransactionAccounts;
    }
}
