package servicos.impl.stone.bean;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 14/04/2019
 */
public class StoneConciliationHeader implements Serializable {
    private static final long serialVersionUID = 1l;

    @SerializedName("StoneCode")
    private String stoneCode;

    @SerializedName("GenerationDateTime")
    private String generationDateTime;

    public String getStoneCode() {
        return stoneCode;
    }

    public void setStoneCode(String stoneCode) {
        this.stoneCode = stoneCode;
    }

    public String getGenerationDateTime() {
        return generationDateTime;
    }

    public void setGenerationDateTime(String generationDateTime) {
        this.generationDateTime = generationDateTime;
    }
}