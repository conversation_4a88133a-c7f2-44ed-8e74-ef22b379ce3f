package servicos.impl.stone.bean;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class StoneConciliationChargeBacks implements Serializable {
    private static final long serialVersionUID = 1l;

    @SerializedName("Chargeback")
    private List<StoneConciliationChargeBack> chargeback;

    public List<StoneConciliationChargeBack> getChargeback() {
        return chargeback;
    }

    public void setChargeback(List<StoneConciliationChargeBack> chargeback) {
        this.chargeback = chargeback;
    }
}
