package servicos.impl.stone.bean;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.Date;

public class StoneConciliationChargeBack implements Serializable {
    private static final long serialVersionUID = 1l;

    @SerializedName("Id")
    private String id;

    @SerializedName("Amount")
    private double amount;

    @SerializedName("Date")
    private String date;

    @SerializedName("ChargeDate")
    private String chargeDate;

    @SerializedName("ReasonCode")
    private String reasonCode;

    @SerializedName("DisputeCondition")
    private String disputeCondition;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getChargeDate() {
        return chargeDate;
    }

    public void setChargeDate(String chargeDate) {
        this.chargeDate = chargeDate;
    }

    public String getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    public String getDisputeCondition() {
        return disputeCondition;
    }

    public void setDisputeCondition(String disputeCondition) {
        this.disputeCondition = disputeCondition;
    }
}
