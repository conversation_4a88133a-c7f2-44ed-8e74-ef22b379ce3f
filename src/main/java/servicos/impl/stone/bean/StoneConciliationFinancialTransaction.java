package servicos.impl.stone.bean;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 14/04/2019
 */
public class StoneConciliationFinancialTransaction implements Serializable {
    private static final long serialVersionUID = 1l;

    @SerializedName("Installments")
    private List<StoneConciliationInstallment> stoneConciliationInstallments;

    @SerializedName("Events")
    private StoneConciliationEvents stoneConciliationEvents;

    @SerializedName("CaptureLocalDateTime")
    private String captureLocalDateTime;

    @SerializedName("IssuerAuthorizationCode")
    private String issuerAuthorizationCode;

    @SerializedName("CardNumber")
    private String cardNumber;

    @SerializedName("AcquirerTransactionKey")
    private String acquirerTransactionKey;

    @SerializedName("NumberOfInstallments")
    private int numberOfInstallments;

    @SerializedName("AccountType")
    private int accountType;

    @SerializedName("InstallmentType")
    private int installmentType;

    @SerializedName("Cancellations")
    private List<StoneConciliationCancellation> stoneConciliationCancellations;

    public List<StoneConciliationInstallment> getStoneConciliationInstallments() {
        return stoneConciliationInstallments;
    }

    public void setStoneConciliationInstallments(List<StoneConciliationInstallment> stoneConciliationInstallments) {
        this.stoneConciliationInstallments = stoneConciliationInstallments;
    }

    public StoneConciliationEvents getStoneConciliationEvents() {
        return stoneConciliationEvents;
    }

    public void setStoneConciliationEvents(StoneConciliationEvents stoneConciliationEvents) {
        this.stoneConciliationEvents = stoneConciliationEvents;
    }

    public String getCaptureLocalDateTime() {
        return captureLocalDateTime;
    }

    public void setCaptureLocalDateTime(String captureLocalDateTime) {
        this.captureLocalDateTime = captureLocalDateTime;
    }

    public String getIssuerAuthorizationCode() {
        return issuerAuthorizationCode;
    }

    public void setIssuerAuthorizationCode(String issuerAuthorizationCode) {
        this.issuerAuthorizationCode = issuerAuthorizationCode;
    }

    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    public String getAcquirerTransactionKey() {
        return acquirerTransactionKey;
    }

    public void setAcquirerTransactionKey(String acquirerTransactionKey) {
        this.acquirerTransactionKey = acquirerTransactionKey;
    }

    public int getNumberOfInstallments() {
        return numberOfInstallments;
    }

    public void setNumberOfInstallments(int numberOfInstallments) {
        this.numberOfInstallments = numberOfInstallments;
    }

    public int getAccountType() {
        return accountType;
    }

    public void setAccountType(int accountType) {
        this.accountType = accountType;
    }

    public List<StoneConciliationCancellation> getStoneConciliationCancellations() {
        return stoneConciliationCancellations;
    }

    public void setStoneConciliationCancellations(List<StoneConciliationCancellation> stoneConciliationCancellations) {
        this.stoneConciliationCancellations = stoneConciliationCancellations;
    }

    public int getInstallmentType() {
        return installmentType;
    }

    public void setInstallmentType(int installmentType) {
        this.installmentType = installmentType;
    }
}
