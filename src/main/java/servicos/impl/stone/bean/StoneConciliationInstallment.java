package servicos.impl.stone.bean;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 14/04/2019
 */
public class StoneConciliationInstallment implements Serializable {
    private static final long serialVersionUID = 1l;

    @SerializedName("PrevisionPaymentDate")
    private String previsionPaymentDate;

    @SerializedName("PaymentDate")
    private String paymentDate;

    @SerializedName("GrossAmount")
    private double grossAmount;

    @SerializedName("NetAmount")
    private double netAmount;

    @SerializedName("InstallmentNumber")
    private int installmentNumber;

    @SerializedName("OriginalPaymentDate")
    private String originalPaymentDate;

    @SerializedName("AdvancedReceivableOriginalPaymentDate")
    private String advancedReceivableOriginalPaymentDate;

    @SerializedName("AdvanceRateAmount")
    private Double advanceRateAmount;

    @SerializedName("Chargebacks")
    private StoneConciliationChargeBacks chargeBacks;

    @SerializedName("ChargebackRefunds")
    private StoneConciliationChargebackRefunds chargebackRefunds;

    public String getOriginalPaymentDate() {
        return originalPaymentDate;
    }

    public void setOriginalPaymentDate(String originalPaymentDate) {
        this.originalPaymentDate = originalPaymentDate;
    }

    public String getPrevisionPaymentDate() {
        return previsionPaymentDate;
    }

    public void setPrevisionPaymentDate(String previsionPaymentDate) {
        this.previsionPaymentDate = previsionPaymentDate;
    }

    public double getGrossAmount() {
        return grossAmount;
    }

    public void setGrossAmount(double grossAmount) {
        this.grossAmount = grossAmount;
    }

    public double getNetAmount() {
        return netAmount;
    }

    public void setNetAmount(double netAmount) {
        this.netAmount = netAmount;
    }

    public int getInstallmentNumber() {
        return installmentNumber;
    }

    public void setInstallmentNumber(int installmentNumber) {
        this.installmentNumber = installmentNumber;
    }

    public String getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(String paymentDate) {
        this.paymentDate = paymentDate;
    }

    public StoneConciliationChargeBacks getChargeBacks() {
        return chargeBacks;
    }

    public void setChargeBacks(StoneConciliationChargeBacks chargeBacks) {
        this.chargeBacks = chargeBacks;
    }

    public String getAdvancedReceivableOriginalPaymentDate() {
        return advancedReceivableOriginalPaymentDate;
    }

    public void setAdvancedReceivableOriginalPaymentDate(String advancedReceivableOriginalPaymentDate) {
        this.advancedReceivableOriginalPaymentDate = advancedReceivableOriginalPaymentDate;
    }

    public Double getAdvanceRateAmount() {
        return advanceRateAmount;
    }

    public void setAdvanceRateAmount(Double advanceRateAmount) {
        this.advanceRateAmount = advanceRateAmount;
    }

    public StoneConciliationChargebackRefunds getChargebackRefunds() {
        return chargebackRefunds;
    }

    public void setChargebackRefunds(StoneConciliationChargebackRefunds chargebackRefunds) {
        this.chargebackRefunds = chargebackRefunds;
    }
}
