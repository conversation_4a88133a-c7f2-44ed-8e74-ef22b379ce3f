package servicos.impl.stone.bean;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 17/04/2019
 */
public class StoneConciliationTrailer implements Serializable {
    private static final long serialVersionUID = 1l;

    @SerializedName("CapturedTransactionsQuantity")
    private int capturedTransactionsQuantity;

    public int getCapturedTransactionsQuantity() {
        return capturedTransactionsQuantity;
    }

    public void setCapturedTransactionsQuantity(int capturedTransactionsQuantity) {
        this.capturedTransactionsQuantity = capturedTransactionsQuantity;
    }
}