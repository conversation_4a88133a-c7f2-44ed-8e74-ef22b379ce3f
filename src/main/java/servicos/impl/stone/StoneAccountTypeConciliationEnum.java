package servicos.impl.stone;

import negocio.comuns.financeiro.enumerador.CodigoRetornoPactoEnum;
import negocio.comuns.financeiro.enumerador.OperacaoRetornoCobrancaEnum;

/**
 * Created with IntelliJ IDEA.
 * User: <PERSON><PERSON>
 * Date: 01/05/2020
 */
public enum StoneAccountTypeConciliationEnum {

    Nenhum(0, "Nenhum"),
    Debit(1, "Débito"),
    Credit(2, "Crédito"),
    PrepaidDebit(3, "Pré-pago Débito"),
    PrepaidCredit(4, "Pré-pago Crédito");

    private Integer id;
    private String descricao;

    private StoneAccountTypeConciliationEnum(Integer id, String descricao) {
        this.id = id;
        this.descricao = descricao;
    }

    public Integer getId() {
        return id;
    }

    public String getDescricao() {
        return descricao;
    }

    public static StoneAccountTypeConciliationEnum valueOff(Integer id) {
        for (StoneAccountTypeConciliationEnum stone : StoneAccountTypeConciliationEnum.values()) {
            if (stone.getId().equals(id)) {
                return stone;
            }
        }
        return Nenhum;
    }

}
