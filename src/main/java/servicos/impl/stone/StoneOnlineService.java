package servicos.impl.stone;

import br.com.pactosolucoes.comuns.notificacao.RecursoSistema;
import br.com.pactosolucoes.comuns.to.CartaoCreditoTO;
import controle.arquitetura.exceptions.CobrancaException;
import negocio.comuns.basico.ClienteVO;
import negocio.comuns.contrato.ContratoRecorrenciaVO;
import negocio.comuns.financeiro.ConvenioCobrancaVO;
import negocio.comuns.financeiro.TransacaoVO;
import negocio.comuns.financeiro.enumerador.AmbienteEnum;
import negocio.comuns.financeiro.enumerador.AtributoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.SituacaoTransacaoEnum;
import negocio.comuns.financeiro.enumerador.TipoTransacaoEnum;
import negocio.comuns.utilitarias.Calendario;
import negocio.comuns.utilitarias.ConsistirException;
import negocio.comuns.utilitarias.Uteis;
import negocio.comuns.utilitarias.UteisValidacao;
import negocio.facade.jdbc.financeiro.ConvenioCobranca;
import negocio.facade.jdbc.financeiro.Transacao;
import negocio.interfaces.financeiro.TransacaoInterfaceFacade;
import org.apache.commons.io.Charsets;
import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;
import org.json.XML;
import servicos.http.MetodoHttpEnum;
import servicos.http.RequestHttpService;
import servicos.http.RespostaHttpDTO;
import servicos.impl.apf.APF;
import servicos.impl.gatewaypagamento.AbstractCobrancaOnlineServiceComum;
import servicos.impl.stone.xml.AbstractStoneElementXML;
import servicos.impl.stone.xml.StoneException;
import servicos.impl.stone.xml.authorization.request.DocumentAcceptorAuthorisationRequestV02_1;
import servicos.impl.stone.xml.authorization.request.DocumentAcceptorAuthorisationRequestV02_1Builder;
import servicos.impl.stone.xml.authorization.request.TransactionTx;
import servicos.impl.stone.xml.authorization.response.DocumentAcceptorAuthorisationResponseV02_1;
import servicos.impl.stone.xml.authorization.response.rejection.DocumentAcceptorRejectionV02_1;
import servicos.impl.stone.xml.cancellation.request.DocumentAcceptorCancellationRequestV0_21;
import servicos.impl.stone.xml.cancellation.request.DocumentAcceptorCancellationRequestV0_21Builder;
import servicos.impl.stone.xml.cancellation.response.DocumentAcceptorCancellationResponseV02_1;
import servicos.interfaces.StoneOnlineServiceInterface;
import servicos.propriedades.PropsService;
import servicos.util.ExecuteRequestHttpService;
import servicos.util.RequestException;

import java.sql.Connection;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.Arrays;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.pactosolucoes.comuns.notificacao.RecursoSistema.STONE_TRANSACAO_AUTORIZACAO_FALHA_ENVIO;
import static br.com.pactosolucoes.comuns.notificacao.RecursoSistema.STONE_TRANSACAO_AUTORIZACAO_PEDIDO;
import static br.com.pactosolucoes.comuns.notificacao.RecursoSistema.STONE_TRANSACAO_AUTORIZACAO_REJEITADO;
import static br.com.pactosolucoes.comuns.notificacao.RecursoSistema.STONE_TRANSACAO_AUTORIZACAO_RESPOSTA_SUCESSO;
import static br.com.pactosolucoes.comuns.notificacao.RecursoSistema.STONE_TRANSACAO_CANCELAMENTO_FALHA_ENVIO;
import static br.com.pactosolucoes.comuns.notificacao.RecursoSistema.STONE_TRANSACAO_CANCELAMENTO_PEDIDO;
import static br.com.pactosolucoes.comuns.notificacao.RecursoSistema.STONE_TRANSACAO_CANCELAMENTO_REJEITADO;
import static br.com.pactosolucoes.comuns.notificacao.RecursoSistema.STONE_TRANSACAO_CANCELAMENTO_RESPOSTA_SUCESSO;
import static br.com.pactosolucoes.comuns.util.StringUtilities.formatarCampoMonetario;
import static negocio.comuns.utilitarias.Calendario.hoje;
import static negocio.comuns.utilitarias.Uteis.getDataAplicandoFormatacao;
import static servicos.impl.stone.xml.AbstractStoneElementXML.tryConverterFromXMLByNameSpace;
import static servicos.util.ExecuteRequestHttpService.METODO_POST;

/**
 * Serviço responsável por realizar toda comunicação/integração com a API do <b>E-Commerce da Stone</b>.
 *
 * <p>
 * Veja coleção da API de HOMOLOGAÇÃO da STONE no postman em: <b>./doc/stone/postman</b>
 * </p>
 *
 * <AUTHOR> Cattany
 * @see <a href="https://online.stone.com.br/reference#introducao">Stone Online (E-Commerce)</a>
 * @since 26/02/2019
 */
public class StoneOnlineService extends AbstractCobrancaOnlineServiceComum implements StoneOnlineServiceInterface {

    private static final List<String> ERROS_COMUNICACAO = Arrays.asList(
            "502 Bad Gateway",
            "error code: 502"
    );

    private String URL_API_STONE = "";
    private String stoneCode;
    private String saleAffiliationKeySAK;

    private Transacao transacaoDAO;
    private ConvenioCobranca convenioCobrancaDAO;
    private ConvenioCobrancaVO convenioStoneOnline;

    public StoneOnlineService(Connection con, Integer empresa, Integer convenioCobranca) throws Exception {
        super(con);
        this.transacaoDAO = new Transacao(con);
        this.convenioCobrancaDAO = new ConvenioCobranca(con);
        this.convenioStoneOnline = this.convenioCobrancaDAO.consultarPorCodigoEmpresa(convenioCobranca, empresa, Uteis.NIVELMONTARDADOS_CONEXAOESPECIFICA);
        popularInformacoes();
    }

    private void popularInformacoes() {
        if (this.convenioStoneOnline != null) {
            this.stoneCode = convenioStoneOnline.getCodigoAutenticacao01();
            this.saleAffiliationKeySAK = convenioStoneOnline.getCodigoAutenticacao02();

            if (this.convenioStoneOnline.getAmbiente().equals(AmbienteEnum.PRODUCAO)) {
                this.URL_API_STONE = PropsService.getPropertyValue(PropsService.urlApiStoneProducao);
            } else {
                this.URL_API_STONE = PropsService.getPropertyValue(PropsService.urlApiStoneSandbox);
            }
        }
    }

    @Override
    public TransacaoInterfaceFacade getTransacaoFacade() {
        return this.transacaoDAO;
    }

    @Override
    public TransacaoVO tentarAprovacao(CartaoCreditoTO dadosCartao) throws Exception {
        TransacaoVO transacao = null;
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());

            transacao = criarTransacao(dadosCartao, new TransacaoStoneOnlineVO(), TipoTransacaoEnum.STONE_ONLINE, convenioStoneOnline);
            transacaoDAO.incluir(transacao);

            Date dataTransacao = hoje();
            DocumentAcceptorAuthorisationRequestV02_1 documentAcceptorAuthorisationRequest = criarParametrosEnvioAutorizacao(dadosCartao, dataTransacao);

            documentAcceptorAuthorisationRequest.setIdentificacaoTransacaoSistemaITK(
                    transacao.getCodigo().toString(),
                    dataTransacao
            );

            documentAcceptorAuthorisationRequest.setIdentificadorTransacaoNaoArmazenadoPelaStone(
                    documentAcceptorAuthorisationRequest.getIdentificacaoTransacaoSistemaITK()
            );

            System.out.println("IDENTIFICADOR STONE: " + documentAcceptorAuthorisationRequest.getIdentificacaoTransacaoSistemaITK());
            System.out.println("IDENTIFICADOR STONE (De maneira legível): " + documentAcceptorAuthorisationRequest.getIdentificacaoTransacaoStonePretty());

            String paramsEnvio = documentAcceptorAuthorisationRequest.toXML();
            paramsEnvio = paramsEnvio.replaceAll("(?m)^\\s*<Rsn>4000</Rsn>\\s*\\n?", ""); //Esse trecho é retirado, pois ele é utilizado no Cancelamento

            //salvar para ser apresentado no gestão de transação
            gravarOutrasInformacoes(dadosCartao.getNumero(), dadosCartao, transacao);

            //encriptar Dados Sigilosos e gravar na transação
            documentAcceptorAuthorisationRequest.encriptarDadosSigilososEnvio();
            transacao.setParamsEnvio(documentAcceptorAuthorisationRequest.toXML());
            transacaoDAO.alterar(transacao);

            validarDadosTransacao(transacao, dadosCartao);

            notificarRecursoEmpresaStone(STONE_TRANSACAO_AUTORIZACAO_PEDIDO);
            String retornoXML = "";
            try {
                retornoXML = executarRequestStone(transacao, paramsEnvio, AUTHORIZE_ENDPOINT);
                if (transacao.isTransacaoVerificarCartao() && dadosCartao.isVerificacaoZeroDollar()) {
                    processarRetornoAutorizacaoZeroDollar(transacao, retornoXML);
                } else {
                    processarRetornoAutorizacao(transacao, retornoXML, paramsEnvio);
                }
            } catch (StoneErroComunicacaoAPIException e) {
                e.printStackTrace();
                notificarRecursoEmpresaStone(STONE_TRANSACAO_AUTORIZACAO_FALHA_ENVIO);
                tratarFalhaComunicacaoAPIStone(e, transacao, transacaoDAO);
                Logger.getLogger(StoneOnlineService.class.getName()).log(Level.WARNING, null, e);
            }  catch (RequestException e2) { // só entra aqui quando dá erro na requisição com o server mock
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
                Logger.getLogger(StoneOnlineService.class.getName()).log(Level.WARNING, null, e2);
                transacaoDAO.alterarMessagemErro(transacao, e2.getMessage());
                transacao.setErroProcessamento(e2.getMessage());
            } catch (ConsistirException e3) {
                //cai aqui quando a exceção é de txref de envio diferente do txref de retorno
                incluirHistoricoRetornoTransacao(transacao, retornoXML, "tentarAprovacao-Exception-ConsistirException");
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
                Logger.getLogger(StoneOnlineService.class.getName()).log(Level.WARNING, null, e3);
                transacaoDAO.alterarMessagemErro(transacao, e3.getMessage());
                transacao.setErroProcessamento(e3.getMessage());
            } catch (Exception e4) {
                e4.printStackTrace();
                incluirHistoricoRetornoTransacao(transacao, retornoXML, "tentarAprovacao-Exception");
                incluirHistoricoRetornoTransacao(transacao, e4.getMessage(), "tentarAprovacao-Exception-Message");
                tratarErroGenerico(transacao);
                Logger.getLogger(StoneOnlineService.class.getName()).log(Level.WARNING, null, e4);
            }

            preencherOutrasInformacoes(transacao);
            transacaoDAO.alterar(transacao);
        } catch (CobrancaException ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex);
            verificarException(transacao, ex);
        } catch (Exception ex) {
            ex.printStackTrace();
            marcarTransacaoComErro(transacao, ex.getMessage());
            verificarException(transacao, ex);
        } finally {
            gravarTentativaCartao(transacao);
            transacaoDAO = null;
        }
        return transacao;
    }

    private void tratarFalhaComunicacaoAPIStone(StoneErroComunicacaoAPIException exception, TransacaoVO transacao, Transacao transacaoDAO) throws Exception {
        incluirHistoricoRetornoTransacao(transacao, exception.getMessage(), "tratarFalhaComunicacaoAPIStone");
        String erroMsg = "Houve algum problema de comunicação com o E-Commerce da Stone. Tente novamente mais tarde!";
        Uteis.logar(exception, getClass());
        verificarTratarRetornoAprovado(transacao);
        transacaoDAO.alterarMessagemErro(transacao, erroMsg);
        transacao.setErroProcessamento(erroMsg);
    }

    private void tratarErroGenerico(TransacaoVO transacao) {
        verificarTratarRetornoAprovado(transacao);
    }

    public void verificarTratarRetornoAprovado(TransacaoVO transacao){
        boolean naoContemErroComunicacao = true;
        for (String erro : ERROS_COMUNICACAO) {
            if (transacao.getParamsResposta().contains(erro)) {
                naoContemErroComunicacao = false;
                break;
            }
        }

        if (!UteisValidacao.emptyString(transacao.getParamsResposta()) && naoContemErroComunicacao) {
            incluirHistoricoRetornoTransacao(transacao, transacao.getParamsResposta(), "verificarTratarRetornoAprovado");
            if (transacao.getParamsResposta().contains("<MsgCntt>Aprovado</MsgCntt>")) {
                transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
            } else {
                transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
            }
        } else {
            //Se não tivermos retorno da stone ou tiver um retorno conhecido e erro de comunicação, colocar a transação em azul (Aprovada - Aguardando Confirmação)
            //Ela fica azul até que o processo da classe ProcessoIdentificarCobrancasStoneAprovadasInterrogacao seja executado
            // no dia seguinte e verifique se ela foi paga ou não de fato.
            transacao.setSituacao(SituacaoTransacaoEnum.APROVADA); //AZUL
        }
    }

    private DocumentAcceptorAuthorisationRequestV02_1 criarParametrosEnvioAutorizacao(CartaoCreditoTO dadosCartao,
                                                                                      Date dataTransacao) {

        if(StringUtils.isBlank(dadosCartao.getCodigoSeguranca())){
            return DocumentAcceptorAuthorisationRequestV02_1Builder.init(dadosCartao)
                    .comSaleAffiliationKeySAK(saleAffiliationKeySAK)
                    .comNomeQueApareceraNaFatura(convenioStoneOnline.getEmpresa().getRazaoSocialParaSoftDescriptor(dadosCartao.isTransacaoVerificarCartao()))
                    .comCodigoIdentificacaoDoPontoDeInteracao(stoneCode)
                    .comNumeroDoCartao(dadosCartao.getNumero())
                    .comDataExpiracaoAno(dadosCartao.getAnoValidadeYYYY())
                    .comDataExpiracaoMes(dadosCartao.getValidade().substring(0, 2))
                    .comDataHoraLocalDaTransacaoDoPontoDeInteracao(
                            getDataAplicandoFormatacao(dataTransacao, "yyyy-MM-dd'T'HH:mm:ss")
                    )
                    .comValorTotalTransacaoEmCentavos(
                            formatarCampoMonetario(dadosCartao.getValor(), 0)
                    )
                    .comTipoParcelamento(dadosCartao.getTipoParcelamentoStone())
                    .comQuantidadeDeParcelas(
                            dadosCartao.getParcelas()
                    )
                    .build();
        } else {
            return DocumentAcceptorAuthorisationRequestV02_1Builder.init(dadosCartao)
                    .comSaleAffiliationKeySAK(saleAffiliationKeySAK)
                    .comNomeQueApareceraNaFatura(convenioStoneOnline.getEmpresa().getRazaoSocialParaSoftDescriptor(dadosCartao.isTransacaoVerificarCartao()))
                    .comCodigoIdentificacaoDoPontoDeInteracao(stoneCode)
                    .comNumeroDoCartao(dadosCartao.getNumero())
                    .comDataExpiracaoAno(dadosCartao.getAnoValidadeYYYY())
                    .comDataExpiracaoMes(dadosCartao.getValidade().substring(0, 2))
                    .comCodigoValidador(dadosCartao.getCodigoSeguranca())
                    .comDataHoraLocalDaTransacaoDoPontoDeInteracao(
                            getDataAplicandoFormatacao(dataTransacao, "yyyy-MM-dd'T'HH:mm:ss")
                    )
                    .comValorTotalTransacaoEmCentavos(
                            formatarCampoMonetario(dadosCartao.getValor(), 0)
                    )
                    .comTipoParcelamento(dadosCartao.getTipoParcelamentoStone())
                    .comQuantidadeDeParcelas(
                            dadosCartao.getParcelas()
                    )
                    .build();
        }
    }

    private void processarRetornoAutorizacao(TransacaoVO transacao, String retornoXML, String envioXML) throws InstantiationException, IllegalAccessException, StoneErroComunicacaoAPIException, ConsistirException {
        incluirHistoricoRetornoTransacao(transacao, retornoXML, "processarRetornoAutorizacao");
        transacao.setParamsResposta(retornoXML);
        try {
            if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
                transacao.setCodigoExterno(transacao.getValorAtributoResposta(APF.Transacao));
            }
        } catch (Exception e) {
            //ignore
        }

        contemErrosComunicacaoStone(retornoXML);

        AbstractStoneElementXML documentResponse = tryConverterFromXMLByNameSpace(retornoXML);

        if (documentResponse.isAuthorisationResponse()) {
            String txRefEnvio = obterTxRefEnvio(envioXML);
            processarRetornoAutorizacaoSucesso(transacao, documentResponse.toAuthorisationResponse(), txRefEnvio);
        } else if (documentResponse.isRejection()) {
            processarRetornoRejeicaoAutorizacao(transacao, documentResponse.toRejection());
        }
    }

    private String obterTxRefEnvio(String envioXML) {
        try {
            AbstractStoneElementXML envio = tryConverterFromXMLByNameSpace(envioXML);
            return envio.toAuthorisationRequest().getTransactionReferenceTxRef();
        } catch (Exception ex) {
            ex.printStackTrace();
            Uteis.logarDebug(ex.getMessage());
            return "";
        }
    }

    private void contemErrosComunicacaoStone(String retornoXML) throws StoneErroComunicacaoAPIException {
        if (UteisValidacao.emptyString(retornoXML)) {
            throw new StoneErroComunicacaoAPIException(new Exception(retornoXML));
        }
        for (String erro : ERROS_COMUNICACAO) {
            if (retornoXML.contains(erro)) {
                throw new StoneErroComunicacaoAPIException(new Exception(retornoXML));
            }
        }
    }

    private void processarRetornoAutorizacaoZeroDollar(TransacaoVO transacao, String retornoXML) throws InstantiationException, IllegalAccessException {
        incluirHistoricoRetornoTransacao(transacao, retornoXML, "processarRetornoAutorizacaoZeroDollar");
        transacao.setParamsResposta(retornoXML);
        try {
            if (UteisValidacao.emptyString(transacao.getCodigoExterno())) {
                transacao.setCodigoExterno(transacao.getValorAtributoResposta(APF.Transacao));
            }
        } catch (Exception e) {
            //ignore
        }

        JSONObject jsonObject = XML.toJSONObject(retornoXML);

        String codAutorizacao = "";
        try {
            codAutorizacao = jsonObject.optJSONObject("Document").optJSONObject("AccptrAuthstnRspn").optJSONObject("AuthstnRspn").optJSONObject("TxRspn").optJSONObject("AuthstnRslt").optString("AuthstnCd");
            transacao.setCodigoAutorizacao(codAutorizacao);
        } catch (Exception e) {
            //ignore
        }

        boolean verificacaoAprovada = jsonObject.optJSONObject("Document").optJSONObject("AccptrAuthstnRspn").optJSONObject("AuthstnRspn").optJSONObject("TxRspn").optJSONObject("Actn").optJSONObject("MsgToPres").optString("MsgCntt").equalsIgnoreCase("Aprovado") &&
                jsonObject.optJSONObject("Document").optJSONObject("AccptrAuthstnRspn").optJSONObject("AuthstnRspn").optJSONObject("TxRspn").optJSONObject("AuthstnRslt").optJSONObject("RspnToAuthstn").optString("Rspn").equalsIgnoreCase("APPR");
        if (verificacaoAprovada) {
            transacao.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
        } else {
            transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
        }

    }

    private void processarRetornoAutorizacaoSucesso(TransacaoVO transacao, DocumentAcceptorAuthorisationResponseV02_1 documentAuthorisationResponse, String txRefEnvio) throws ConsistirException {
        transacao.setSituacao(documentAuthorisationResponse.getRespostaTransacao().toSituacaoTransacaoEnumParaRetornoAutorizacao());
        if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO) && !UteisValidacao.emptyString(txRefEnvio)) {
            //verificações adicionais de integridade para saber se ela realmente foi aprovada ou não.
            String txRefRetorno = "";
            try {
                txRefRetorno = documentAuthorisationResponse.getTransactionReferenceTxRef();
            } catch (Exception ex) {
                ex.printStackTrace();
                Uteis.logarDebug(ex.getMessage());
            }
            if (!UteisValidacao.emptyString(txRefRetorno) && !UteisValidacao.emptyString(txRefEnvio)
                    && !txRefRetorno.equals(txRefEnvio)) {
                transacao.setSituacao(SituacaoTransacaoEnum.APROVADA);
                try {
                    transacao.setCodigoAutorizacao(documentAuthorisationResponse.getCodigoAutorizacao());
                    transacao.setCodigoExterno(documentAuthorisationResponse.getIdentificadorTransacaoStoneATK());
                } catch (Exception ex) {
                }
                throw new ConsistirException("Stone nos enviou uma transação diferente na resposta. Favor entre em contato com a Pacto para verificarmos.");
            }
        }
        transacao.setCodigoAutorizacao(documentAuthorisationResponse.getCodigoAutorizacao());
        transacao.setCodigoExterno(documentAuthorisationResponse.getIdentificadorTransacaoStoneATK());

        notificarRecursoEmpresaStone(STONE_TRANSACAO_AUTORIZACAO_RESPOSTA_SUCESSO);
        notificarRecursoEmpresaStone(documentAuthorisationResponse.getRespostaTransacao().toRecursoSistemaAutorizacao());
    }

    private void processarRetornoRejeicaoAutorizacao(TransacaoVO transacao, DocumentAcceptorRejectionV02_1 documentAuthorisationRejection) {
        transacao.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
        logarRejeicao(documentAuthorisationRejection);

        notificarRecursoEmpresaStone(STONE_TRANSACAO_AUTORIZACAO_REJEITADO);
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacao, Boolean estornarRecibo) throws Exception {
        return cancelarTransacao(transacao, estornarRecibo, null);
    }

    @Override
    public TransacaoVO cancelarTransacao(TransacaoVO transacaoOriginal) throws Exception {
        return null;
    }

    public TransacaoVO cancelarTransacaoComdentificadorStone(TransacaoVO transacao, Boolean estornarRecibo, String identificadorStone) throws Exception {
        return cancelarTransacao(transacao, estornarRecibo, identificadorStone);
    }

    public TransacaoVO cancelarTransacao(TransacaoVO transacao, Boolean estornarRecibo, String identificadorStone) throws Exception {
        Transacao transacaoDAO = null;
        try {
            transacaoDAO = new Transacao(getCon());
            notificarRecursoEmpresaStone(STONE_TRANSACAO_CANCELAMENTO_PEDIDO);

            String response = "";
            try {
                response = executarRequestStone(transacao, criarParametrosEnvioCancelamento(transacao, identificadorStone).toXML(), CANCELLATION_ENDPOINT);
                processarRetornoCancelamento(transacao, response);
            } catch (StoneErroComunicacaoAPIException e) {
                e.printStackTrace();
                incluirHistoricoRetornoTransacao(transacao, e.getMessage(), "cancelarTransacao-StoneErro1");
                if (!UteisValidacao.emptyString(response)) {
                    incluirHistoricoRetornoTransacao(transacao, response, "cancelarTransacao-StoneErro2");
                }
                notificarRecursoEmpresaStone(STONE_TRANSACAO_CANCELAMENTO_FALHA_ENVIO);
            }

            if (transacao.getSituacao().equals(SituacaoTransacaoEnum.CANCELADA) && estornarRecibo && transacao.getReciboPagamento() != 0) {
                estornarRecibo(transacao, estornarRecibo);
                if (transacao.getSituacao().equals(SituacaoTransacaoEnum.ESTORNADA)) {
                    transacao.setSituacao(SituacaoTransacaoEnum.CANCELADA);
                }
            }

            transacaoDAO.alterar(transacao);
        } finally {
            transacaoDAO = null;
        }
        return transacao;
    }

    private DocumentAcceptorCancellationRequestV0_21 criarParametrosEnvioCancelamento(TransacaoVO transacao, String identificadorStone) throws Exception {
        DocumentAcceptorAuthorisationRequestV02_1 documentAuthorisationRequest = tryConverterFromXMLByNameSpace(transacao.getParamsEnvio());

        if (UteisValidacao.emptyString(identificadorStone)) {
            try {
                DocumentAcceptorAuthorisationResponseV02_1 documentAuthorisationResponse = tryConverterFromXMLByNameSpace(transacao.getParamsResposta());
                identificadorStone = documentAuthorisationResponse.getIdentificadorTransacaoStoneATK();
            } catch (Exception ex) {
                if (!UteisValidacao.emptyString(transacao.getCodigoExterno())) {
                    identificadorStone = transacao.getCodigoExterno();
                } else {
                    throw ex;
                }
            }
        }

        if (UteisValidacao.emptyString(identificadorStone)) {
            throw new Exception("IdentificadorTransacaoStoneATK não encontrado");
        }


        return DocumentAcceptorCancellationRequestV0_21Builder.init()
                .comSaleAffiliationKeySAK(obterSAK(transacao))
                .comCodigoIdentificacaoDoPontoDeInteracao(obterStoneCode(transacao))
                .comDataHoraLocalDaTransacaoDoPontoDeInteracao(
                        documentAuthorisationRequest.getDataHoraLocalTransacaoPontoDeInteracao()
                )
                .comIdentificacaoDaTransacaoPontoDeInteracao(
                        documentAuthorisationRequest.getIdentificadorTransacaoNaoArmazenadoPelaStone()
                )
                .comValorTotalTransacaoEmCentavosASerCancelado(
                        documentAuthorisationRequest.getValorTotalTransacaoEmCentavos()
                )
                .comIdentificacaoTransacaoSistemaITK(
                        documentAuthorisationRequest.getIdentificacaoTransacaoSistemaITK()
                )
                .comIdentificadorTransacaoStoneATK(
                        identificadorStone
                )
                .build();
    }

    private String obterStoneCode(TransacaoVO transacaoVO) {
        String code = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.codigoAutenticacao01);
        if (UteisValidacao.emptyString(code)) {
            return stoneCode;
        }
        return code;
    }

    private String obterSAK(TransacaoVO transacaoVO) {
        String sak = transacaoVO.obterItemOutrasInformacoes(AtributoTransacaoEnum.codigoAutenticacao02);
        if (UteisValidacao.emptyString(sak)) {
            return saleAffiliationKeySAK;
        }
        return sak;
    }

    private void processarRetornoCancelamento(TransacaoVO transacao, String retornoXML) throws IllegalAccessException, InstantiationException {
        incluirHistoricoRetornoTransacao(transacao, retornoXML, "processarRetornoCancelamento");
        transacao.setResultadoCancelamento(retornoXML);
        AbstractStoneElementXML documentResponse = tryConverterFromXMLByNameSpace(retornoXML);

        if (documentResponse instanceof DocumentAcceptorCancellationResponseV02_1) {
            processarRetornoCancelamentoSucesso(transacao, documentResponse.toCancellationResponse());
        } else if (documentResponse instanceof DocumentAcceptorRejectionV02_1) {
            processarRetornoRejeicaoCancelamento(documentResponse.toRejection());
        }
    }

    private void processarRetornoRejeicaoCancelamento(DocumentAcceptorRejectionV02_1 documentAuthorisationRejection) {
        logarRejeicao(documentAuthorisationRejection);
        notificarRecursoEmpresaStone(STONE_TRANSACAO_CANCELAMENTO_REJEITADO);
    }

    private void processarRetornoCancelamentoSucesso(TransacaoVO transacao, DocumentAcceptorCancellationResponseV02_1 cancellationResponse) {
        transacao.setSituacao(
                cancellationResponse.getRespostaTransacao().toSituacaoTransacaoEnumParaRetornoCancelamento(transacao.getSituacao())
        );
        transacao.setDataHoraCancelamento(Calendario.hoje());

        notificarRecursoEmpresaStone(STONE_TRANSACAO_CANCELAMENTO_RESPOSTA_SUCESSO);
        notificarRecursoEmpresaStone(cancellationResponse.getRespostaTransacao().toRecursoSistemaCancelamento());
    }

    private void validarDadosConvenio() throws Exception {
        if (this.convenioStoneOnline == null || UteisValidacao.emptyNumber(this.convenioStoneOnline.getCodigo())) {
            throw new Exception("Convênio de cobrança não encontrado ou inativo.");
        }
        if (UteisValidacao.emptyString(this.stoneCode)) {
            throw new Exception("StoneCode no convênio de cobrança não informado.");
        }
        if (UteisValidacao.emptyString(this.saleAffiliationKeySAK)) {
            throw new Exception("SAK (SaleAffiliationKey) no convênio de cobrança não informado.");
        }
    }

    private String executarRequestStone(TransacaoVO transacaoVO, String xmlBody, String endPoint) throws Exception {
        validarDadosConvenio();

        Map<String, String> headers = new HashMap<String, String>();
        headers.put("Content-Type", APPLICATION_XML);

        String fullUrlRequest = URL_API_STONE + endPoint;

        long inicio = 0;
        long tempoDecorrido = 0;
        int statusServer = 0;
        RespostaHttpDTO respostaHttpDTO = null;

        boolean usarMockStone = PropsService.isTrue(PropsService.usarMockStone);
        boolean deuErroNaRequisicaoDoMock = false;
        try {
            if (usarMockStone) {
                Uteis.logarDebug("Vou tentar cobrar a transação de código: " + transacaoVO.getCodigo()  + " | StoneCode: " +
                        transacaoVO.getConvenioCobrancaVO() != null ? transacaoVO.getConvenioCobrancaVO().getCodigoAutenticacao01() : "");
                Uteis.logarDebug("## MOCK DA STONE ATIVADO ##");
                inicio = System.currentTimeMillis();

                String retorno = "";
                try {
                    //request para o mock server é de 63 segundos. Lá dentro do mock server a request tem um timeout de 60 segundos
                    retorno = ExecuteRequestHttpService.executeHttpRequestMockStone(fullUrlRequest, xmlBody, headers, METODO_POST, Charsets.UTF_8.name(), false,
                            10000, 63000);
                } catch (Exception ex) {
                    deuErroNaRequisicaoDoMock = true;
                    throw ex;
                }


                tempoDecorrido = System.currentTimeMillis() - inicio;
                Uteis.logarDebug("Tempo de resposta da Stone: " + tempoDecorrido + "ms" + " | statusServer: " + statusServer);
                incluirHistoricoRetornoTransacao(transacaoVO, retorno, "executarRequestStone|response|mock");
                return retorno;
            } else {
                RequestHttpService service = new RequestHttpService();
                Uteis.logarDebug("Vou tentar cobrar a transação de código: " + transacaoVO.getCodigo()  + " | StoneCode: " +
                        transacaoVO.getConvenioCobrancaVO() != null ? transacaoVO.getConvenioCobrancaVO().getCodigoAutenticacao01() : "");
                service.connectTimeout = 60000; // 60 segundos (tempo sugerido pelo time da Stone para a Pacto)
                inicio = System.currentTimeMillis();
                respostaHttpDTO = service.executeRequest(fullUrlRequest, headers, null, xmlBody, MetodoHttpEnum.POST);
                tempoDecorrido = System.currentTimeMillis() - inicio;

                statusServer = respostaHttpDTO != null && !UteisValidacao.emptyNumber(respostaHttpDTO.getHttpStatus()) ? respostaHttpDTO.getHttpStatus() : 0;
                Uteis.logarDebug("Tempo de resposta da Stone: " + tempoDecorrido + "ms" + " | statusServer: " + statusServer);
                incluirHistoricoRetornoTransacao(transacaoVO, new JSONObject(respostaHttpDTO).toString(), "executarRequestStone|response", statusServer, tempoDecorrido);

                return respostaHttpDTO.getResponse();
            }
        } catch (Exception e) {
            tempoDecorrido = System.currentTimeMillis() - inicio;
            e.printStackTrace();

            if (deuErroNaRequisicaoDoMock) {
                Uteis.logarDebug("Erro na Requisição para o servidor Mock: " + e.getMessage());
                incluirHistoricoRetornoTransacao(transacaoVO, e.getMessage(), "executarRequestStone|erro|requisicaoMock", statusServer, tempoDecorrido);
                throw new RequestException(e.getMessage());
            }

            String erroCompleto = "";
            try {
                String getMessage = UteisValidacao.emptyString(e.getMessage()) ? " " : e.getMessage();
                String getCause = e.getCause() != null && !UteisValidacao.emptyString(e.getCause().toString()) ? e.getCause().toString() : "";
                String getCauseMessage = e.getCause() != null && !UteisValidacao.emptyString(e.getCause().getMessage()) ? e.getCause().getMessage() : "";
                erroCompleto = "e.getMessage: " + getMessage + " | e.getCause(): " + getCause + " | e.getCause().getMessage(): " + getCauseMessage;
            } catch (Exception ex) {
            }

            Uteis.logarDebug("Erro na Requisição HTTP para a Stone: " + erroCompleto);

            try {
                statusServer = respostaHttpDTO != null && !UteisValidacao.emptyNumber(respostaHttpDTO.getHttpStatus()) ? respostaHttpDTO.getHttpStatus() : 0;
                Uteis.logarDebug("Tempo decorrido na tentativa da requisição: " + tempoDecorrido + "ms" + " | statusServer: " + statusServer);
                incluirHistoricoRetornoTransacao(transacaoVO, erroCompleto, "executarRequestStone|erro", statusServer, tempoDecorrido);
            } catch (Exception e1) {
                e1.printStackTrace();
                Uteis.logarDebug("ERRO | executarRequestStone|erro2 | " + e1.getMessage());
            }
            throw new StoneErroComunicacaoAPIException(e);
        }
    }

    @Override
    public TransacaoVO descartarTransacao(TransacaoVO transacao) throws Exception {
        transacao.setSituacao(SituacaoTransacaoEnum.DESCARTADA);
        transacaoFacade.alterar(transacao);
        return transacao;
    }

    private void logarRejeicao(AbstractStoneElementXML documentResponse) {
        Uteis.logar("Transação rejeitada, retorno da Rejeição da Stone: " + documentResponse.toRejection().getInformacaoAdicionalRejeicao());
    }

    /**
     * Captura automática, veja em: {@link TransactionTx#getTransactionCaptureTxCaptr()}.
     */
    @Override
    public TransacaoVO confirmarTransacao(TransacaoVO transacao, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        // Não implementado, uma vez que a captura é imediata, no momento do pedido da autorização
        throw new StoneException("Não disponibilizado/necessário para Stone.");
    }

    @Override
    public TransacaoVO retransmitirTransacao(TransacaoVO transacao, ContratoRecorrenciaVO contratoRecorrencia, ClienteVO cliente) throws Exception {
        // Não implementado, uma vez que não é permitido mais retransmitir depois do ticket #15912
        throw new StoneException("Não disponibilizado/necessário para Stone.");
    }

    private void notificarRecursoEmpresaStone(RecursoSistema recursoSistema) {
    /*
     * Este recurso foi temporariamente desabilitado, pois depende do ticket #17084 para ser liberado.
     *
     *  if (JSFUtilities.isJSFContext()) {
     *     EnfileiradorNotificadorRecursoSistemaSingleton.getInstance().enfileirarNotificacaoRecursoEmpresa(
     *             recursoSistema,
     *             usuarioLogado.getAdministrador(),
     *             PropsService.isTrue(PropsService.usarUrlRecursoEmpresa),
     *             codigoEmpresa,
     *             usuarioLogado.getUsername(),
     *             "",
     *             "",
     *             "",
     *             "",
     *             FacesContext.getCurrentInstance()
     *     );
     * } else {
     *     if (isNotBlank(RemessaService.CHAVE_EMPRESA_REMESSA)) {
     *         NotificadorRecursoSistemaAssincronoSingleton.getInstance().notificarRecursoEmpresaAssincrono(
     *                 RemessaService.CHAVE_EMPRESA_REMESSA,
     *                 recursoSistema,
     *                 codigoEmpresa,
     *                 RemessaService.class.getSimpleName() + " - " + StoneOnlineService.class.getSimpleName(),
     *                 "",
     *                 "",
     *                 "",
     *                 ""
     *         );
     *     }
     * }
     * */
    }

    public void consultarSituacaoCobrancaTransacao(TransacaoVO transacaoVO) throws Exception {
        String resposta = "";
        try {
            String sak = obterSAK(transacaoVO);
            if (UteisValidacao.emptyString(sak)) {
                sak = transacaoVO.getConvenioCobrancaVO().getCodigoAutenticacao02();
            }
            if (UteisValidacao.emptyString(sak) && !UteisValidacao.emptyNumber(transacaoVO.getConvenioCobrancaVO().getCodigo())) {
                ConvenioCobrancaVO convenioCobrancaVO = convenioCobrancaDAO.consultarPorChavePrimaria(transacaoVO.getConvenioCobrancaVO().getCodigo(), Uteis.NIVELMONTARDADOS_DADOSBASICOS);
                sak = convenioCobrancaVO.getCodigoAutenticacao02();
            }
            String idTransacao = transacaoVO.getTID();
            if (UteisValidacao.emptyString(idTransacao)) {
                idTransacao = obterValorParametroXML(transacaoVO.getParamsEnvio(),"TxRef");
            }

            if (UteisValidacao.emptyString(idTransacao)) {
                idTransacao = transacaoVO.getCodigoExterno();
            }

            if (UteisValidacao.emptyString(sak)) {
                throw new Exception("SAK não identificado");
            }

            if (UteisValidacao.emptyString(idTransacao)) {
                throw new Exception("Identificador não encontrado");
            }

            StringBuilder xmlConsulta = new StringBuilder();
            xmlConsulta.append("<Document xmlns=\"urn:AcceptorTransactionStatusReportRequestV02.1\">");
            xmlConsulta.append("	<AccptrTxStsRptRq>");
            xmlConsulta.append("		<Hdr>");
            xmlConsulta.append("			<MsgFctn>TSRR</MsgFctn>");
            xmlConsulta.append("			<PrtcolVrsn>2.0</PrtcolVrsn>");
            xmlConsulta.append("			<InitgPty>");
            xmlConsulta.append("				<Id>").append(sak).append("</Id>");
            xmlConsulta.append("			</InitgPty>");
            xmlConsulta.append("		</Hdr>");
            xmlConsulta.append("		<TxStsRpt>");
            xmlConsulta.append("			<Tx>");
            xmlConsulta.append("            <TxRpt>OPRS</TxRpt>");
            xmlConsulta.append("            <TxRpt>SUMM</TxRpt>");
            xmlConsulta.append("				<OrgnlTx>");
            xmlConsulta.append("					<InitrTxId>").append(idTransacao).append("</InitrTxId>");
            xmlConsulta.append("				</OrgnlTx>");
            xmlConsulta.append("			</Tx>");
            xmlConsulta.append("		</TxStsRpt>");
            xmlConsulta.append("	</AccptrTxStsRptRq>");
            xmlConsulta.append("</Document>");

            resposta = executarRequestStone(transacaoVO, xmlConsulta.toString(), STATUS_REPORT_ENDPOINT);
            processarRetornoConsultaTransacao(transacaoVO, resposta);
        } catch (StoneErroComunicacaoAPIException ex) {
            ex.printStackTrace();
            incluirHistoricoRetornoTransacao(transacaoVO, ex.getMessage(), "consultarSituacaoCobrancaTransacao-StoneErro1");
            if (!UteisValidacao.emptyString(resposta)) {
                incluirHistoricoRetornoTransacao(transacaoVO, resposta, "consultarSituacaoCobrancaTransacao-StoneErro2");
            }
            throw ex;
        } catch (Exception e2) {
            e2.printStackTrace();
            incluirHistoricoRetornoTransacao(transacaoVO, e2.getMessage(), "consultarSituacaoCobrancaTransacao-Exception1");
            if (!UteisValidacao.emptyString(resposta)) {
                incluirHistoricoRetornoTransacao(transacaoVO, resposta, "consultarSituacaoCobrancaTransacao-Exception2");
            }
            throw e2;
        }
    }

    private void processarRetornoConsultaTransacao(TransacaoVO transacaoVO, String retornoXML) {
        incluirHistoricoRetornoTransacao(transacaoVO, retornoXML, "processarRetornoConsultaTransacao");
        if (UteisValidacao.emptyString(transacaoVO.getParamsResposta())) {
            transacaoVO.setParamsResposta(retornoXML);
        }
        if (UteisValidacao.emptyString(transacaoVO.getCodigoExterno())) {
            transacaoVO.setCodigoExterno(obterValorParametroXML(retornoXML,"RcptTxId"));
        }

        boolean foiCobrado = retornoXML.toUpperCase().contains("<AuthrsdSts>FULL</AuthrsdSts>".toUpperCase());
        boolean foiNegado = (retornoXML.toUpperCase().contains("<AuthrsdSts>NONE</AuthrsdSts>".toUpperCase()) && retornoXML.toUpperCase().contains("<Rspn>DECL</Rspn>".toUpperCase()));
        boolean foiCancelado = retornoXML.toUpperCase().contains("<CancSts>FULL</CancSts>".toUpperCase());
        boolean naoTemSumary = (!retornoXML.toUpperCase().contains("<Summry>".toUpperCase()) &&
                !retornoXML.toUpperCase().contains("<Oprs>".toUpperCase()) &&
                !retornoXML.toUpperCase().contains("<Authstn>".toUpperCase()));

        if (foiCobrado && !foiCancelado) {
            transacaoVO.setSituacao(SituacaoTransacaoEnum.CONCLUIDA_COM_SUCESSO);
            transacaoVO.setCodigoAutorizacao(obterValorParametroXML(retornoXML,"AuthstnCd"));
            transacaoVO.setCodigoExterno(obterValorParametroXML(retornoXML,"RcptTxId"));
            transacaoVO.setNrVezes(Integer.parseInt(obterValorParametroXML(transacaoVO.getParamsEnvio(),"TtlNbOfPmts")));
        } else if (foiCancelado) {
            transacaoVO.setSituacao(SituacaoTransacaoEnum.CANCELADA);
        } else if (foiNegado || naoTemSumary) {
            transacaoVO.setSituacao(SituacaoTransacaoEnum.NAO_APROVADA);
        }
    }

    private String obterValorParametroXML(String xml, String parametro) {
        if (!UteisValidacao.emptyString(xml) && xml.contains("<"+parametro+">")) {
            try {
                String[] split = xml.split("<"+parametro+">");
                String[] split2 = split[1].split("</"+parametro+">");
                String valor = split2[0];
                if (!UteisValidacao.emptyString(valor)) {
                    return valor;
                }
            } catch (Exception ignored) {
            }
        }
        return "";
    }
}
