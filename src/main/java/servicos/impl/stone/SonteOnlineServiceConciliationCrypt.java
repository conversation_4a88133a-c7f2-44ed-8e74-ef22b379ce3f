package servicos.impl.stone;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

/**
 * Utilizado para gerar a chave de criptografia para comunicação com a API <b>Conciliação Stone</b>.
 *
 * <AUTHOR>
 * @see <a href="https://conciliacao.stone.com.br/v2.2/reference#gerando-seu-token-de-acesso">Stone Online (API de Conciliação)</a>
 * @since 10/04/2019
 */
public class SonteOnlineServiceConciliationCrypt {

    /**
     * Gera criptografia para token de acesso com a API de Conciliação da Stone.<br/>
     * A Signature é um HMAC gerado com algoritmo SHA-512. Deve-se utilizar como String de codificação o ClientEncryptionString juntamente do SecretKey gerado pela Stone.
     *
     * @param text Chave livre.
     * @param key  Chave gerada pela Stone.
     * @return Signature criptografada para comunicação com a API de Conciliação da Stone.
     */
    public static String encryptSha512Hmac(String text, String key) throws UnsupportedEncodingException, NoSuchAlgorithmException, InvalidKeyException {

        Mac sha512_HMAC = null;
        String result = null;

        final String HMAC_SHA512 = "HmacSHA512";

        byte[] byteKey = key.getBytes("UTF-8");
        sha512_HMAC = Mac.getInstance(HMAC_SHA512);
        SecretKeySpec keySpec = new SecretKeySpec(byteKey, HMAC_SHA512);
        sha512_HMAC.init(keySpec);
        byte[] mac_data = sha512_HMAC.doFinal(text.getBytes("UTF-8"));
        //result = Base64.encode(mac_data);
        result = toHexFormat(mac_data);

        return result;
    }

    private static String toHexFormat(byte[] bytes) {
        final StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }

        return sb.toString();
    }
}